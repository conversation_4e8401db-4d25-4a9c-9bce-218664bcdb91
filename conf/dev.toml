[Common]
  Environment = "dev"
  ServeAddr = ":20080"
  PProfAddr = ":9090"
  Debug = true
  Domain = ["*"]
  CsrfCookieDomain = "localhost"
  StaffCacheTime = 30
  AuthTokenTTL = 300

[Mysql]
  [Mysql.bpm]
    [Mysql.bpm.RW]
      DataSourceName = "7YewoGCkqI0Zn/OOLcv47y0YtK4mj2002UL896GGWH5nyPOlzjMVUwr5vcxmv/l9pAOU7RBwQ9f+CAg+DTLxA8HEw0EWnz75Avs8+uiAtOmPpq5P4tLFd8kEBr9GcB14"
      MaxIdleConns = 50
      MaxOpenConns = 100
      MaxRetryTimes = 1
    [Mysql.bpm.R]
      DataSourceName = "7YewoGCkqI0Zn/OOLcv47y0YtK4mj2002UL896GGWH5nyPOlzjMVUwr5vcxmv/l9pAOU7RBwQ9f+CAg+DTLxA8HEw0EWnz75Avs8+uiAtOmPpq5P4tLFd8kEBr9GcB14"
      MaxIdleConns = 50
      MaxOpenConns = 100
      MaxRetryTimes = 1

[Redis]
  [Redis.default]
    Servers = ["127.0.0.1:6379"]
    MaxRetries = -1

[Bpm]
  ToBAddr = "http://localhost:8081/"
  ToCAddr = "http://localhost:8080/"
  PulseInterval = 5
  WaitInterval = 5
  PulseTimeout = 60
  WaitTimeout = 90
  WaitRetryTimes = 0
  ProcessorChanLen = 10
  ProcessorNum = 10
  ProcessorRetryTimes = 1

[Lark]
  AwsSecretID = "prod/sec_bpm"
  AwsKmsKeyID = "3d93d7d3-95ac-48f1-8f82-dbd726a6c6d9"

[S3]
  Url = "http://s3.ap-northeast-1.amazonaws.com"
  BucketName = "my-buckets-1"
  AccessKey = "ioWp6T0rgxGXhDVeP0l+vbv35zfvL6BUCgdAuPRUO4o="
  SecretKey = "R3Fd0MefvBpjXrGccaToOsr4NAqRiCf6Zu/er8HpA2Bq3scQ5uT25tOepnmk5L7E"
  BlockSize = 5242880
  PathStyle = true

[Email]
  Host = "http://bitex-mc-gateway.test-2.huobiapps.com"
  Url = "/mc/inter/message/send"
  AppId = "5NHdJecRt2SHz0qs1EjnuleMLM06yV6wlhLypH3c5WA="
  AppKey = "5PQI9YM7pJMYDp047RyUGJramfgjHV5Zn4k1gqYa4A3JrMWk12y8H6FcofAQn9jQ"
  SendStrategy = "EMAIL_ONLY"
  ExchangeId = 1
  Scene = "NOTICE"
  BusinessType = "IAM"
  Version = 2
  EmailTitle = ""

[Cas]
  EndPoint = "http://localhost"
  FrontDomain = "http://localhost"
  ClientID = "GGNH7qXnxPY0TFkE9nVNBQSaxl1xFbMQjak0c+4GkQ4="
  ClientSecret = "MocNcSWJjxSWJQNDS+041x/4k/oZDXQM/6nwlysODHHksYKpWLsIsF1ZRIaSEDyH"
  Cert = "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"
  EnforcerID = ""
  OrgName = "built-in"
  AppName = "fuse"
  AppDomain = "http://localhost:8080"
  AppBackDomain = "http://localhost:20080"
  LogoutPath = "/cas/logout"
  CallbackPath = "/cas/c_back"
  NoValidateSSOPath = ["/"]
  NoValidateIAMPath = ["/"]
  CookieKey = "_q"
  SessionKey = "bpm_session_%s"
  SessionTTL = 86400

[Log]
  LogLevel = "debug"
  LogFile = "/Users/<USER>/logs/bpm.log"
  KafkaLog = false
  LocalLog = true
  [Log.Kafka]
    Servers = ["127.0.0.1:9092"]
    Topic = "bpm-log"
    InjectHostname = true
    App = "bpm"
    AppName = "bpm-dev"
    EnvName = "dev"
  [Log.Rotate]
    Interval = 24
    MaxAge = 30
    MaxSize = 15360
    LocalTime = true
