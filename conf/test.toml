[Common]
  Environment = "test"
  ServeAddr = ":20080"
  PProfAddr = ":9090"
  Debug = true
  Domain = ["*"]
  CsrfCookieDomain = "sec-test.yorkapp.com"
  StaffCacheTime = 30
  AuthTokenTTL = 300

[Mysql]
  [Mysql.bpm]
    [Mysql.bpm.RW]
      DataSourceName = "EktsfwIvOPHcIoozjyS1xJp1SYIl7en/yqegkiZP5r7QAzfO1zPmndOY9Z44SSLYKOIJwrfQh26VOkE+0vIlNgTxttN4JQ9KPrIKv85ROxJBxc1FfVcKPrXW0Mal1PAEqXI9x/BdweMmTZABEpnIvwyeK5uw0QvSO0ImXX+1Jh5ZpP+PjiKOx7TOSoJzR6mU6PBOLUVOkW2g8YH7OLh4U+Pj4E3w9JPhUl4VuPQS1Nc="
      MaxIdleConns = 50
      MaxOpenConns = 100
      MaxRetryTimes = 1
    [Mysql.bpm.R]
      DataSourceName = "EktsfwIvOPHcIoozjyS1xJp1SYIl7en/yqegkiZP5r7QAzfO1zPmndOY9Z44SSLYKOIJwrfQh26VOkE+0vIlNgTxttN4JQ9KPrIKv85ROxJBxc1FfVcKPrXW0Mal1PAEqXI9x/BdweMmTZABEpnIvwyeK5uw0QvSO0ImXX+1Jh5ZpP+PjiKOx7TOSoJzR6mU6PBOLUVOkW2g8YH7OLh4U+Pj4E3w9JPhUl4VuPQS1Nc="
      MaxIdleConns = 50
      MaxOpenConns = 100
      MaxRetryTimes = 1

[Redis]
  [Redis.default]
    Servers = ["10.6.0.219:6379"]
    MaxRetries = -1

[Bpm]
  ToBAddr = "https://bpm.sec-test.yorkapp.com/b/"
  ToCAddr = "https://bpm.sec-test.yorkapp.com/c/"
  PulseInterval = 5
  WaitInterval = 5
  PulseTimeout = 60
  WaitTimeout = 90
  WaitRetryTimes = 0
  ProcessorChanLen = 10
  ProcessorNum = 10
  ProcessorRetryTimes = 1

[Lark]
  AwsSecretID = "prod/sec_bpm"
  AwsKmsKeyID = "3d93d7d3-95ac-48f1-8f82-dbd726a6c6d9"

[Aws]
  Region = "ap-northeast-1"

[S3]
  Url = "http://s3.ap-northeast-1.amazonaws.com"
  BucketName = "my-buckets-1"
  AccessKey = "ioWp6T0rgxGXhDVeP0l+vbv35zfvL6BUCgdAuPRUO4o="
  SecretKey = "R3Fd0MefvBpjXrGccaToOsr4NAqRiCf6Zu/er8HpA2Bq3scQ5uT25tOepnmk5L7E"
  BlockSize = 5242880
  PathStyle = true

[Email]
  Host = "http://bitex-mc-gateway.test-2.huobiapps.com"
  Url = "/mc/inter/message/send"
  AppId = "5NHdJecRt2SHz0qs1EjnuleMLM06yV6wlhLypH3c5WA="
  AppKey = "5PQI9YM7pJMYDp047RyUGJramfgjHV5Zn4k1gqYa4A3JrMWk12y8H6FcofAQn9jQ"
  SendStrategy = "EMAIL_ONLY"
  ExchangeId = 1
  Scene = "NOTICE"
  BusinessType = "IAM"
  Version = 2
  EmailTitle = ""

[Cas]
  EndPoint = "https://sso.sec-test.yorkapp.com"
  FrontDomain = "https://sso.sec-test.yorkapp.com"
  ClientID = "fVRdi9vPD4KQ02VS/nd+DVtP9lY8OztcS4rszBbM23w="
  ClientSecret = "4pYQrXEFD3xAvBLdmvvZQabOejqllIdzB80hTAHqP7SmVRyoQaiQoAr871utvCDU"
  Cert = "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"
  OrgName = "Avenir"
  AppName = "Bpm-test"
  AppDomain = "https://bpm.sec-test.yorkapp.com"
  AppBackDomain = "https://bpm-api.sec-test.yorkapp.com"
  LogoutPath = "/cas/logout"
  CallbackPath = "/cas/c_back"
  NoValidateSSOPath = ["/"]
  NoValidateIAMPath = ["/"]
  CookieKey = "_q"
  SessionKey = "bpm_session_%s"
  SessionTTL = 86400

[Log]
  LogLevel = "debug"
  LogFile = "/root/logs/bpm.log"
  KafkaLog = false
  LocalLog = true
  [Log.Kafka]
    Servers = ["127.0.0.1:9092"]
    Topic = "bpm-log"
    InjectHostname = true
    App = "bpm"
    AppName = "bpm-test"
    EnvName = "test"
  [Log.Rotate]
    Interval = 24
    MaxAge = 30
    MaxSize = 15360
    LocalTime = true
