/**
 * @note
 * hbpm—engine的入口函数
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package main

import (
	"flag"
	"fmt"
	larkHandler "gitlab.docsl.com/security/bpm/internal/handler/lark"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"gitlab.docsl.com/security/common/masker"
	"log"
	"math/rand"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tmsong/hlog"

	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/internal/handler/app_auth"
	"gitlab.docsl.com/security/bpm/internal/handler/file"
	"gitlab.docsl.com/security/bpm/internal/handler/inter"
	"gitlab.docsl.com/security/bpm/internal/handler/process"
	"gitlab.docsl.com/security/bpm/internal/handler/proxy"
	"gitlab.docsl.com/security/bpm/internal/handler/staff"
	"gitlab.docsl.com/security/bpm/internal/handler/tag"
	"gitlab.docsl.com/security/bpm/internal/handler/user"
	"gitlab.docsl.com/security/bpm/internal/handler/user_group"
	"gitlab.docsl.com/security/bpm/internal/handler/workflow/back"
	"gitlab.docsl.com/security/bpm/internal/handler/workflow/front"
	bpmMiddle "gitlab.docsl.com/security/bpm/internal/middleware"
	"gitlab.docsl.com/security/bpm/internal/srvs/workflow_pull"
	"gitlab.docsl.com/security/bpm/internal/srvs/workflow_service"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/config"
	"gitlab.docsl.com/security/bpm/pkg/middleware"
	"gitlab.docsl.com/security/common/logger"
)

/**
 * @note
 * 环境初始化
 */
func init() {
	rand.Seed(time.Now().UTC().UnixNano())
}

/**
 * @note
 * 运行选项
 */
func Flagset() *flag.FlagSet {
	flagSet := flag.NewFlagSet(common.ModuleName, flag.ExitOnError)
	flagSet.String("config", "", "path to config file")
	flagSet.String("logfile", "", "log output file")
	flagSet.String("outputConfig", "", "output config file")
	flagSet.String("encrypt", "", "encrypt config file")

	return flagSet
}

/**
 * @note
 * 主逻辑开始
 */
func main() {

	masker.SetGetKeyFunc(func(arg string) []byte {
		return []byte(os.Getenv("BPM_CONFIG_CRYPT_KEY"))
	})

	//读取运行参数
	flagSet := Flagset()
	flagSet.Parse(os.Args[1:])

	//读取配置文件
	outputConfig := flagSet.Lookup("outputConfig").Value.String()
	encrypt := flagSet.Lookup("encrypt").Value.String() == "true"
	configFile := flagSet.Lookup("config").Value.String()
	if configFile != "" {
		configFile, err := config.ReplaceAndLoad(configFile, !encrypt)
		if err != nil {
			log.Fatalf("ERROR: failed to load config file %s - %s\n", configFile, err)
		}
	} else {
		log.Fatalln("ERROR: must set config file")
	}

	// 这里用来生成加密或解密配置文件，平时用不到
	if outputConfig != common.StringEmpty {
		if err := config.OutputConfigToFile(outputConfig, encrypt); err != nil {
			panic(err)
		}
		return
	}

	//取日志配置
	logFile := flagSet.Lookup("logfile").Value.String()
	if logFile != common.StringEmpty {
		logger.SetLoggerFile(logFile)
	}

	//崩溃恢复打印
	defer func() {
		if err := recover(); err != nil {
			log.Printf("ERROR: abort, unknown error, reason:%v,\n stack:%s\n", err, common.IdentifyPanic())
			fmt.Printf("ERROR: abort, unknown error, reason:%v", err)
		}
	}()

	//初始化日志组件&ctx
	cfg := common.GetConfig()
	loggerConfig := logger.GetHlogConfig()
	l := hlog.NewLoggerWithConfig(loggerConfig, 0)
	hctx := common.NewContext(l, nil)

	//Pprof
	if cfg.Debug {
		common.StartPProf(cfg.PProfAddr)
		log.Println("start pprof, addr:", cfg.PProfAddr)
	}

	////////////////////HTTP SERVER////////////////////
	//gin engine init
	engine := middleware.Custom(l, common.GetConfig().ServeAddr)

	//注册接口
	toC := engine.Group("/c")
	toB := engine.Group("/b")
	cas := engine.Group("/cas")
	internal := engine.Group("/i")
	lark := engine.Group("/lark")

	//flag
	toC.Use(common.SetAppFlag(common.ToC))
	toB.Use(common.SetAppFlag(common.ToB))
	cas.Use(common.SetAppFlag(common.Cas))
	internal.Use(common.SetAppFlag(common.Inter))

	//validate
	toC.Use(bpmMiddle.Validate())
	toC.Use(middleware.ValidateCsrf())

	toB.Use(bpmMiddle.Validate())
	toB.Use(middleware.ValidateCsrf())

	cas.Use(bpmMiddle.Validate())

	internal.Use(bpmMiddle.ValidateSignature())

	//lark的callback接口
	WrapLarkCallbackControllers(lark)

	//cas的callback接口，防止回调问题
	WrapCasControllers(cas)

	//内部接口
	WrapInternalControllers(internal)
	WrapAuthAppControllers(internal) //内部自己调用

	//C端
	WrapWorkflowRuntimeControllers(toC)  //注册工作流相关接口
	WrapProcessControllers(toC)          //注册任务相关接口
	WrapTemplateGroupToCControllers(toC) //C端模板组查看接口
	WrapUserInfoControllers(toC)         //用户信息/权限/菜单获取
	WrapProxyControllers(toC)            //转发接口
	WrapToCStaffInfoControllers(toC)     //用户信息查询接口
	WrapUserGroupToCControllers(toC)     //用户组查询接口
	WrapToCTagControllers(toC)           //tag相关接口

	if !common.IsProd() {
		WrapTestControllers(toC) //测试环境加点测试用接口
	}

	//B端
	WrapUserGroupToBControllers(toB)        //注册用户组相关接口
	WrapWorkFlowTemplateToBControllers(toB) //注册WorkFlowTemplate相关接口
	WrapTemplateGroupToBControllers(toB)    //注册WorkFlowTemplateGroup相关接口
	WrapToBStaffInfoControllers(toB)        //注册用户、部门信息查询接口
	WrapUserInfoControllers(toB)            //用户信息/权限/菜单获取
	WrapProxyControllers(toB)               //转发接口
	WrapAuthAppControllers(toB)             //app加签信息接口
	WrapWorkflowArchiveControllers(toB)     //流程归档接口
	WrapToBTagControllers(toB)              //tag相关接口

	//start service
	l.Infoln("service start")
	hctx.WaitGroup().Wrap(func() {
		engine.Run()
	})

	////////////////////WORKFLOW SERVICE////////////////////
	//init workflow process chan
	bpmCom.WorkflowProcessChan = make(chan *bpmCom.WorkflowProcessMsg, bpmCom.GetConfig().ProcessorChanLen)

	//init workflow processor
	workflowProcessor := workflow_service.NewWorkflowProcesser(hctx)

	//run
	hctx.WaitGroup().Wrap(func() {
		workflowProcessor.Run()
	})

	//init workflow puller
	workflowPuller := workflow_pull.NewWorkflowPuller(hctx)

	//run
	hctx.WaitGroup().Wrap(func() {
		workflowPuller.Pull()
	})

	////////////////////EXIT CONTROL////////////////////
	//日志实例处理
	hctx.WaitGroup().Wrap(func() {
		<-common.QuitChan
		l.Close()
		fmt.Println("logger closed")
	})

	//优雅退出
	middleware.GracefulExit(engine)

	//等待全部退出
	hctx.WaitGroup().Wait()

	//退出pprof
	if common.GetConfig().Debug {
		common.StopPProf()
	}
	fmt.Println("main closed")
}

func WrapCasControllers(engine *gin.RouterGroup) {
	engine.Any("/c_back", common.HandleJumpTo(bpmCom.GetConfig().ToCAddr, bpmCom.GetConfig().ToBAddr))
	engine.GET("/dingAuth", common.AlwaysReturnOK)
	engine.GET("/logout", func(ctx *gin.Context) {})
	//种csrfToken的cookie
	engine.GET("/csrf", middleware.SetCsrf)
}

func WrapWorkFlowTemplateToBControllers(engine *gin.RouterGroup) {
	engine.POST("/workflow/template/enable", back.EnableWorkFlowTemplate)
	engine.POST("/workflow/template/disable", back.DisableWorkFlowTemplate)
	engine.POST("/workflow/template/tag_edit", back.EditWorkflowTag)
	engine.POST("/workflow/template/edit", back.EditWorkFlowTemplate)
	engine.POST("/workflow/template/list", back.ListWorkFlowTemplate)
	engine.GET("/workflow/template/detail", back.GetWorkFlowTemplateDetail)
	engine.POST("/workflow/template/delete", back.DeleteWorkFlowTemplate)
	engine.POST("/workflow/template/validateCondition", back.ValidateCondition)
}

func WrapTemplateGroupToBControllers(engine *gin.RouterGroup) {
	engine.POST("/workflow/template/group/create", back.CreateTemplateGroup)
	engine.POST("/workflow/template/group/enable", back.EnableTemplateGroup)
	engine.POST("/workflow/template/group/disable", back.DisableTemplateGroup)
	engine.POST("/workflow/template/group/list", back.ListTemplateGroup)
	engine.GET("/workflow/template/group/detail", back.GetTemplateGroupDetail)
	engine.POST("/workflow/template/group/update", back.UpdateTemplateGroup)
	engine.POST("/workflow/template/group/delete", back.DeleteTemplateGroup)
}

func WrapTemplateGroupToCControllers(engine *gin.RouterGroup) {
	engine.GET("/workflow/template/group/list", front.ListTemplateGroupForC)
	engine.POST("/workflow/template/group/list", front.ListTemplateGroupForC)
	engine.GET("/workflow/template/group/detail", back.GetTemplateGroupDetail)
}

func WrapProcessControllers(engine *gin.RouterGroup) {
	engine.POST("/process/list", process.ListProcess)
	engine.POST("/process/group_query", process.GroupQueryProcess)
	engine.POST("/process/update", process.UpdateProcess)
	engine.GET("/process/detail", process.ProcessDetail)
	engine.GET("/process/todo/count", process.GetTodoCount)
	//BPM V2
	engine.POST("/process/handover", process.HandoverProcess)
	engine.POST("/process/countersign", process.CountersignProcess)
	engine.POST("/process/remark", process.RemarkProcess)
	engine.POST("/process/rollback", process.RollbackProcess)
	engine.POST("/process/needComplete", process.NeedCompleteProcess)
	engine.POST("/process/markAllNotified", process.MarkAllNotified)
}

func WrapWorkflowRuntimeControllers(engine *gin.RouterGroup) {
	engine.GET("/workflow/template/list", front.ListAllWorkflowTemplate)
	engine.GET("/workflow/template/detail", front.WorkflowTemplateDetail)
	engine.POST("/workflow/create", front.CreateWorkflow)
	engine.POST("/workflow/prehandle", front.PreHandleWorkflow)
	engine.POST("/workflow/list", front.ListWorkFlow)
	engine.GET("/workflow/detail", front.GetWorkflowDetail)
	engine.POST("/workflow/withdraw", front.WithdrawWorkFlow)
	engine.POST("/workflow/urge", front.UrgeWorkflow)
	engine.POST("/workflow/recreate", front.ReCreateWorkflow)
	engine.POST("/workflow/complete", front.CompleteWorkflow)

	//文件上传相关
	engine.POST("/file/upload", file.UploadFile)
	engine.GET("/file/getPreSignUrl", file.GetPreSignUrl)
}

func WrapWorkflowArchiveControllers(engine *gin.RouterGroup) {
	engine.POST("/workflow/archive/list", back.ListWorkFlowArchive)
	engine.GET("/workflow/archive/detail", back.GetWorkflowArchiveDetail)
	engine.POST("/workflow/archive/handover", back.WorkflowArchiveHandover)
	engine.POST("/workflow/archive/rollback", back.WorkflowArchiveRollback)
}

func WrapUserGroupToBControllers(engine *gin.RouterGroup) {
	engine.POST("/userGroup/list", user_group.ListUserGroup)
	engine.POST("/userGroup/create", user_group.CreateUserGroup)
	engine.POST("/userGroup/delete", user_group.DeleteUserGroup)
	engine.GET("/userGroup/detail", user_group.GetUserGroupDetail)
	engine.POST("/userGroup/update", user_group.UpdateUserGroupInfo)

	engine.POST("/userGroup/user/add", user_group.AddUser)
	engine.POST("/userGroup/user/delete", user_group.DeleteUser)
}

func WrapUserGroupToCControllers(engine *gin.RouterGroup) {
	engine.POST("/userGroup/list", user_group.ListUserGroup)
	engine.GET("/userGroup/detail", user_group.GetUserGroupDetail)

}

func WrapUserInfoControllers(engine *gin.RouterGroup) {
	engine.GET("/user/info", user.GetUserInfo)
	engine.GET("/user/permissions", user.GetUserPermission)
}

func WrapToBStaffInfoControllers(engine *gin.RouterGroup) {
	engine.GET("/staff/user/info", staff.StaffUserInfo)
	engine.GET("/staff/department/info", staff.DepartmentInfo)
	engine.GET("/staff/department/users", staff.DepartmentUsers)
	engine.GET("/staff/department/tree", staff.DepartmentTree)
	engine.POST("/staff/user/search", staff.StaffUserSearch)
	engine.GET("/staff/app/availableAppList", staff.StaffAppList)
}

func WrapToBTagControllers(engine *gin.RouterGroup) {
	engine.GET("/tag/list", workflow_tag.ListWorkflowTag)
	engine.POST("/tag/create", workflow_tag.CreateWorkflowTag)
	engine.POST("/tag/delete", workflow_tag.DeleteWorkflowTag)
	engine.POST("/tag/edit", workflow_tag.UpdateWorkflowTag)
}

func WrapToCTagControllers(engine *gin.RouterGroup) {
	engine.GET("/tag/list", workflow_tag.ListWorkflowTag)
}

func WrapToCStaffInfoControllers(engine *gin.RouterGroup) {
	engine.GET("/staff/user/info", staff.StaffUserInfo)
	engine.POST("/staff/user/integrate", staff.StaffIntegratedInfo)
	engine.GET("/staff/department/info", staff.DepartmentInfo)
	engine.GET("/staff/department/users", staff.DepartmentUsers)
	engine.GET("/staff/department/tree", staff.DepartmentTree)
	engine.POST("/staff/user/search", staff.StaffUserSearch)
}

func WrapInternalControllers(engine *gin.RouterGroup) {
	engine.POST("/workflow/create", front.CreateWorkflow)
	engine.POST("/workflow/detail", front.GetWorkflowDetail)
	engine.POST("/workflow/withdraw", front.WithdrawWorkFlow)
	engine.POST("/process/update", process.UpdateProcess)
	engine.POST("/file/upload", file.UploadFile)
	engine.POST("/file/getPreSignUrl", file.GetPreSignUrl)
	engine.POST("/workflow/interact/callback", inter.HandleInteractCallback)
	engine.POST("/auth/token", inter.GenerateAuthToken)
	engine.Any("/workflow/testData", inter.TestL) //TODO DEL 给前端的测试数据接口
}

func WrapTestControllers(engine *gin.RouterGroup) {
	engine.POST("/auth/token", inter.GenerateAuthToken)
}

func WrapAuthAppControllers(engine *gin.RouterGroup) {
	engine.POST("/appAuth/create", app_auth.CreateAppAuth)
	//engine.POST("/appAuth/delete", app_auth.DeleteAppAuth)
	//engine.POST("/appAuth/update", app_auth.UpdateAppAuth)
	//engine.GET("/appAuth/detail", app_auth.GetAuthAppKey)
	engine.GET("/appAuth/list", app_auth.ListAppAuth)
}

func WrapProxyControllers(engine *gin.RouterGroup) {
	engine.POST("/proxy", proxy.Proxy)
}

func WrapLarkCallbackControllers(engine *gin.RouterGroup) {
	engine.POST("/callback", lark.NewEventHandlerFunc(larkHandler.GetLarkEventDispatcher()))
}
