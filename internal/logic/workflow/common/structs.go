/**
 * @note
 * 为了能够编译通过，将runtime和template都需要的结构提出来
 *
 * <AUTHOR>
 * @date 	2020-03-24
 */
package common

import (
	"encoding/json"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

const (
	DATA_HISTORY_KEY = "__dataHistory"
)

type GroupInfoForTmplInfo struct {
	Name                    json.RawMessage `json:"name,omitempty"`
	Description             json.RawMessage `json:"description,omitempty"`
	WorkflowTemplateGroupId int64           `json:"workflowTemplateGroupId"`
}

type DataHistoryInfo struct {
	ProcessUuid string               `json:"processUuid"`
	PrevData    interface{}          `json:"prevData"`
	Data        interface{}          `json:"data"`
	User        *staffLogic.UserInfo `json:"user"`
	OpTime      int64                `json:"opTime"`
}

func ExtractDataHistory(input interface{}) []*DataHistoryInfo {
	if inputMap, ok := input.(map[string]interface{}); ok {
		if historyIface, ok := inputMap[DATA_HISTORY_KEY]; ok {
			ret := make([]*DataHistoryInfo, 0)
			if err := common.InterfaceToStruct(historyIface, &ret); err == nil {
				return ret
			}
		}
	}
	return nil
}

func SetDataHistory(input interface{}, history []*DataHistoryInfo) (output interface{}) {
	if inputMap, ok := input.(map[string]interface{}); ok {
		inputMap[DATA_HISTORY_KEY] = history
		return inputMap
	}
	return input
}

func DeleteDataHistory(input interface{}) (output interface{}) {
	d := func(in interface{}) (o interface{}) {
		if m, ok := in.(map[string]interface{}); ok {
			delete(m, DATA_HISTORY_KEY)
			return m
		}
		return in
	}
	if inputSlice, ok := input.([]interface{}); ok {
		for idx := range inputSlice {
			inputSlice[idx] = d(inputSlice[idx])
		}
		return inputSlice
	} else {
		return d(input)
	}
}
