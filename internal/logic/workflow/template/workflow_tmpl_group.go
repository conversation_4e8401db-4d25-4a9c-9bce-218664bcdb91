package template

import (
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

const (
	FlagWorkflowGroup     string = "workflow_group"
	FlagWorkflowGroupRoot string = "all_workflow_group"
)

func (op *WorkflowTmplOperator) EnableTemplateGroup(templateGroupIds []int64) error {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	err = pModel.EnableTemplateGroup(templateGroupIds)
	if err != nil {
		return err
	}
	return nil
}

func (op *WorkflowTmplOperator) DisableTemplateGroup(templateGroupIds []int64) error {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	err = pModel.DisableTemplateGroup(templateGroupIds)
	if err != nil {
		return err
	}
	return nil
}

func (op *WorkflowTmplOperator) InsertWorkflowTmplGroup(name, description common2.I18nString) (id int64, err error) {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return 0, err
	}

	tmplGroupId, err := pModel.InsertTemplateGroup(name, description, common2.GROUP_STATE_ENABLED, common2.STATUS_NORMAL)

	return tmplGroupId, err
}

func (op *WorkflowTmplOperator) UpdateWorkflowTmplGroupNameAndDesc(groupId int64, name, description common2.I18nString) error {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	return pModel.UpdateTemplateGroupNameAndDesc(groupId, name, description)
}

func (op *WorkflowTmplOperator) UpdateWorkflowTmplGroupTemplates(groupId int64, workflowTmplUuids []string) error {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	return pModel.UpdateTemplateGroupTemplates(groupId, workflowTmplUuids)
}

func (op *WorkflowTmplOperator) ListTemplateGroup(condition WorkflowTemplateGroupListCondition, offset, limit int64) (
	res []*WorkflowTemplateGroupInfo, cnt int64, err error) {

	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, 0, err
	}

	var condGroupIds []int64
	if condition.WorkflowTemplateUuid != common.StringEmpty ||
		condition.WorkflowTemplateState != common.StringEmpty ||
		condition.WorkflowTemplateName != common.StringEmpty {
		m := make(map[string]interface{})
		if condition.WorkflowTemplateUuid != common.StringEmpty {
			m["uuid"] = condition.WorkflowTemplateUuid
		}
		if condition.WorkflowTemplateState != common.StringEmpty {
			m["state"] = condition.WorkflowTemplateState
		}
		condGroupIds, err = wModel.QueryWorkflowTemplateGroupIdsByWhereMap(m, condition.WorkflowTemplateName)
		if err != nil {
			return nil, 0, err
		} else if len(condGroupIds) == 0 {
			return nil, 0, nil
		}
	}

	templateGroupTables, count, err := wModel.QueryTemplateGroupByConditions(condition.WorkflowTemplateGroupName, nil, offset, limit)
	if err != nil {
		return nil, 0, err
	}

	var groupIds []int64
	for _, templateGroupTable := range templateGroupTables {
		groupInfo, err := getWorkflowTemplateGroupInfo(templateGroupTable)
		if err != nil {
			return nil, 0, err
		}

		groupInfo.AllowDetail = true // TODO PERMISSION
		groupInfo.AllowDelete = true
		groupInfo.AllowEdit = true

		res = append(res, groupInfo)
		groupIds = append(groupIds, groupInfo.WorkflowTemplateGroupId)
	}

	cntMap, err := wModel.QueryWorkflowTemplateCntByGroupIds(groupIds, []common2.WorkflowTemplateStateEnum{common2.STATE_ENABLED, common2.STATE_DISABLED})
	if err != nil {
		return nil, 0, err
	}

	for idx := range res {
		res[idx].WorkflowTemplateCount = cntMap[res[idx].WorkflowTemplateGroupId]
	}
	return res, count, nil
}

func getWorkflowTemplateGroupInfo(m *workflowModel.TemplateGroupTable) (groupInfo *WorkflowTemplateGroupInfo, err error) {
	groupInfo = &WorkflowTemplateGroupInfo{
		UserId:                  m.UserId,
		WorkflowTemplateGroupId: m.ID,
		CreateTime:              m.CreateTime,
		UpdateTime:              m.UpdateTime,
	}
	if name, err := common.JsonStringToRaw(m.Name); err != nil {
		return groupInfo, err
	} else if description, err := common.JsonStringToRaw(m.Description); err != nil {
		return groupInfo, err
	} else {
		groupInfo.Name = name
		groupInfo.Description = description
	}
	return groupInfo, nil
}

func (op *WorkflowTmplOperator) DeleteTemplateGroup(workflowTemplateGroupIds []int64) error {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}

	cntMap, err := wModel.QueryWorkflowTemplateCntByGroupIds(workflowTemplateGroupIds,
		[]common2.WorkflowTemplateStateEnum{common2.STATE_ENABLED, common2.STATE_DISABLED, common2.STATE_STAGING})
	if err != nil {
		return err
	}
	for _, groupId := range workflowTemplateGroupIds {
		if cntMap[groupId] > 0 {
			return common2.ErrDelTmplGroupHasTmpls
		}
	}
	err = wModel.DeleteTemplateGroupById(workflowTemplateGroupIds)
	return err
}

func (op *WorkflowTmplOperator) QueryTemplateGroupByGroupIds(workflowTemplateGroupIds []int64) (
	[]*workflowModel.TemplateGroupTable, error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	return wModel.QueryTemplateGroupByGroupIds(workflowTemplateGroupIds)
}

func (op *WorkflowTmplOperator) QueryTemplateGroupByGroupId(workflowTemplateGroupId int64) (
	*workflowModel.TemplateGroupTable, error) {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	return pModel.QueryTemplateGroupByGroupId(workflowTemplateGroupId)
}

func (op *WorkflowTmplOperator) GetTemplateGroupDetailByGroupId(workflowTemplateGroupId int64) (
	*WorkflowTemplateGroupInfo, error) {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	table, err := pModel.QueryTemplateGroupByGroupId(workflowTemplateGroupId)
	if err != nil {
		return nil, err
	}
	return getWorkflowTemplateGroupInfo(table)
}
