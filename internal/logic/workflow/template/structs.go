package template

import (
	"encoding/json"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	baseOp "gitlab.docsl.com/security/bpm/internal/logic/base_operator"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wCommon "gitlab.docsl.com/security/bpm/internal/logic/workflow/common"
	"gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	"unicode/utf8"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

type WorkflowTmplOperator struct {
	*baseOp.BaseOperator
}

func NewWorkflowTmplOperator(hctx common.HContextIface) *WorkflowTmplOperator {
	return &WorkflowTmplOperator{baseOp.NewBaseOperator(hctx)}
}

type TopologyDetailInput struct {
	engine.TopologyDetail
	Next []int
}

type WorkflowTemplateInput struct {
	WorkflowTemplateUuid string `json:"workflowTemplateUuid" validate:"required,len=32"`
	Version              string `json:"version"`
}

type WorkflowTemplateInfo struct {
	Id                    int64                             `json:"-"`
	UserId                int64                             `json:"userId,omitempty"`
	Name                  interface{}                       `json:"name,omitempty"`
	Description           interface{}                       `json:"description,omitempty"`
	Origin                common2.UserOriginEnum            `json:"origin,omitempty"`
	TagIds                []int64                           `json:"-"`
	Edition               string                            `json:"-"`
	Tags                  []*WorkflowTemplateTagInfo        `json:"tags"`
	WorkflowTemplateGroup *wCommon.GroupInfoForTmplInfo     `json:"workflowTemplateGroup,omitempty"`
	WorkflowTemplateState common2.WorkflowTemplateStateEnum `json:"workflowTemplateState,omitempty"`
	WorkflowTemplateUuid  string                            `json:"workflowTemplateUuid"`
	WorkflowTemplateId    int64                             `json:"workflowTemplateId"`
	Version               string                            `json:"version"`
	CreateTime            int64                             `json:"createTime,omitempty"`
	UpdateTime            int64                             `json:"updateTime,omitempty"`

	AllowEdit    bool `json:"allowEdit"`
	AllowEnable  bool `json:"allowEnable"`
	AllowDisable bool `json:"allowDisable"`
	AllowDelete  bool `json:"allowDelete"`
	AllowDetail  bool `json:"allowDetail"`
}

type WorkflowTemplateTagInfo struct {
	Id      int64  `json:"id,omitempty"`
	TagName string `json:"tagName,omitempty"`
}
type NodeTemplateInput struct {
	TemplateUuid   string                    `json:"templateUuid" validate:"required"`
	Name           json.RawMessage           `json:"name"`
	Type           common2.NodeTypeEnum      `json:"type" validate:"required"`
	Detail         json.RawMessage           `json:"detail"`
	FormPermission *formLogic.FormPermission `json:"formPermission"`
	Next           []*NodeTemplateInput      `json:"next"`
}

func (input *NodeTemplateInput) Validate() error {
	if input.Type != common2.NODE_TYPE_EXAMPLE_TASK &&
		input.Type != common2.NODE_TYPE_START &&
		input.Type != common2.NODE_TYPE_APPROVAL &&
		input.Type != common2.NODE_TYPE_TRANSACTOR &&
		input.Type != common2.NODE_TYPE_INTERACT &&
		input.Type != common2.NODE_TYPE_NOTIFY &&
		input.Type != common2.NODE_TYPE_GATEWAY &&
		input.Type != common2.NODE_TYPE_API &&
		input.Type != common2.NODE_TYPE_END {
		return common2.ErrInvalidNodeType
	}
	for _, n := range input.Next {
		if len(n.TemplateUuid) == 0 {
			return common.ErrMissingAMandatoryParameterf("node.templateUuid")
		}
		nameLen := utf8.RuneCount(input.Name)
		if len(input.Name) >= 2 {
			if input.Name[0] == '"' {
				nameLen -= 1
			}
			if input.Name[len(input.Name)-1] == '"' {
				nameLen -= 1
			}
		}
		if nameLen > common.MaxNameLength {
			return common.ErrInvalidFieldf("node.name")
		}
	}
	return common.Validator.Struct(input)
}

func (input *NodeTemplateInput) CollectStaffInfo() []*staffLogic.StaffInfo {
	switch input.Type {
	case common2.NODE_TYPE_APPROVAL:
		d := engine.ApprovalTaskDetail{}
		b, _ := input.Detail.MarshalJSON()
		err := common.JsonDecode(b, &d)
		if err == nil {
			return append(d.Staff, d.EmptyStaff...)
		}
	case common2.NODE_TYPE_TRANSACTOR:
		d := engine.TransactorTaskDetail{}
		b, _ := input.Detail.MarshalJSON()
		if err := common.JsonDecode(b, &d); err == nil {
			return append(d.Staff, d.EmptyStaff...)
		}
	case common2.NODE_TYPE_NOTIFY:
		d := engine.NotifyTaskDetail{}
		b, _ := input.Detail.MarshalJSON()
		err := common.JsonDecode(b, &d)
		if err == nil {
			return d.Staff
		}
	}
	return nil
}

type WorkflowTemplateListCondition struct {
	UserId                   int64    `json:"userId"`
	TagIds                   []int64  `json:"tagIds"`
	NoneTag                  bool     `json:"noneTag"`
	WorkflowTemplateName     string   `json:"workflowTemplateName"`
	WorkflowTemplateGroupId  int64    `json:"workflowTemplateGroupId"`
	WorkflowTemplateState    string   `json:"workflowTemplateState"`
	WorkflowTemplateGroupIds []int64  `json:"workflowTemplateGroupIds"`
	WorkflowTemplateIds      []int64  `json:"workflowTemplateIds"`
	WorkflowTemplateUuids    []string `json:"workflowTemplateUuids"`
}

type WorkflowTemplateDetail struct {
	UserId                int64                             `json:"userId,omitempty"`
	WorkflowTemplateUuid  string                            `json:"workflowTemplateUuid"`
	WorkflowTemplateId    int64                             `json:"workflowTemplateId"`
	WorkflowTemplateGroup *wCommon.GroupInfoForTmplInfo     `json:"workflowTemplateGroup,omitempty"`
	Name                  common2.I18nString                `json:"name"`
	Description           common2.I18nString                `json:"description"`
	Origin                common2.UserOriginEnum            `json:"origin,omitempty"`
	Nodes                 []*NodeTemplateInfo               `json:"nodes"`
	RemoveDuplicate       bool                              `json:"removeDuplicate"`
	Form                  interface{}                       `json:"form,omitempty"`
	CurrentVersion        string                            `json:"currentVersion,omitempty"`
	Staff                 json.RawMessage                   `json:"staff,omitempty"`
	Versions              []string                          `json:"versions,omitempty"`
	State                 common2.WorkflowTemplateStateEnum `json:"state"`
	Tags                  []*WorkflowTemplateTagInfo        `json:"tags"`
	CreateTime            int64                             `json:"createTime"`
	UpdateTime            int64                             `json:"updateTime"`

	AllowEdit    bool `json:"allowEdit"`
	AllowEnable  bool `json:"allowEnable"`
	AllowDisable bool `json:"allowDisable"`
	AllowDelete  bool `json:"allowDelete"`
}

type NodeTemplateInfo struct {
	Name         json.RawMessage      `json:"name,omitempty"`
	TemplateUuid string               `json:"templateUuid"`
	Uuid         string               `json:"uuid"`
	Type         common2.NodeTypeEnum `json:"type"`
	Detail       json.RawMessage      `json:"detail,omitempty"`
	Front        json.RawMessage      `json:"front,omitempty"`
	Next         []*NodeTemplateInfo  `json:"next,omitempty"`
}

type WorkflowTemplateGroupListCondition struct {
	WorkflowTemplateGroupName string                            `json:"workflowTemplateGroupName"`
	WorkflowTemplateUuid      string                            `json:"workflowTemplateUuid"`
	WorkflowTemplateName      string                            `json:"workflowTemplateName"`
	WorkflowTemplateState     common2.WorkflowTemplateStateEnum `json:"workflowTemplateState"`
}

type WorkflowTemplateGroupInfo struct {
	UserId                  int64                   `json:"userId,omitempty"`
	WorkflowTemplateGroupId int64                   `json:"workflowTemplateGroupId"`
	Name                    json.RawMessage         `json:"name"`
	Description             json.RawMessage         `json:"description"`
	WorkflowTemplateCount   int64                   `json:"workflowTemplateCount"`
	CreateTime              int64                   `json:"createTime"`
	UpdateTime              int64                   `json:"updateTime"`
	WorkflowTemplates       []*WorkflowTemplateInfo `json:"workflowTemplates,omitempty"`

	AllowEdit   bool `json:"allowEdit"`
	AllowDelete bool `json:"allowDelete"`
	AllowDetail bool `json:"allowDetail"`
}
