package template

import (
	"encoding/json"
	"errors"
	"gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wCommon "gitlab.docsl.com/security/bpm/internal/logic/workflow/common"
	"gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	wrLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/runtime"
	"gitlab.docsl.com/security/bpm/internal/model/tag"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"sort"
	"strconv"
	"strings"

	"github.com/jianfengye/collection"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

// 批量插入一堆node_template和一条workflow_template
func (op *WorkflowTmplOperator) BatchInsertWorkflowAndNodeTemplate(tmplUuid string, nodes []*NodeTemplateInput,
	form interface{}, staff []*staffLogic.StaffInfo, groupId int64, name, description common.I18nString,
	state common.WorkflowTemplateStateEnum, origin common.UserOriginEnum, version string, removeDuplicate bool, tags workflowModel.WorkflowTemplateTags, newRecord bool) (workflowTmplUuid string, err error) {

	//step 1
	//workflowTmplUuid = NewUuid(RESOURCE_TYPE_WORKFLOW_TEMPLATE)
	workflowTmplUuid = tmplUuid
	//step 2. 生成node_template的Uuid
	nodeUuidMap := make(map[string]string)
	for idx := range nodes {
		nodeUuidMap[nodes[idx].TemplateUuid] = NewUuid(RESOURCE_TYPE_NODE_TEMPLATE)
	}
	//替换node的Uuid
	for idx := range nodes {
		nodes[idx].TemplateUuid = nodeUuidMap[nodes[idx].TemplateUuid]
		for _, nextNode := range nodes[idx].Next {
			if newUuid, ok := nodeUuidMap[nextNode.TemplateUuid]; !ok {
				return StringEmpty, common.ErrNodeTopology
			} else {
				nextNode.TemplateUuid = newUuid
			}
		}
	}
	//step 3. 生成workflow_template的拓扑
	workflowDetail, err := op.AssembleNodeTmplTopology(nodes)
	if err != nil {
		return StringEmpty, err
	}
	workflowDetail.RemoveDuplicate = removeDuplicate
	workflowBytes, err := json.Marshal(workflowDetail)
	if err != nil {
		return StringEmpty, err
	}
	//step 4.组装nodeTemplate参数
	var nodeTables []*workflowModel.NodeTemplateTable
	for _, node := range nodes {
		detailBytes, err := node.Detail.MarshalJSON()
		if err != nil {
			return StringEmpty, err
		}
		if err = op.ValidateNodeDetail(detailBytes, node.Type); err != nil {
			return StringEmpty, common.ErrInvalidNodeDetail
		}
		front := &formLogic.Front{
			Form:           form,
			FormPermission: node.FormPermission,
		}
		frontByte, err := JsonEncode(front)
		if err != nil {
			return StringEmpty, err
		}
		name, _ := node.Name.MarshalJSON()
		n := &workflowModel.NodeTemplateTable{
			Uuid:       node.TemplateUuid,
			ParentUuid: workflowTmplUuid,
			Name:       string(name),
			Type:       node.Type,
			Detail:     string(detailBytes),
			Front:      string(frontByte),
			Status:     common.STATUS_NORMAL,
		}
		nodeTables = append(nodeTables, n)
	}

	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return StringEmpty, err
	}
	staffBytes, err := JsonEncode(staff)
	if err != nil {
		return StringEmpty, err
	}
	//step 5. 执行插入
	//默认插入是下线状态
	_, err = wModel.BatchInsertWorkflowAndNodeTemplate(nodeTables, string(staffBytes),
		groupId, workflowTmplUuid, name.Marshal(),
		description.Marshal(), string(workflowBytes),
		version, origin, state, common.STATUS_NORMAL, tags)

	return workflowTmplUuid, err
}

func (op *WorkflowTmplOperator) EnableWorkFlowTemplate(workflowTemplateUuids []string) error {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	for _, workflowTemplateUuid := range workflowTemplateUuids {
		var versions []string
		templates, err := pModel.QueryWorkflowTemplate(workflowTemplateUuid) //查询该模块的所有历史版本
		if err != nil {
			return err
		} else if len(templates) == 0 {
			return ErrRecordNotFound
		}
		//只能有一个版本处于Enable状态,所以需要将已Enabled的版本disable掉
		for _, tmpl := range templates {
			if len(tmpl.Edition) > 0 {
				versions = append(versions, tmpl.Edition)
			}
			if tmpl.State == common.STATE_ENABLED {
				err := pModel.DisableWorkflowTemplate(tmpl.Uuid, tmpl.Edition)
				if err != nil {
					return err
				}
			}
		}
		//2020-6-10 文琪修改为只能上线最新的version的模板
		sort.Slice(versions, func(i, j int) bool { //2019-12-13 fix 支持大于10的版本号
			if len(versions[i]) < len(versions[j]) {
				return true
			} else if len(versions[i]) > len(versions[j]) {
				return false
			} else {
				return versions[i] < versions[j]
			}
		})
		if len(versions) > 0 {
			//启用新模板
			err = pModel.EnableWorkflowTemplate(workflowTemplateUuid, versions[len(versions)-1])
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (op *WorkflowTmplOperator) DisableWorkFlowTemplate(workflowTemplateUuids []string) error {
	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	for _, workflowTemplateUuid := range workflowTemplateUuids {
		templates, err := pModel.QueryWorkflowTemplate(workflowTemplateUuid) //查询该模块的所有历史版本
		if err != nil {
			return err
		} else if len(templates) == 0 {
			return errors.New("record is not exist")
		}
		//找到enable的那个version
		var version string
		for _, template := range templates {
			if template.State == common.STATE_ENABLED {
				version = template.Edition
			}
		}
		if version != StringEmpty {
			//禁用模板
			err = pModel.DisableWorkflowTemplate(workflowTemplateUuid, version)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (op *WorkflowTmplOperator) InsertWorkFlowTemplate(groupId int64, workflowTmplUuid, name, desc,
	topologyDetail, edition string, origin common.UserOriginEnum) (id int64, err error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return 0, err
	}
	return wModel.InsertWorkflowTemplate(groupId, workflowTmplUuid, name, desc,
		topologyDetail, edition, origin, common.STATE_STAGING, common.STATUS_NORMAL)
}

func (op *WorkflowTmplOperator) EditWorkflowTemplateTags(workflowTemplateInfo WorkflowTemplateInfo, tagIds []int64) error {

	tagModel, err := workflow_tag.NewWorkflowTagModel(op, false)
	if err != nil {
		return err
	}
	if len(tagIds) > 0 {
		if workflowTagTable, err := tagModel.QueryWorkflowTagsById(tagIds); err != nil {
			return err
		} else {
			tagIdsCollection := collection.NewInt64Collection([]int64{})
			for _, row := range workflowTagTable {
				tagIdsCollection.Append(row.ID)
			}
			tagIds, _ = tagIdsCollection.Unique().ToInt64s()
		}
	}

	pModel, err := workflowModel.NewWorkflowModel(op, false)
	templates, err := pModel.QueryWorkflowTemplate(workflowTemplateInfo.WorkflowTemplateUuid) //查询该模块的所有历史版本
	if err != nil {
		return err
	} else if len(templates) == 0 {
		return ErrRecordNotFound
	}

	for _, tmpl := range templates {
		if len(workflowTemplateInfo.Version) != 0 {
			if tmpl.Edition == workflowTemplateInfo.Version {
				if err := pModel.EditWorkflowTemplateTag(map[string]interface{}{
					"uuid":    tmpl.Uuid,
					"edition": tmpl.Edition,
				}, tagIds); err != nil {
					return err
				}
				break
			}
		} else {
			if err := pModel.EditWorkflowTemplateTag(map[string]interface{}{
				"uuid": tmpl.Uuid,
			}, tagIds); err != nil {
				return err
			}
			break
		}
	}

	return nil
}

/*func (op *WorkflowTmplOperator) StagingWorkFlowTemplate(workflowTemplateUuid, version string, nodes []*NodeTemplateInput,
	staff []*staffLogic.StaffInfo, form interface{}, groupId int64, name, description bpmCom.I18nString,
	state bpmCom.WorkflowTemplateStateEnum, origin bpmCom.UserOriginEnum, removeDuplicate bool) (templateUuid, newVersion string, err error) {

	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return STRING_EMPTY, STRING_EMPTY, err
	}

	var newRecord bool

	if version != STRING_EMPTY {
		newRecord = false
		//修改指定版本的node内容
		workflowTemplateTable, err := wModel.QueryWorkflowTemplateByUuidAndVersion(workflowTemplateUuid, version)
		if err != nil {
			return STRING_EMPTY, STRING_EMPTY, err
		} else if workflowTemplateTable.State != bpmCom.STATE_STAGING {
			return STRING_EMPTY, STRING_EMPTY, bpmCom.ErrWorkflowTmplNotStaging
		}
		nodeTmplUuidMap, _, err := weLogic.GetNodeUuidNextUuidsMapByWorkflowDetailStr(workflowTemplateTable.Detail)
		if err != nil {
			return STRING_EMPTY, STRING_EMPTY, err
		}

		nodeTmplUuids := make([]string, 0, len(nodeTmplUuidMap))
		for k := range nodeTmplUuidMap {
			nodeTmplUuids = append(nodeTmplUuids, k)
		}
		err = wModel.DeleteNodeTemplateByNodeUuids(nodeTmplUuids)
		if err != nil {
			return STRING_EMPTY, STRING_EMPTY, err
		}
	} else {
		newRecord = false
		//在原有的版本基础上生成一条新的版本
		versions, err := wModel.QueryWorkflowTemplateAllVersions(workflowTemplateUuid)
		if err != nil || len(versions) == 0 {
			return STRING_EMPTY, STRING_EMPTY, err
		}
		sort.Slice(versions, func(i, j int) bool { //2019-12-13 fix 支持大于10的版本号
			if len(versions[i]) < len(versions[j]) {
				return true
			} else if len(versions[i]) > len(versions[j]) {
				return false
			} else {
				return versions[i] < versions[j]
			}
		})
		version, err = parseVersionStrAndIncrease(versions[len(versions)-1])
	}

	workflowTemplateUuid, err = op.BatchInsertWorkflowAndNodeTemplate(
		workflowTemplateUuid,
		nodes,
		form,
		staff,
		groupId,
		name,
		description,
		state,
		origin,
		version,
		removeDuplicate,
		newRecord)
	return workflowTemplateUuid, version, err
}
*/

func (op *WorkflowTmplOperator) EditWorkFlowTemplate(workflowTemplateUuid string, nodes []*NodeTemplateInput,
	staff []*staffLogic.StaffInfo, form interface{}, groupId int64, name, description common.I18nString,
	origin common.UserOriginEnum, removeDuplicate bool, create bool, publish bool, tags workflowModel.WorkflowTemplateTags) (templateUuid, version string, err error) {

	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return StringEmpty, StringEmpty, err
	}
	var state common.WorkflowTemplateStateEnum

	if tags == nil {
		if t, err := wModel.QueryWorkflowTemplateByUuid(workflowTemplateUuid); err != nil {
			return StringEmpty, StringEmpty, err
		} else {
			tags = t.Tags
		}
	}

	//case 1. 只是保存草稿箱，不发布
	if !create && !publish {
		//修改指定版本的node内容
		stagingWorkflowTemplateTable, err := wModel.QueryStagingWorkflowTemplateByUuid(workflowTemplateUuid)

		if err != nil && err != ErrRecordNotFound {
			return StringEmpty, StringEmpty, err
		} else if err == nil {
			nodeTmplUuidMap, _, err := engine.GetNodeUuidNextUuidsMapByWorkflowDetailStr(stagingWorkflowTemplateTable.Detail)
			if err != nil {
				return StringEmpty, StringEmpty, err
			}

			nodeTmplUuids := make([]string, 0, len(nodeTmplUuidMap))
			for k := range nodeTmplUuidMap {
				nodeTmplUuids = append(nodeTmplUuids, k)
			}
			err = wModel.DeleteNodeTemplateByNodeUuids(nodeTmplUuids)
			if err != nil {
				return StringEmpty, StringEmpty, err
			}
		}
		state = common.STATE_STAGING

	} else {
		//先把原有的version都查出来，确定本次插入的version
		var versions []string
		templates, err := wModel.QueryWorkflowTemplate(workflowTemplateUuid) //查询该模块的所有历史版本
		if err != nil {
			return StringEmpty, StringEmpty, err
		} else if len(templates) == 0 {
			return StringEmpty, StringEmpty, ErrRecordNotFound
		}
		//只能有一个版本处于Enable状态,所以需要将已Enabled的版本disable掉
		for _, tmpl := range templates {
			if len(tmpl.Edition) > 0 {
				versions = append(versions, tmpl.Edition)
			}
			if tmpl.State == common.STATE_ENABLED || tmpl.State == common.STATE_DISABLED {
				err = wModel.SetWorkflowTemplateHistory(workflowTemplateUuid, tmpl.Edition)
				if err != nil {
					return StringEmpty, StringEmpty, err
				}
			}
		}
		if len(versions) == 0 {
			version = "v1.0"
		} else {
			sort.Slice(versions, func(i, j int) bool { //2019-12-13 fix 支持大于10的版本号
				if len(versions[i]) < len(versions[j]) {
					return true
				} else if len(versions[i]) > len(versions[j]) {
					return false
				} else {
					return versions[i] < versions[j]
				}
			})
			version, err = parseVersionStrAndIncrease(versions[len(versions)-1])
			if err != nil || len(versions) == 0 {
				return StringEmpty, StringEmpty, err
			}
		}
		state = common.STATE_ENABLED
	}
	workflowTemplateUuid, err = op.BatchInsertWorkflowAndNodeTemplate(
		workflowTemplateUuid,
		nodes,
		form,
		staff,
		groupId,
		name,
		description,
		state,
		origin,
		version,
		removeDuplicate,
		tags,
		false)
	if err != nil {
		return workflowTemplateUuid, version, err
	}
	if publish { //如果是从草稿箱里发布，需要删掉原草稿箱里的东西
		err = op.DeleteStagingWorkflowTemplateByUuid(workflowTemplateUuid)
	}
	return workflowTemplateUuid, version, err
}

func parseVersionStrAndIncrease(versionStr string) (newVersionStr string, err error) {
	tmpSlice := strings.Split(versionStr, "v")
	if len(tmpSlice) == 1 {
		return StringEmpty, ErrInvalidFieldf("version")
	} else if versionFloat, err := strconv.ParseFloat(tmpSlice[1], 64); err != nil {
		return StringEmpty, ErrInvalidFieldf("version")
	} else {
		newVerion := (versionFloat*10 + 1) / 10
		return "v" + strconv.FormatFloat(newVerion, 'f', 1, 64), nil
	}
}

// 组装workflow的拓扑信息（放在detail中）
func (op *WorkflowTmplOperator) AssembleNodeTmplTopology(inputs []*NodeTemplateInput) (detail engine.WorkflowDetail, err error) {
	nodesIdxMap := make(map[string]int)
	for idx, input := range inputs {
		detail.Nodes = append(detail.Nodes, &engine.TopologyDetail{
			TemplateUuid: input.TemplateUuid,
			Type:         input.Type,
		})
		nodesIdxMap[input.TemplateUuid] = idx
	}
	for idx := range detail.Nodes {
		for _, nextNode := range inputs[idx].Next {
			nextIdx, ok := nodesIdxMap[nextNode.TemplateUuid]
			if !ok {
				return detail, common.ErrNodeTopology
			}
			detail.Nodes[idx].Next = append(detail.Nodes[idx].Next, &engine.TopologyDetail{
				TemplateUuid: detail.Nodes[nextIdx].TemplateUuid,
				Type:         detail.Nodes[nextIdx].Type,
			})
			detail.Nodes[nextIdx].Prev = append(detail.Nodes[nextIdx].Prev, &engine.TopologyDetail{
				TemplateUuid: detail.Nodes[idx].TemplateUuid,
				Type:         detail.Nodes[idx].Type,
			})
		}
	}
	return
}

func (op *WorkflowTmplOperator) ListWorkFlowTemplate(condition WorkflowTemplateListCondition, offset, limit int64) (
	infos []*WorkflowTemplateInfo, totalCnt int64, err error) {

	m := make(map[string]interface{})
	m["status"] = common.STATUS_NORMAL

	if condition.WorkflowTemplateGroupId > 0 {
		m["group_id"] = condition.WorkflowTemplateGroupId
	}

	if condition.UserId > 0 {
		m["user_id"] = condition.UserId
	}

	if condition.WorkflowTemplateState != StringEmpty {
		m["state"] = condition.WorkflowTemplateState
	}

	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, 0, err
	}

	templates, totalCount, err := wModel.QueryWorkflowTemplateByWhereMapWithOffsetAndLimit(m, nil,
		nil, condition.WorkflowTemplateIds, condition.WorkflowTemplateGroupIds,
		condition.WorkflowTemplateUuids, condition.TagIds, condition.NoneTag, condition.WorkflowTemplateName, offset, limit)

	if err != nil {
		return nil, 0, err
	}

	var groupIds []int64
	var tmplUuids []string

	for _, t := range templates {
		info := &WorkflowTemplateInfo{
			UserId:                t.UserId,
			Name:                  t.Name,
			Description:           t.Description,
			Origin:                t.Origin,
			WorkflowTemplateUuid:  t.Uuid,
			WorkflowTemplateId:    t.ID,
			WorkflowTemplateState: t.State,
			Version:               t.Edition,
			CreateTime:            t.CreateTime,
			UpdateTime:            t.UpdateTime,
			Tags:                  []*WorkflowTemplateTagInfo{},
		}
		if nameI18nString, err := common.NewI18nString(t.Name); err == nil {
			info.Name = nameI18nString
		}
		if descriptionI18nString, err := common.NewI18nString(t.Description); err == nil {
			info.Description = descriptionI18nString
		}
		if t.GroupId > 0 {
			info.WorkflowTemplateGroup = &wCommon.GroupInfoForTmplInfo{WorkflowTemplateGroupId: t.GroupId}
			groupIds = append(groupIds, t.GroupId)
		}

		tmplUuids = append(tmplUuids, t.Uuid)

		info.AllowDelete = true // TODO PERMISSION
		info.AllowEdit = true
		info.AllowDisable = true
		info.AllowEnable = true
		info.AllowDetail = true

		infos = append(infos, info)
	}

	//查询模板组数据
	groupInfos, err := wModel.QueryTemplateGroupByGroupIds(groupIds)
	if err != nil {
		op.Ctx().Log().Errorf("QueryGroupInfosByGroupIds[%v] err[%v]", groupIds, err)
	}
	groupIdNameMap := make(map[int64]json.RawMessage)
	for _, groupInfo := range groupInfos {
		if name, err := JsonStringToRaw(groupInfo.Name); err != nil {
			return nil, 0, err
		} else {
			groupIdNameMap[groupInfo.ID] = name
		}
	}

	//tag
	tagInfoMap, err := wModel.QueryTemplateTagsInfoByUuid(tmplUuids)
	if err != nil {
		return nil, 0, err
	}

	//拼一下
	for _, info := range infos {
		if info.WorkflowTemplateGroup != nil {
			info.WorkflowTemplateGroup.Name = groupIdNameMap[info.WorkflowTemplateGroup.WorkflowTemplateGroupId]
		}
		if tags, ok := tagInfoMap[info.WorkflowTemplateUuid][info.Version]; ok {
			for _, t := range tags {
				info.Tags = append(info.Tags, &WorkflowTemplateTagInfo{
					Id:      t.ID,
					TagName: t.TagName,
				})
			}
		}
	}

	return infos, totalCount, nil
}

func (op *WorkflowTmplOperator) GetWorkFlowTemplateDetail(workflowTemplateUuid, version string, staging bool) (res *WorkflowTemplateDetail, err error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return res, err
	}
	// step1. 查询该模板的所有版本列表
	versions, err := wModel.QueryWorkflowTemplateNormalVersions(workflowTemplateUuid)
	if err != nil {
		return res, err
	}

	// step2. 从workflow_template表中查询模板详情
	workflowTemplateTable, err := wModel.QueryWorkflowTemplateByUuidAndVersion(workflowTemplateUuid, version, staging)
	if err != nil {
		return res, err
	}

	staffInfo, err := JsonStringToRaw(workflowTemplateTable.Staff)
	if err != nil {
		return res, err
	}

	//之后拼数据用
	nodeTmplUuidNextUuidsMap, topo, err := engine.GetNodeUuidNextUuidsMapByWorkflowDetailStr(workflowTemplateTable.Detail)
	if err != nil {
		return res, err
	}
	nodeTmplUuids := make([]string, 0, len(nodeTmplUuidNextUuidsMap))
	for k := range nodeTmplUuidNextUuidsMap {
		nodeTmplUuids = append(nodeTmplUuids, k)
	}
	// step3. 从node_template表中查询node模板详情
	nodeTemplates, err := wModel.QueryNodeTemplatesByUuids(nodeTmplUuids)
	if err != nil {
		return res, err
	}
	//做个map
	nodeTmplUuidTmplMap := make(map[string]workflowModel.NodeTemplateTable)
	for _, nodeTemplate := range nodeTemplates {
		nodeTmplUuidTmplMap[nodeTemplate.Uuid] = *nodeTemplate
	}

	//拼装
	var nodes []*NodeTemplateInfo
	var form interface{}
	for _, nodeTemplate := range nodeTemplates {
		node := &NodeTemplateInfo{
			TemplateUuid: nodeTemplate.Uuid,
			Uuid:         nodeTemplate.Uuid,
			Type:         nodeTemplate.Type,
		}
		if detail, err := JsonStringToRaw(nodeTemplate.Detail); err != nil {
			return res, err
		} else if front, err := JsonStringToRaw(nodeTemplate.Front); err != nil {
			return res, err
		} else {
			node.Detail = detail
			node.Front = front
		}
		front := &formLogic.Front{}
		if form == nil {
			if err = JsonDecode([]byte(nodeTemplate.Front), &front); err == nil {
				form = front.Form
				//TODO DEL 为前端暂时去掉front
				front.Form = nil
				node.Front, _ = JsonEncode(front)
				//TODO DEL 为前端暂时去掉front
			}
		}
		if name, err := JsonStringToRaw(nodeTemplate.Name); err == nil {
			node.Name = name
		}
		for _, nextUuid := range nodeTmplUuidNextUuidsMap[node.TemplateUuid] {
			node.Next = append(node.Next, &NodeTemplateInfo{
				TemplateUuid: nextUuid,
				Uuid:         nextUuid,
				Type:         nodeTmplUuidTmplMap[nextUuid].Type,
			})
		}
		nodes = append(nodes, node)
	}
	res = &WorkflowTemplateDetail{
		UserId:               workflowTemplateTable.UserId,
		WorkflowTemplateUuid: workflowTemplateTable.Uuid,
		WorkflowTemplateId:   workflowTemplateTable.ID,
		CurrentVersion:       workflowTemplateTable.Edition,
		Versions:             versions,
		Origin:               workflowTemplateTable.Origin,
		Nodes:                nodes,
		RemoveDuplicate:      topo.RemoveDuplicate,
		Form:                 form,
		Staff:                staffInfo,
		State:                workflowTemplateTable.State,
		CreateTime:           workflowTemplateTable.CreateTime,
		UpdateTime:           workflowTemplateTable.UpdateTime,
		Tags:                 []*WorkflowTemplateTagInfo{},
	}

	//tags
	tModel, err := workflow_tag.NewWorkflowTagModel(op, false)
	if err != nil {
		return nil, err
	}
	tagIds, _ := collection.NewInt64Collection(workflowTemplateTable.Tags).Unique().ToInt64s()
	if len(tagIds) > 0 {
		tagInfos, err := tModel.QueryWorkflowTagsById(tagIds)
		if err != nil {
			op.Ctx().Log().Errorf("Query tag info error [%v] err[%v]", tagIds, err)
		}
		for _, tagInfo := range tagInfos {
			res.Tags = append(res.Tags, &WorkflowTemplateTagInfo{
				Id:      tagInfo.ID,
				TagName: tagInfo.TagName,
			})
		}
	}

	//name
	if nameI18nString, err := common.NewI18nString(workflowTemplateTable.Name); err != nil {
		return res, err
	} else if descriptionI18nString, err := common.NewI18nString(workflowTemplateTable.Description); err != nil {
		return res, err
	} else {
		res.Name = nameI18nString
		res.Description = descriptionI18nString
	}

	//组装TemplateGroup内容
	if workflowTemplateTable.GroupId > 0 {
		res.WorkflowTemplateGroup = &wCommon.GroupInfoForTmplInfo{WorkflowTemplateGroupId: workflowTemplateTable.GroupId}
		groupInfo, err := wModel.QueryTemplateGroupByGroupId(workflowTemplateTable.GroupId)
		if err != nil {
			op.Ctx().Log().Warningln("query group info err:", err)
			return res, nil
		}
		if name, err := JsonStringToRaw(groupInfo.Name); err != nil {
			return res, err
		} else {
			res.WorkflowTemplateGroup.Name = name
		}
	}

	res.AllowDelete = true // TODO PERMISSION
	res.AllowEdit = true
	res.AllowDisable = true
	res.AllowEnable = true

	return res, nil
}

func (op *WorkflowTmplOperator) QueryAllWorkflowTmplByTmplGroupId(tmplGroupId int64, allowListTmplUuids, allowListTmplGroupIds collection.ICollection) (
	infos []*WorkflowTemplateInfo, err error) {

	m := make(map[string]interface{})
	m["status"] = common.STATUS_NORMAL
	m["group_id"] = tmplGroupId

	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}

	templates, err := wModel.QueryWorkflowTemplateByWhereMap(m)
	if err != nil {
		return nil, err
	}

	var tmplUuids []string

	for _, t := range templates {

		if allowListTmplUuids != nil && allowListTmplGroupIds != nil && !allowListTmplUuids.Contains(t.Uuid) && !allowListTmplGroupIds.Contains(t.GroupId) {
			continue
		}

		info := &WorkflowTemplateInfo{
			UserId:                t.UserId,
			Name:                  t.Name,
			Description:           t.Description,
			Origin:                t.Origin,
			WorkflowTemplateUuid:  t.Uuid,
			WorkflowTemplateId:    t.ID,
			WorkflowTemplateState: t.State,
			Version:               t.Edition,
			CreateTime:            t.CreateTime,
			UpdateTime:            t.UpdateTime,
			Tags:                  []*WorkflowTemplateTagInfo{},

			AllowEnable:  true,
			AllowDisable: true,
			AllowEdit:    true,
			AllowDelete:  true,
			AllowDetail:  true,
		}

		if nameI18nString, err := common.NewI18nString(t.Name); err == nil {
			info.Name = nameI18nString
		}
		if descriptionI18nString, err := common.NewI18nString(t.Description); err == nil {
			info.Description = descriptionI18nString
		}

		tmplUuids = append(tmplUuids, t.Uuid)

		infos = append(infos, info)
	}

	tagInfoMap, err := wModel.QueryTemplateTagsInfoByUuid(tmplUuids)
	if err != nil {
		return nil, err
	}

	for _, info := range infos {
		if tags, ok := tagInfoMap[info.WorkflowTemplateUuid][info.Version]; ok {
			for _, t := range tags {
				info.Tags = append(info.Tags, &WorkflowTemplateTagInfo{
					Id:      t.ID,
					TagName: t.TagName,
				})
			}
		}
	}

	return infos, nil
}

// 删除草稿箱中的模板
func (op *WorkflowTmplOperator) DeleteStagingWorkflowTemplateByUuid(workflowTemplateUuid string) error {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	workflowTemplateTable, err := wModel.QueryStagingWorkflowTemplateByUuid(workflowTemplateUuid)
	if err == ErrRecordNotFound {
		return nil
	} else if err != nil {
		return err
	}
	nodeTmplUuidMap, _, err := engine.GetNodeUuidNextUuidsMapByWorkflowDetailStr(workflowTemplateTable.Detail)
	if err != nil {
		return err
	}
	nodeTmplUuids := make([]string, 0, len(nodeTmplUuidMap))
	for k := range nodeTmplUuidMap {
		nodeTmplUuids = append(nodeTmplUuids, k)
	}
	return wModel.BatchDeleteStagingWorkflowAndNodeTemplate(nodeTmplUuids, workflowTemplateUuid)
}

func (op *WorkflowTmplOperator) DeleteWorkFlowTemplate(workflowTemplateUuids []string, staging bool) error {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	for _, workflowTemplateUuid := range workflowTemplateUuids {
		templates, err := wModel.QueryWorkflowTemplate(workflowTemplateUuid) //查询该模块的所有历史版本
		if err != nil {
			return err
		} else if len(templates) == 0 {
			return errors.New("record is not exist")
		}
		if !staging {
			//如果没有传staging，则默认删除该模板的所有版本
			err := wModel.DeleteAllVersionsOfWorkflowAndNodeTemplate(workflowTemplateUuid)
			if err != nil {
				return err
			}

		} else {
			err := op.DeleteStagingWorkflowTemplateByUuid(workflowTemplateUuid)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (op *WorkflowTmplOperator) QueryWorkFlowTemplateByTmplUuid(tmplUuid string) (
	*workflowModel.WorkflowTemplateTable, error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	return wModel.QueryWorkflowTemplateByUuid(tmplUuid)
}

func (op *WorkflowTmplOperator) QueryWorkFlowTemplateByTmplUuids(tmplUuids []string) ([]*workflowModel.WorkflowTemplateTable, error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	return wModel.QueryWorkflowTemplateByUuids(tmplUuids)
}

// //////////////////TO C////////////////////
func (op *WorkflowTmplOperator) GetWorkFlowTemplateDetailForC(workflowTemplateUuid string, preHandle bool, data interface{}) (res *WorkflowTemplateDetail, err error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return res, err
	}

	// step1. 从workflow_template表中查询可用的模板
	workflowTemplateTable, err := wModel.QueryEnabledWorkflowTemplateByUuid(workflowTemplateUuid)
	if err != nil {
		return res, err
	}

	staffOperator := staffLogic.NewStaffOperator(op.Ctx())
	allDeptIds, _, allUserGroupIds, err := staffOperator.GetUserAllDeptIdsAndDeptNamesAndUserGroupIds()
	if err != nil {
		return nil, err
	}

	//筛选是否有权限看到的模板
	var staffInfos []staffLogic.StaffInfo
	if err = JsonDecode([]byte(workflowTemplateTable.Staff), &staffInfos); err != nil {
		return nil, err
	}

	if !staffOperator.JudgeIfStaffInfosContainsAnyUserIdsOrDeptIdsOrUserGroupIds(
		staffInfos, []int64{op.Ctx().User().UserId}, allDeptIds, allUserGroupIds) {
		return nil, ErrNoPermission
	}

	nodeTmplUuidNextUuidsMap, topo, err := engine.GetNodeUuidNextUuidsMapByWorkflowDetailStr(workflowTemplateTable.Detail)
	if err != nil {
		return res, err
	}

	nodeTmplUuids := make([]string, 0, len(nodeTmplUuidNextUuidsMap))
	for k := range nodeTmplUuidNextUuidsMap {
		nodeTmplUuids = append(nodeTmplUuids, k)
	}
	// step2. 从node_template表中查询node模板详情
	nodeTemplateTables, err := wModel.QueryNodeTemplatesByUuids(nodeTmplUuids)
	if err != nil {
		return res, err
	}

	//需要剪枝操作
	if preHandle {
		var finish bool
		workflowOperator := wrLogic.NewWorkflowOperator(op.Ctx())
		topo, nodeTemplateTables, finish, err = workflowOperator.PreHandleWorkflowByWorkflowDetail(data.(map[string]interface{}), topo, nodeTemplateTables)
		if err != nil {
			return nil, err
		} else if !finish {
			return nil, common.ErrWorkflowImpossibleAfterCut
		}
		nodeTmplUuidNextUuidsMap, _, err = engine.GetNodeUuidNextUuidsMapByWorkflowDetail(topo) //根据topo重新生成map
		if err != nil {
			return res, err
		}
	}

	//做个map
	nodeTmplUuidTmplMap := make(map[string]workflowModel.NodeTemplateTable)
	for _, nodeTemplate := range nodeTemplateTables {
		nodeTmplUuidTmplMap[nodeTemplate.Uuid] = *nodeTemplate
	}

	//拼装
	var nodes []*NodeTemplateInfo
	var form interface{}
	for _, nodeTemplate := range nodeTemplateTables {
		node := &NodeTemplateInfo{
			TemplateUuid: nodeTemplate.Uuid,
			Uuid:         nodeTemplate.Uuid,
			Type:         nodeTemplate.Type,
		}
		if detail, err := JsonStringToRaw(nodeTemplate.Detail); err != nil {
			return res, err
		} else if front, err := JsonStringToRaw(nodeTemplate.Front); err != nil {
			return res, err
		} else {
			node.Detail = detail
			node.Front = front
		}
		front := &formLogic.Front{}
		if form == nil {
			if err = JsonDecode([]byte(nodeTemplate.Front), &front); err == nil {
				form = front.Form
				//TODO DEL 为前端暂时去掉front
				front.Form = nil
				node.Front, _ = JsonEncode(front)
				//TODO DEL 为前端暂时去掉front
			}
		}
		if name, err := JsonStringToRaw(nodeTemplate.Name); err == nil {
			node.Name = name
		}
		for _, nextUuid := range nodeTmplUuidNextUuidsMap[node.TemplateUuid] {
			node.Next = append(node.Next, &NodeTemplateInfo{
				TemplateUuid: nextUuid,
				Uuid:         nextUuid,
				Type:         nodeTmplUuidTmplMap[nextUuid].Type,
			})
		}
		nodes = append(nodes, node)
	}

	res = &WorkflowTemplateDetail{
		WorkflowTemplateUuid: workflowTemplateTable.Uuid,
		WorkflowTemplateId:   workflowTemplateTable.ID,
		Nodes:                nodes,
		RemoveDuplicate:      topo.RemoveDuplicate,
		Form:                 form,
		CreateTime:           workflowTemplateTable.CreateTime,
		UpdateTime:           workflowTemplateTable.UpdateTime,
		Tags:                 []*WorkflowTemplateTagInfo{},
	}

	//tags
	tModel, err := workflow_tag.NewWorkflowTagModel(op, false)
	if err != nil {
		return nil, err
	}
	tagIds, _ := collection.NewInt64Collection(workflowTemplateTable.Tags).Unique().ToInt64s()
	if len(tagIds) > 0 {
		tagInfos, err := tModel.QueryWorkflowTagsById(tagIds)
		if err != nil {
			op.Ctx().Log().Errorf("Query tag info error [%v] err[%v]", tagIds, err)
		}
		for _, tagInfo := range tagInfos {
			res.Tags = append(res.Tags, &WorkflowTemplateTagInfo{
				Id:      tagInfo.ID,
				TagName: tagInfo.TagName,
			})
		}
	}

	if nameI18nString, err := common.NewI18nString(workflowTemplateTable.Name); err != nil {
		return res, err
	} else if descriptionI18nString, err := common.NewI18nString(workflowTemplateTable.Description); err != nil {
		return res, err
	} else {
		res.Name = nameI18nString
		res.Description = descriptionI18nString
	}
	//组装TemplateGroup内容
	if workflowTemplateTable.GroupId > 0 {
		res.WorkflowTemplateGroup = &wCommon.GroupInfoForTmplInfo{WorkflowTemplateGroupId: workflowTemplateTable.GroupId}
		groupInfo, err := wModel.QueryTemplateGroupByGroupId(workflowTemplateTable.GroupId)
		if err != nil {
			return res, err
		}
		if name, err := JsonStringToRaw(groupInfo.Name); err == nil {
			res.WorkflowTemplateGroup.Name = name
		}
		if desc, err := JsonStringToRaw(groupInfo.Description); err == nil {
			res.WorkflowTemplateGroup.Description = desc
		}
	}

	return res, nil
}

func (op *WorkflowTmplOperator) ListWorkFlowTemplateForC() (
	infos []*WorkflowTemplateInfo, err error) {
	infos = make([]*WorkflowTemplateInfo, 0)

	pModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}

	staffOperator := staffLogic.NewStaffOperator(op.Ctx())
	allDeptIds, _, allUserGroupIds, err := staffOperator.GetUserAllDeptIdsAndDeptNamesAndUserGroupIds()
	if err != nil {
		return nil, err
	}

	//查询有权限看到的模板
	templateStaffs, err := pModel.QueryAllEnabledWorkflowTemplateStaff()
	if err != nil {
		return nil, err
	}

	//筛选有权限看到的模板
	var templateUuids []string
	for _, t := range templateStaffs {
		var staffInfos []staffLogic.StaffInfo
		if err = JsonDecode([]byte(t.Staff), &staffInfos); err != nil {
			continue
		}
		if staffOperator.JudgeIfStaffInfosContainsAnyUserIdsOrDeptIdsOrUserGroupIds(
			staffInfos, []int64{op.Ctx().User().UserId}, allDeptIds, allUserGroupIds) {
			templateUuids = append(templateUuids, t.Uuid)
			continue
		}
	}
	if len(templateUuids) == 0 {
		return
	}

	//2019-02-04 根据来源，对其他系统的流程，在hbpm上过滤使其在C端不可见
	var origins []common.UserOriginEnum
	if op.Ctx().User().Origin == common.OriginBpm {
		origins = []common.UserOriginEnum{common.OriginBpm, common.OriginAll}
	}

	templates, err := pModel.QueryEnabledWorkflowTemplateByUuidsAndOrigins(templateUuids, origins)
	if err != nil {
		return nil, err
	}

	var groupIds []int64
	var tmplUuids []string

	for _, t := range templates {
		info := &WorkflowTemplateInfo{
			WorkflowTemplateUuid: t.Uuid,
			WorkflowTemplateId:   t.ID,
			Version:              t.Edition,
			CreateTime:           t.CreateTime,
			UpdateTime:           t.UpdateTime,
			Edition:              t.Edition,
			Tags:                 []*WorkflowTemplateTagInfo{},
		}

		tmplUuids = append(tmplUuids, t.Uuid)

		if nameI18nString, err := common.NewI18nString(t.Name); err == nil {
			info.Name = nameI18nString
		}
		if descriptionI18nString, err := common.NewI18nString(t.Description); err == nil {
			info.Description = descriptionI18nString
		}
		if t.GroupId > 0 {
			info.WorkflowTemplateGroup = &wCommon.GroupInfoForTmplInfo{WorkflowTemplateGroupId: t.GroupId}
			groupIds = append(groupIds, t.GroupId)
		}

		infos = append(infos, info)
	}

	//查询模板组数据
	groupInfos, err := pModel.QueryTemplateGroupByGroupIds(groupIds)
	if err != nil {
		op.Ctx().Log().Errorf("QueryGroupInfosByGroupIds[%v] err[%v]", groupIds, err)
	}
	groupIdNameMap := make(map[int64]json.RawMessage)
	groupIdDescMap := make(map[int64]json.RawMessage)
	for _, groupInfo := range groupInfos {
		if name, err := JsonStringToRaw(groupInfo.Name); err == nil {
			groupIdNameMap[groupInfo.ID] = name
		}
		if desc, err := JsonStringToRaw(groupInfo.Description); err == nil {
			groupIdDescMap[groupInfo.ID] = desc
		}
	}

	//tag
	tagInfoMap, err := pModel.QueryTemplateTagsInfoByUuid(tmplUuids)
	if err != nil {
		return nil, err
	}

	//拼一下
	for _, info := range infos {
		if info.WorkflowTemplateGroup != nil {
			info.WorkflowTemplateGroup.Name = groupIdNameMap[info.WorkflowTemplateGroup.WorkflowTemplateGroupId]
			info.WorkflowTemplateGroup.Description = groupIdDescMap[info.WorkflowTemplateGroup.WorkflowTemplateGroupId]
		}
		if tags, ok := tagInfoMap[info.WorkflowTemplateUuid][info.Edition]; ok {
			for _, t := range tags {
				info.Tags = append(info.Tags, &WorkflowTemplateTagInfo{
					Id:      t.ID,
					TagName: t.TagName,
				})
			}
		}
	}

	return infos, nil
}
