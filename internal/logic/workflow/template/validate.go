/**
 * @note
 * smooth_upgrade.go
 *
 * <AUTHOR>
 * @date 	2020-07-01
 */
package template

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

func (op *WorkflowTmplOperator) ValidateNodeDetail(detailBytes []byte, nodeType bpmCom.NodeTypeEnum) (err error) {
	if nodeType == bpmCom.NODE_TYPE_GATEWAY { //校验gateway的detail
		d := engine.GatewayDetail{} //解gateway配置
		if err := common.JsonDecode(detailBytes, &d); err != nil {
			return err
		}
		for _, rule := range d.OutRule {
			if len(rule.Condition) > 0 {
				if err = op.ValidateGatewayCondition(rule.Condition); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func (op *WorkflowTmplOperator) ValidateGatewayCondition(condition string) error {
	_, err := engine.InitExpression(op.Ctx(), condition)
	return err
}
