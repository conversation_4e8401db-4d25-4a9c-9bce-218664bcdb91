/**
 * @note
 * workflow的预处理
 *
 * <AUTHOR>
 * @date 	2020-02-12
 */
package runtime

import (
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	"gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	wModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

/* @note
 * 处理用户的输入，剪枝，返回新的拓扑和node信息
 */
func (op *WorkflowOperator) PreHandleWorkflowByWorkflowDetail(params map[string]interface{},
	topo *engine.WorkflowDetail, nodes []*wModel.NodeTemplateTable) (newTopo *engine.WorkflowDetail,
	newNodes []*wModel.NodeTemplateTable, finish bool, err error) {
	//组装一个map，方便查找和后续处理
	nodeMap := make(map[string]*NodeRecord)
	for idx, n := range nodes {
		nodeMap[n.Uuid] = &NodeRecord{
			Table: nodes[idx],
			State: common2.STATE_READY,
		}
	}
	for _, t := range topo.Nodes {
		if nodeMap[t.TemplateUuid] == nil {
			return nil, nil, false, common2.ErrNodeTopology
		}
		for _, next := range t.Next {
			if nodeMap[next.TemplateUuid] == nil {
				return nil, nil, false, common2.ErrNodeTopology
			}
			nodeMap[t.TemplateUuid].Next = append(nodeMap[t.TemplateUuid].Next, nodeMap[next.TemplateUuid])
		}
		for _, prev := range t.Prev {
			if nodeMap[prev.TemplateUuid] == nil {
				return nil, nil, false, common2.ErrNodeTopology
			}
			nodeMap[t.TemplateUuid].Prev = append(nodeMap[t.TemplateUuid].Prev, nodeMap[prev.TemplateUuid])
		}
	}
	var start *NodeRecord
	//先找到start，从起点开始梳理
	for uuid, n := range nodeMap {
		if len(n.Prev) == 0 { //start没有prev
			start = nodeMap[uuid]
		}
	}
	if start == nil {
		return nil, nil, false, common2.ErrNodeTopology
	}
	inputKeys := make([]string, 0, len(params))
	for key := range params {
		inputKeys = append(inputKeys, key)
	}
	//调用do，简单走一遍流程
	if finish, err = op.do(start, params, inputKeys, nodeMap); err != nil {
		return nil, nil, finish, err
	} else if !finish {
		return nil, nil, finish, nil
	}
	//然后对无用分支进行剪枝
	op.cut(start, nodeMap)
	//整理出新的拓扑和node表
	newTopo, newNodes = tidy(topo, nodes, nodeMap)
	s, _ := common.JsonStringEncode(newTopo)
	op.Ctx().Log().Debugln("new topology:", s)
	return newTopo, newNodes, true, nil
}

type NodeRecord struct {
	Table          *wModel.NodeTemplateTable
	Next           []*NodeRecord
	Prev           []*NodeRecord
	State          common2.WorkflowStateEnum //在此只可能有Ready/Finish/Discard
	WritableFields []string                  //当前节点处有可能被非发起人更改的字段
	Passes         []int                     //仅对gateway有效，0=不通过，1=可能通过（有可更改条件的情况），2=肯定能通过
}

const (
	NOT_PASS        = 0
	MAY_PASS        = 1
	ABSOLUTELY_PASS = 2
)

// 将流程图走一遍，不可能走到的节点将被标记为discard状态，其余均应该是finish
// 先统计当前节点可能被修改的变量
// 然后对于当前节点分析：
// 1. 普通节点，上一个节点不是gateway，则将此节点状态修改为与上一个节点相同
// 2. 普通节点，上一个节点是gateway，根据gateway的pass数组修改此节点状态
// 3. gateway节点：
// 3.1. 对所有前序节点prev，如果有的没完成，则什么都不做，返回。
// 3.2. 如果所有前序节点prev已经完成，那么Finish的普通prev的记一个成功，Discard的普通prev记一个失败，Discard的gateway-prev记一个或多个失败，Finish的gateway-prev根据pass数组记成功或失败
// 3.3. 根据成功和失败数判断此gateway是finish还是discard
// 3.4. 若finish，则根据condition判断是否输出并赋值pass数组，另外，若某个分支的输出condition里包含可能被修改的变量，则直接记此分支为pass状态。
// 4. 对当前节点的所有next节点，递归做同样的事。
func (op *WorkflowOperator) do(current *NodeRecord,
	params map[string]interface{}, //用户输入的所有key-value，外加其他可用于判断条件的用户属性
	inputKeys []string, //用户输入的所有key
	nodeMap map[string]*NodeRecord, //节点处理状态的记录
) (finish bool, err error) {
	//step 1. 统计当前节点可能被修改的变量
	var currentWritableFields []string
	for _, prev := range current.Prev {
		if nodeMap[prev.Table.Uuid].State == common2.STATE_FINISHED {
			currentWritableFields = mergeTwoStringSlice(currentWritableFields, nodeMap[prev.Table.Uuid].WritableFields)
		}
	}
	//step 2. 统计可能被审批人修改的变量
	if current.Table.Type == common2.NODE_TYPE_APPROVAL ||
		current.Table.Type == common2.NODE_TYPE_TRANSACTOR ||
		current.Table.Type == common2.NODE_TYPE_INTERACT { //审批和交互节点有可能篡改params，处理一下
		f := formLogic.Front{}
		if err := common.JsonStringDecode(nodeMap[current.Table.Uuid].Table.Front, &f); err != nil {
			return false, err
		}
		if f.FormPermission != nil {
			if f.FormPermission.AllWritable {
				formOperator := formLogic.NewFormOperator(op.Ctx())
				currentWritableFields = mergeTwoStringSlice(currentWritableFields, formOperator.ExtractFieldsFromForm(f.Form))
			} else {
				currentWritableFields = mergeTwoStringSlice(currentWritableFields, f.FormPermission.WritableFields)
			}
		}
	}
	//step 3. 将节点的当前的可变量的key记录下来
	nodeMap[current.Table.Uuid].WritableFields = currentWritableFields
	//step 4. 然后分情况讨论
	//step 4.a. 本节点不是gateway的情况
	if current.Table.Type != common2.NODE_TYPE_GATEWAY {
		if len(current.Prev) > 0 {
			if current.Prev[0].Table.Type != common2.NODE_TYPE_GATEWAY { //上一个节点也不是gateway
				nodeMap[current.Table.Uuid].State = nodeMap[current.Prev[0].Table.Uuid].State //将本节点状态与上节点置为相同
			} else {
				if nextIndexes, pass := getOutputIndexesOfNextNode(current.Prev[0], current.Table.Uuid), //查看上一个节点（gateway）的pass数组
					nodeMap[current.Prev[0].Table.Uuid].Passes; len(nextIndexes) > 0 && pass[nextIndexes[0]] != NOT_PASS {
					nodeMap[current.Table.Uuid].State = common2.STATE_FINISHED
				} else {
					nodeMap[current.Table.Uuid].State = common2.STATE_DISCARD
				}
			}
		} else {
			nodeMap[current.Table.Uuid].State = common2.STATE_FINISHED //没有prev，直接置为成功
		}
	} else {
		//step 4.b. 本节点是gateway的情况
		d := engine.GatewayDetail{} //解gateway配置
		if err := common.JsonStringDecode(nodeMap[current.Table.Uuid].Table.Detail, &d); err != nil {
			return false, err
		}
		//先判断输入的所有节点是否都已处理，否则等待所有输入节点都有结果后再继续
		var failedOrDiscardNum, hasInputNum int
		prevMap := make(map[string]bool) //这里做一个map，记录与前序gateway间多于一条线直连的情况，使这种情况下只处理一次前序gateway节点。
		for _, prev := range current.Prev {
			if nodeMap[prev.Table.Uuid].State == common2.STATE_READY { //如果存在前序节点未处理
				return false, nil
			} else if nodeMap[prev.Table.Uuid].State == common2.STATE_DISCARD { //如果前序节点已失败
				failedOrDiscardNum++
			} else if prev.Table.Type != common2.NODE_TYPE_GATEWAY { //如果前序节点非Gateway且成功
				hasInputNum++
			} else { //如果前序节点为Gateway且成功
				if !prevMap[prev.Table.Uuid] {
					prevMap[prev.Table.Uuid] = true //如果已经处理过前序的gateway了，则不需要再处理
				} else {
					continue
				}
				nextIndexes := getOutputIndexesOfNextNode(prev, current.Table.Uuid)
				for _, nextIdx := range nextIndexes {
					if nodeMap[prev.Table.Uuid].Passes[nextIdx] != NOT_PASS {
						hasInputNum++
					} else {
						failedOrDiscardNum++
					}
				}
			}
		}
		//自此应该开始处理此节点了
		//初始化pass数组
		nodeMap[current.Table.Uuid].Passes = make([]int, len(current.Next))
		//判断输入是否足够让gateway 通过，不够的话就置为失败
		if finish, _ := engine.ValuateInput(d.Spec, len(current.Prev), failedOrDiscardNum, hasInputNum); !finish {
			nodeMap[current.Table.Uuid].State = common2.STATE_DISCARD
		} else {
			nodeMap[current.Table.Uuid].State = common2.STATE_FINISHED
			//通过的情况下，要赋值pass数组
			for idx := range nodeMap[current.Table.Uuid].Passes {
				nodeMap[current.Table.Uuid].Passes[idx], err = judgeIfPossibleToPass(op.Ctx(), idx, d.OutRule, currentWritableFields, params)
				if err != nil {
					return false, err
				}
			}
			//如果一定都不能通过，看看有没有else分支，将outRule中的else分支作为一定pass的分支
			if judgeIfAllNotPass(nodeMap[current.Table.Uuid].Passes) {
				for idx, outRule := range d.OutRule {
					if outRule.Else {
						nodeMap[current.Table.Uuid].Passes[idx] = ABSOLUTELY_PASS
					}
				}
			}
			//如果还是一定都不能通过，看看有没有default分支，将outRule中的default分支作为一定pass的分支
			if judgeIfAllNotPass(nodeMap[current.Table.Uuid].Passes) {
				for idx, outRule := range d.OutRule {
					if outRule.Default {
						nodeMap[current.Table.Uuid].Passes[idx] = ABSOLUTELY_PASS
					}
				}
			}
			//如果都是不一定能通过，看看有没有else分支，将outRule中的else分支作为可能pass的分支
			if judgeIfAllNotAbsolutelyPass(nodeMap[current.Table.Uuid].Passes) {
				for idx, outRule := range d.OutRule {
					if outRule.Else {
						nodeMap[current.Table.Uuid].Passes[idx] = MAY_PASS
					}
				}
			}
			//如果还是都不一定能通过，看看有没有default分支，将outRule中的default分支作为可能pass的分支
			if judgeIfAllNotAbsolutelyPass(nodeMap[current.Table.Uuid].Passes) {
				for idx, outRule := range d.OutRule {
					if outRule.Default {
						nodeMap[current.Table.Uuid].Passes[idx] = MAY_PASS
					}
				}
			}
			//如果还是不能通过，那么此gateway还是难逃失败的命运
			if judgeIfAllNotPass(nodeMap[current.Table.Uuid].Passes) {
				nodeMap[current.Table.Uuid].State = common2.STATE_DISCARD
			}
		}
	}
	//继续往下走
	if len(current.Next) > 0 {
		for _, next := range current.Next {
			if f, err := op.do(next, params, inputKeys, nodeMap); err != nil {
				return false, err
			} else if f {
				finish = f
			}
		}
		return finish, nil
	} else {
		return current.State == common2.STATE_FINISHED, nil
	}
}

// 将走完的流程图剪枝
// 逻辑：根据当前节点的状态：
// 1.普通节点：什么都不做，继续往下走
// 2.gateway-Finish：按照pass数组，把未pass的分支下的直到下一个gateway的所有后续节点都去掉
// 3.gateway-Discard：先把所有前面的直到上一个gateway的节点都去掉，把自己去掉，再把直到下一个gateway的所有后续节点都去掉
func (op *WorkflowOperator) cut(current *NodeRecord, nodeMap map[string]*NodeRecord) {
	op.Ctx().Log().Debugln("cut deal with node", current.Table.Uuid, current.Table.Name,
		"type", current.Table.Type, "name", current.Table.Name, "passes", current.Passes)
	if current.Table.Type != common2.NODE_TYPE_GATEWAY { //普通节点
		if len(current.Next) > 0 {
			op.cut(current.Next[0], nodeMap)
		}
	} else { //gateway的情况
		if current.State == common2.STATE_FINISHED && judgeIfAllNotPass(current.Passes) {
			current.State = common2.STATE_DISCARD
		}
		needToRecur := make(map[string]*NodeRecord) //此节点结束后需要递归处理的节点
		if current.State == common2.STATE_DISCARD { //如果是失败状态，把所有的前序的gateway的连接都掐断
			for _, prev := range current.Prev { //先往前找
				tmp := current
				tmpPrev := prev
				for tmpPrev.Table.Type != common2.NODE_TYPE_GATEWAY && len(tmpPrev.Prev) > 0 { //往下找，直到碰到一个gateway或走到结束
					tmp = tmpPrev
					delete(nodeMap, tmpPrev.Table.Uuid) //删除这个节点
					op.ctx.Log().Debugln("cut", tmpPrev.Table.Uuid, tmpPrev.Table.Name,
						"because gateway", current.Table.Uuid,
						"is discard, and this node is its prev node")
					tmpPrev = tmpPrev.Prev[0]
				}
				//如果最终tmpPrev找到了一个gateway
				if tmpPrev.Table.Type == common2.NODE_TYPE_GATEWAY {
					nextIndexes := getOutputIndexesOfNextNode(tmpPrev, tmp.Table.Uuid)
					if len(nextIndexes) > 0 { //从这个gateway的后序分支里移除这条分支
						var newTmpPrevGDetail engine.GatewayDetail
						common.JsonStringDecode(tmpPrev.Table.Detail, &newTmpPrevGDetail) //这里不用处理错误，因为如果有问题，在do的时候就失败了
						tmpPrev.Next = append(tmpPrev.Next[:nextIndexes[0]], tmpPrev.Next[nextIndexes[0]+1:]...)
						tmpPrev.Passes = append(tmpPrev.Passes[:nextIndexes[0]], tmpPrev.Passes[nextIndexes[0]+1:]...)
						if len(newTmpPrevGDetail.OutRule) > nextIndexes[0] { //修改输出condition
							newTmpPrevGDetail.OutRule = append(newTmpPrevGDetail.OutRule[:nextIndexes[0]],
								newTmpPrevGDetail.OutRule[nextIndexes[0]+1:]...)
						}
						dBytes, _ := common.JsonEncode(newTmpPrevGDetail)
						tmpPrev.Table.Detail = string(dBytes)
						//记录，之后再对这个gateway递归调用cut
						needToRecur[tmpPrev.Table.Uuid] = tmpPrev
					}
				}
			}
		}
		//再往后找
		var newNext []*NodeRecord //用于记录修改后的Next等数据，**只在finish的情况下有作用**
		var newPasses []int
		var newCurrentGDetail engine.GatewayDetail
		var newOutRules []engine.OutputRule
		common.JsonStringDecode(current.Table.Detail, &newCurrentGDetail) //这里不用处理错误，因为如果有问题，在do的时候就失败了
		for idx, next := range current.Next {
			if current.State == common2.STATE_FINISHED &&
				current.Passes[idx] != NOT_PASS { //如果此gateway是完成状态，则需要修改此gateway的信息
				newPasses = append(newPasses, current.Passes[idx])
				newNext = append(newNext, next)
				if len(newCurrentGDetail.OutRule) > idx {
					newOutRules = append(newOutRules, newCurrentGDetail.OutRule[idx])
				}
				needToRecur[next.Table.Uuid] = next
				continue
			}
			tmp := current
			tmpNext := next
			for tmpNext.Table.Type != common2.NODE_TYPE_GATEWAY && len(tmpNext.Next) > 0 { //往下找，直到碰到一个gateway或走到结束
				tmp = tmpNext
				delete(nodeMap, tmpNext.Table.Uuid) //删除这个节点
				op.ctx.Log().Debugln("cut", tmpNext.Table.Uuid, tmpNext.Table.Name, "because gateway", current.Table.Uuid,
					"is not pass, and this node is one of its next node")
				tmpNext = tmpNext.Next[0]
			}
			//如果最终tmpNext找到了一个gateway
			if tmpNext.Table.Type == common2.NODE_TYPE_GATEWAY {
				prevIndexes := getInputIndexesOfPrevNode(tmpNext, tmp.Table.Uuid)
				if len(prevIndexes) > 0 { //从这个gateway的前序分支里移除这条分支
					tmpNext.Prev = append(tmpNext.Prev[:prevIndexes[0]], tmpNext.Prev[prevIndexes[0]+1:]...)
				}
				//记录，之后再对这个gateway递归调用cut
				needToRecur[tmpNext.Table.Uuid] = tmpNext
			}
		}
		if current.State == common2.STATE_FINISHED { //如果是完成状态，需要更新本gateway的信息
			//更新current的next和current的detail
			newCurrentGDetail.OutRule = newOutRules
			dBytes, _ := common.JsonEncode(newCurrentGDetail)
			current.Table.Detail = string(dBytes)
			current.Next = newNext
			current.Passes = newPasses
			//这时，如果此gateway只剩下一个前序节点和一个后续节点，且还是一定通过的话，则这个gateway已经没必要存在了
			if len(current.Prev) == 1 && len(current.Next) == 1 && current.Passes[0] == ABSOLUTELY_PASS {
				nextIdxes := getOutputIndexesOfNextNode(current.Prev[0], current.Table.Uuid)
				for _, nextIdx := range nextIdxes {
					current.Prev[0].Next[nextIdx] = current.Next[0]
				}
				prevIdxes := getInputIndexesOfPrevNode(current.Next[0], current.Table.Uuid)
				for _, prevIdx := range prevIdxes {
					current.Next[0].Prev[prevIdx] = current.Prev[0]
				}
				//然后直接把本gateway删除
				delete(nodeMap, current.Table.Uuid)
				op.ctx.Log().Debugln("cut", current.Table.Uuid, current.Table.Name, "because it's a gateway and it has only one prev and one next")
			}
		} else { //否则直接把本gateway删除
			delete(nodeMap, current.Table.Uuid)
			op.ctx.Log().Debugln("cut", current.Table.Uuid, current.Table.Name, "because it's a discard gateway")
		}
		for _, n := range needToRecur { //最后递归调用
			op.cut(n, nodeMap)
		}
	}
}

func tidy(oldTopo *engine.WorkflowDetail, oldNodes []*wModel.NodeTemplateTable, nodeMap map[string]*NodeRecord) (
	newTopo *engine.WorkflowDetail, newNodes []*wModel.NodeTemplateTable) {
	//组装newNodes
	for _, n := range oldNodes {
		if record, ok := nodeMap[n.Uuid]; ok {
			newNodes = append(newNodes, record.Table)
		}
	}
	newTopo = &engine.WorkflowDetail{
		RemoveDuplicate: oldTopo.RemoveDuplicate,
	}
	//组装newTopo
	for _, t := range oldTopo.Nodes {
		if record, ok := nodeMap[t.TemplateUuid]; ok {
			newDetail := &engine.TopologyDetail{
				TemplateUuid: record.Table.Uuid,
				Type:         record.Table.Type,
			}
			for _, next := range record.Next {
				newDetail.Next = append(newDetail.Next, &engine.TopologyDetail{
					TemplateUuid: next.Table.Uuid,
					Type:         next.Table.Type,
				})
			}
			for _, prev := range record.Prev {
				newDetail.Prev = append(newDetail.Prev, &engine.TopologyDetail{
					TemplateUuid: prev.Table.Uuid,
					Type:         prev.Table.Type,
				})
			}
			newTopo.Nodes = append(newTopo.Nodes, newDetail)
		}
	}
	return newTopo, newNodes
}

func judgeIfAllNotPass(passes []int) bool {
	var existPass bool //标识有分支已经pass了
	for _, p := range passes {
		if p != NOT_PASS {
			existPass = true
		}
	}
	return !existPass
}

func judgeIfAllNotAbsolutelyPass(passes []int) bool {
	var existPass bool //标识有分支已经pass了
	for _, p := range passes {
		if p == ABSOLUTELY_PASS {
			existPass = true
		}
	}
	return !existPass
}

// 通过gateway中设定的condition判断是否能够通过
// writableFields中是当前节点中可能被审批人修改的字段名，若condition中有以这些字段名作判断的条件，则无偿认为是有可能通过的 （TODO 优化）
func judgeIfPossibleToPass(ctx common.HContextIface, idx int, rules []engine.OutputRule, writableFields []string, params map[string]interface{}) (int, error) {
	if len(rules) > idx && rules[idx].Else {
		return NOT_PASS, nil
	}
	if len(rules) <= idx || len(rules[idx].Condition) == 0 {
		return ABSOLUTELY_PASS, nil
	}
	expr, err := engine.InitExpression(ctx, rules[idx].Condition)
	if err != nil {
		return NOT_PASS, err
	}
	if len(writableFields) > 0 { //若有可写的field，则需要判断是否存在于condition中
		wMap := make(map[string]bool, len(writableFields))
		for _, f := range writableFields {
			wMap[f] = true
		}
		vNames, err := engine.GetVariableNamesInExpression(expr)
		if err != nil {
			return NOT_PASS, err
		}
		for _, vName := range vNames {
			if wMap[vName] {
				return MAY_PASS, nil
			}
		}
	}
	p, _ := engine.ValuateExpression(rules[idx].Condition, expr, params) //这里忽略错误直接不通过，因为真正运行时也会出同样的错
	return func() int {
		if p {
			return ABSOLUTELY_PASS
		} else {
			return NOT_PASS
		}
	}(), nil
}

func mergeTwoStringSlice(s1 []string, s2 []string) (ret []string) {
	if len(s1) == 0 {
		return s2
	} else if len(s2) == 0 {
		return s1
	}
	m := make(map[string]bool)
	for _, s := range s1 {
		m[s] = true
	}
	for _, s := range s2 {
		m[s] = true
	}
	for s := range m {
		ret = append(ret, s)
	}
	return ret
}

func getOutputIndexesOfNextNode(n *NodeRecord, nextUuid string) (indexes []int) {
	for idx, next := range n.Next {
		if next.Table.Uuid == nextUuid {
			indexes = append(indexes, idx)
		}
	}
	return indexes
}

func getInputIndexesOfPrevNode(n *NodeRecord, prevUuid string) (indexes []int) {
	for idx, prev := range n.Prev {
		if prev.Table.Uuid == prevUuid {
			indexes = append(indexes, idx)
		}
	}
	return indexes
}
