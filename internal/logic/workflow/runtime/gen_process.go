/**
 * @note
 * 创建workflow前就生成所有审批人
 *
 * <AUTHOR>
 * @date 	2020-04-28
 */
package runtime

import (
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/internal/logic/form"
	"gitlab.docsl.com/security/bpm/internal/logic/staff"
	engine2 "gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/common/structures/sets"
)

func (op *WorkflowOperator) preGenerateProcessesByNodeDetail(workflowName, workflowDesc string,
	nodeTables []*workflowModel.NodeTable,
	approvers []*SelfSelectStaffInfo, notifiers []*SelfSelectStaffInfo, data interface{}) (procTables []*processModel.ProcessTable, err error) {
	approverProcTables, err := op.preGenerateApproverByNodeDetail(workflowName,
		workflowDesc, nodeTables, approvers, data)
	if err != nil {
		return nil, err
	}
	notifierProcTables, err := op.preGenerateNotifierByNodeDetail(workflowName, workflowDesc,
		nodeTables, notifiers, data)
	if err != nil { //这里只打一行日志
		op.Ctx().Log().Errorf("GetNotifierUserInfos error [%v]", err)
	}
	return append(approverProcTables, notifierProcTables...), nil
}

func (op *WorkflowOperator) preGenerateApproverByNodeDetail(workflowName, workflowDesc string,
	nodeTables []*workflowModel.NodeTable,
	approvers []*SelfSelectStaffInfo, data interface{}) (allProcTables []*processModel.ProcessTable, err error) {

	staffOperator := staff.NewStaffOperator(op.ctx)

	for _, n := range nodeTables {

		if n.Type != common2.NODE_TYPE_APPROVAL && n.Type != common2.NODE_TYPE_TRANSACTOR { //只处理审批和办理节点
			continue
		}

		var procTables []*processModel.ProcessTable
		d := engine2.ApprovalTaskDetail{}
		if err := common.JsonDecode([]byte(n.Detail), &d); err != nil {
			return nil, err
		}
		if len(d.StaffFromForm) > 0 { //表单里的联系人
			formOperator := form.NewFormOperator(op.Ctx())
			d.Staff = formOperator.ExtractStaffsFromForm(d.StaffFromForm, data)
		}
		if d.SelfSelect { //自选审批人的情况
			d.Staff = d.Staff[0:0]               //为防原模板有Staff配置（错误），这里清空此项
			for _, approver := range approvers { //找到用户自选的审批人
				if approver.NodeTemplateUuid == n.TemplateUuid {
					d.Staff = approver.Staff
					break
				}
			}
			if len(d.Staff) == 0 {
				return nil, common2.ErrMissingSelfSelectApprovers
			}
		}

		var hasApprover bool  //标志是否能查到有效的审批人
		if len(d.Staff) > 0 { //查找此节点的审批人信息
			if staffInfos, err := staffOperator.GetStaffUserInfos(d.Staff); err != nil && len(staffInfos) == 0 {
				op.Ctx().Log().Errorf("GetStaffUserInfos error [%v]", err)
			} else if curProcTables := op.genApproverProcTablesByUserInfos(workflowName, workflowDesc,
				n, d, staffInfos); len(curProcTables) > 0 {
				procTables = append(procTables, curProcTables...)
				hasApprover = true //标志此审批节点找到了审批人
			}
		}

		if !hasApprover { //找不到审批人，emptyStaff开始发挥作用
			d.IsEmpty = true //标记此节点触发了审批人为空的转交机制
			if d.EmptyMode != common.StringEmpty {
				d.Mode = d.EmptyMode //为空，走为空的审批模式，直接替换
			}
			if len(d.EmptyStaff) > 0 { //如果此节点设置为自动通过，则忽略
				if staffInfos, err := staffOperator.GetStaffUserInfos(d.EmptyStaff); err != nil && len(staffInfos) == 0 {
					op.Ctx().Log().Errorf("GetEmptyStaffUserInfos error [%v]", err)
					return nil, common2.ErrCannotFindApprovers //这里不能再自动通过了
				} else if curProcTables := op.genApproverProcTablesByUserInfos(workflowName, workflowDesc,
					n, d, staffInfos); len(curProcTables) == 0 {
					return nil, common2.ErrCannotFindApprovers
				} else {
					procTables = append(procTables, curProcTables...)
				}
			}
		}

		//去重
		set := sets.HashSet{}
		var removeDupProcTables []*processModel.ProcessTable
		for _, pt := range procTables {
			if !set.Contains(pt.UserId) {
				removeDupProcTables = append(removeDupProcTables, pt)
				set.AddInt64(pt.UserId)
			}
		}
		procTables = removeDupProcTables
		if newDetail, err := common.JsonStringEncode(d); err != nil { //为节点生成新的detail
			return nil, err
		} else {
			n.Detail = newDetail
		}
		allProcTables = append(allProcTables, procTables...)
	}

	return allProcTables, nil
}

func (op *WorkflowOperator) genApproverProcTablesByUserInfos(workflowName, workflowDesc string,
	n *workflowModel.NodeTable, d engine2.ApprovalTaskDetail, userInfos []*staff.UserInfo) (
	procTables []*processModel.ProcessTable) {
	var order int

	for _, userInfo := range userInfos { //判断没有的userId就进行插入
		if userInfo.UserId == 0 { //忽略userId=0的特殊情况
			continue
		}
		pDetail := processModel.ApprovalProcessDetail{
			Mode:   d.Mode,
			Notify: d.Notify,
			Order:  order,
		}
		pDetailStr, err := common.JsonStringEncode(pDetail)
		if err != nil {
			op.Ctx().Log().Errorln("Encode process detail err", err)
			continue
		}
		if d.Mode == common2.APPROVAL_MODE_INTURN_SIGN { //依次审批，需要指定审批顺序
			order++
		}

		pTable := &processModel.ProcessTable{
			UserId:       userInfo.UserId,
			Uuid:         common.NewUuid(common.RESOURCE_TYPE_PROCESS),
			WorkflowUuid: n.ParentUuid,
			NodeUuid:     n.Uuid,
			Detail:       pDetailStr,
			Data:         common.StringJsonEmpty,
			Status:       common2.STATUS_NORMAL,
			State:        common2.PROCESS_STATE_INITIALIZED,
			Type: func() common2.ProcessTypeEnum {
				var processType common2.ProcessTypeEnum
				if n.Type == common2.NODE_TYPE_TRANSACTOR {
					processType = common2.PROCESS_TYPE_TRANSACTOR
				} else if n.Type == common2.NODE_TYPE_APPROVAL {
					processType = common2.PROCESS_TYPE_APPROVAL
				}
				return processType
			}(),
			Tag:         common2.PORTAL_BPM_TASK,
			Origin:      common2.OriginBpm,
			InitiatorId: op.Ctx().User().UserId,
			Title:       workflowName,
			Desc:        workflowDesc,
		}
		procTables = append(procTables, pTable)
	}

	return procTables
}

func (op *WorkflowOperator) preGenerateNotifierByNodeDetail(workflowName, workflowDesc string,
	nodeTables []*workflowModel.NodeTable, notifiers []*SelfSelectStaffInfo, data interface{}) (procTables []*processModel.ProcessTable, err error) {
	staffOperator := staff.NewStaffOperator(op.ctx)
	for _, n := range nodeTables {
		if n.Type != common2.NODE_TYPE_NOTIFY { //只处理抄送的
			continue
		}
		d := engine2.NotifyTaskDetail{}
		if err := common.JsonDecode([]byte(n.Detail), &d); err != nil {
			return nil, err
		}
		if len(d.StaffFromForm) > 0 { //表单里的联系人
			formOperator := form.NewFormOperator(op.Ctx())
			d.Staff = formOperator.ExtractStaffsFromForm(d.StaffFromForm, data)
		}
		if d.SelfSelect { //自选审批人的情况
			d.Staff = d.Staff[0:0]               //为防原模板有Staff配置（错误），这里清空此项
			for _, notifier := range notifiers { //找到用户自选的审批人
				if notifier.NodeTemplateUuid == n.TemplateUuid {
					d.Staff = notifier.Staff
					break
				}
			}
		}
		if len(d.Staff) > 0 { //查找此节点的审批人信息
			if staffInfos, err := staffOperator.GetStaffUserInfos(d.Staff); err != nil && len(staffInfos) == 0 {
				op.Ctx().Log().Errorf("GetStaffUserInfos error [%v]", err)
				//return nil, bpmCom.ErrCannotFindApprovers //这里不能再自动通过了
			} else if curProcTables := op.genNotifierProcTablesByUserInfos(workflowName, workflowDesc,
				n, d, staffInfos); len(curProcTables) > 0 {
				//去重
				set := sets.HashSet{}
				var removeDupProcTables []*processModel.ProcessTable
				for _, pt := range curProcTables {
					if !set.Contains(pt.UserId) {
						removeDupProcTables = append(removeDupProcTables, pt)
						set.AddInt64(pt.UserId)
					}
				}
				procTables = append(procTables, removeDupProcTables...)
			}
		}
		if len(procTables) == 0 {
			d.IsEmpty = true
		}
		if newDetail, err := common.JsonStringEncode(d); err != nil { //为节点生成新的detail
			return nil, err
		} else {
			n.Detail = newDetail
		}
	}
	return procTables, nil
}

func (op *WorkflowOperator) genNotifierProcTablesByUserInfos(workflowName, workflowDesc string,
	n *workflowModel.NodeTable, d engine2.NotifyTaskDetail, userInfos []*staff.UserInfo) (
	procTables []*processModel.ProcessTable) {
	for _, userInfo := range userInfos { //判断没有的userId就进行插入
		if userInfo.UserId == 0 { //忽略userId=0的特殊情况
			continue
		}
		pDetail := processModel.NotifyProcessDetail{
			Notify: d.Notify,
		}
		pDetailStr, err := common.JsonStringEncode(pDetail)
		if err != nil {
			op.Ctx().Log().Errorln("Encode process detail err", err)
			continue
		}
		pTable := &processModel.ProcessTable{
			UserId:       userInfo.UserId,
			Uuid:         common.NewUuid(common.RESOURCE_TYPE_PROCESS),
			WorkflowUuid: n.ParentUuid,
			NodeUuid:     n.Uuid,
			Detail:       pDetailStr,
			Data:         common.StringJsonEmpty,
			Status:       common2.STATUS_NORMAL,
			State:        common2.PROCESS_STATE_INITIALIZED,
			Type:         common2.PROCESS_TYPE_NOTIFY,
			Tag:          common2.PORTAL_BPM_TASK,
			Origin:       common2.OriginBpm,
			InitiatorId:  op.Ctx().User().UserId,
			Title:        workflowName,
			Desc:         workflowDesc,
		}
		procTables = append(procTables, pTable)
	}
	return procTables
}
