package runtime

import (
	"encoding/json"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wCommon "gitlab.docsl.com/security/bpm/internal/logic/workflow/common"
	"gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

type WorkflowOperator struct {
	ctx common.HContextIface
}

func (op *WorkflowOperator) Ctx() common.HContextIface {
	return op.ctx
}

func NewWorkflowOperator(hctx common.HContextIface) *WorkflowOperator {
	return &WorkflowOperator{ctx: hctx}
}

type WorkflowListCondition struct {
	WorkflowTemplateUuid     string   `json:"workflowTemplateUuid"`
	WorkflowTemplateUuids    []string `json:"workflowTemplateUuids"`
	WorkflowTemplateGroupId  int64    `json:"workflowTemplateGroupId"`
	WorkflowTemplateGroupIds []int64  `json:"workflowTemplateGroupIds"`
	WorkflowUuids            []string `json:"workflowUuids"`
	WorkflowSerialIds        []string `json:"workflowSerialIds"`
	WorkflowName             string   `json:"workflowName"`
	UserName                 string   `json:"userName"` //流程归档时有用
	TagIds                   []int64  `json:"tagIds"`
	NoneTag                  bool     `json:"noneTag"`
	State                    string   `json:"state"`
	States                   []string `json:"states"`
	StartTime                int64    `json:"startTime,omitempty" validate:"gte=0"`
	EndTime                  int64    `json:"endTime,omitempty" validate:"gte=0"`
	UpdateStartTime          int64    `json:"updateStartTime,omitempty" validate:"gte=0"`
	UpdateEndTime            int64    `json:"updateEndTime,omitempty" validate:"gte=0"`
}

type WorkflowInfo struct {
	WorkflowUuid          string                        `json:"workflowUuid,omitempty"`
	WorkflowSerialId      int64                         `json:"workflowSerialId,omitempty,string"`
	CurUrgeInfo           *UrgeInfo                     `json:"curUrgeInfo,omitempty"`
	WorkflowTemplateUuid  string                        `json:"workflowTemplateUuid,omitempty"`
	WorkflowTemplateGroup *wCommon.GroupInfoForTmplInfo `json:"workflowTemplateGroup,omitempty"`
	User                  *staffLogic.UserInfo          `json:"user,omitempty"`
	WorkflowName          json.RawMessage               `json:"workflowName,omitempty"`
	Description           json.RawMessage               `json:"description,omitempty"`
	CreateTime            int64                         `json:"createTime,omitempty"`
	UpdateTime            int64                         `json:"updateTime,omitempty"`
	State                 bpmCom.WorkflowStateEnum      `json:"state,omitempty"`
	Edition               string                        `json:"-"`
	TagIds                []int64                       `json:"-"`
	Tags                  []*WorkflowTemplateTagInfo    `json:"tags"`
}

type WorkflowTemplateTagInfo struct {
	Id      int64  `json:"id,omitempty"`
	TagName string `json:"tagName,omitempty"`
}

type UrgeInfo struct {
	ProcessUuids []string               `json:"processUuids"`
	CurApprovers []*staffLogic.UserInfo `json:"curApprovers"`
	LastUrgeTime int64                  `json:"lastUrgeTime"`
	Interact     bool                   `json:"interact"`
}

type WorkflowDetailInfo struct {
	WorkflowUuid          string                        `json:"workflowUuid,omitempty"`
	WorkflowName          json.RawMessage               `json:"workflowName,omitempty"`
	Description           json.RawMessage               `json:"description,omitempty"`
	WorkflowSerialId      int64                         `json:"workflowSerialId,omitempty,string"`
	WorkflowTemplateUuid  string                        `json:"workflowTemplateUuid,omitempty"`
	WorkflowTemplateGroup *wCommon.GroupInfoForTmplInfo `json:"workflowTemplateGroup,omitempty"`
	User                  *staffLogic.UserInfo          `json:"user,omitempty"`
	Nodes                 []*NodeRuntimeInfo            `json:"nodes,omitempty"`
	RemoveDuplicate       bool                          `json:"removeDuplicate"`
	Form                  interface{}                   `json:"form,omitempty"`
	State                 bpmCom.WorkflowStateEnum      `json:"state,omitempty"`
	CreateTime            int64                         `json:"createTime,omitempty"`
	UpdateTime            int64                         `json:"updateTime,omitempty"`
}

type NodeRuntimeInfo struct {
	Next           []*engine.TopologyDetail   `json:"next,omitempty"`
	Type           bpmCom.NodeTypeEnum        `json:"type,omitempty"`
	Uuid           string                     `json:"uuid"`
	State          bpmCom.WorkflowStateEnum   `json:"state,omitempty"`
	Name           json.RawMessage            `json:"name,omitempty"`
	Detail         json.RawMessage            `json:"detail,omitempty"`
	Data           interface{}                `json:"data,omitempty"`
	DataHistory    []*wCommon.DataHistoryInfo `json:"dataHistory,omitempty"`
	FormPermission *formLogic.FormPermission  `json:"formPermission,omitempty"`
	Extra          *NodeExtraInfo             `json:"extra,omitempty"`
	CreateTime     int64                      `json:"createTime,omitempty"`
	UpdateTime     int64                      `json:"updateTime,omitempty"`
}

type NodeExtraInfo struct {
	Processes []*NodeProcessInfo     `json:"processes,omitempty"`
	Interact  *InteractInfo          `json:"interact,omitempty"`
	Complete  []*engine.CompleteInfo `json:"complete,omitempty"`
}

type InteractInfo struct {
	Comment string `json:"comment"`
}

type NodeProcessInfo struct {
	ProcessUuid              string                            `json:"processUuid"`
	Type                     bpmCom.ProcessTypeEnum            `json:"type,omitempty"`
	User                     *staffLogic.UserInfo              `json:"user,omitempty"`
	State                    bpmCom.ProcessStateEnum           `json:"state"`
	Comment                  string                            `json:"comment,omitempty"`
	RollbackNodeName         json.RawMessage                   `json:"rollbackNodeName,omitempty"`
	HandoverExplain          string                            `json:"handoverExplain,omitempty"`
	CountersignBeforeExplain string                            `json:"countersignBeforeExplain,omitempty"`
	CountersignAfterExplain  string                            `json:"countersignAfterExplain,omitempty"`
	Remark                   []*processModel.ProcessRemarkInfo `json:"remark,omitempty"`
	SubProcess               []*NodeProcessInfo                `json:"subProcess,omitempty"` //由此审批人创建的转交/加签任务，仅当此任务有转交/加签子任务时存在
	SubType                  bpmCom.ProcessSubTypeEnum         `json:"subType,omitempty"`    //"Handover"表示此任务是一个被转交的任务，"CountersignBefore"表示是一个加签（前置审批）的审批任务，"CountersignAfter"表示是一个加签（后置审批）的审批任务，仅当此任务为一个转交/加签子任务时存在
	CreateTime               int64                             `json:"createTime"`
	UpdateTime               int64                             `json:"updateTime"`
}

type SelfSelectStaffInfo struct {
	NodeTemplateUuid string                  `json:"nodeTemplateUuid"`
	Staff            []*staffLogic.StaffInfo `json:"staff"`
}
