package runtime

import (
	"encoding/json"
	interCommon "gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	notifyLogic "gitlab.docsl.com/security/bpm/internal/logic/notify"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wCommon "gitlab.docsl.com/security/bpm/internal/logic/workflow/common"
	"gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"sort"
	"strings"
	"time"

	"github.com/jianfengye/collection"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/common/structures/sets"
)

/* @note
 * 4-26 为了归档接口增加一个archive参数
 */
func (op *WorkflowOperator) ListWorkFlow(condition WorkflowListCondition, offset,
	limit int64, archive bool) (workflowInfos []*WorkflowInfo, count int64, err error) {
	m := make(map[string]interface{})
	m["workflow_runtime.status"] = interCommon.STATUS_NORMAL
	if condition.WorkflowTemplateUuid != common.StringEmpty {
		condition.WorkflowTemplateUuids = append(condition.WorkflowTemplateUuids, condition.WorkflowTemplateUuid)
	}
	staffOperator := staffLogic.NewStaffOperator(op.ctx)
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, 0, err
	}
	//流程归档中，输入用户名称查询的能力
	var userIds []int64
	if archive && condition.UserName != common.StringEmpty {
		userIds, err = staffOperator.GetUserIdsByUserNameZhVague(condition.UserName)
		if err != nil || len(userIds) == 0 {
			return nil, 0, err
		}
	}
	workflowTables, totalCount, err := wModel.QueryUserWorkflowRuntimeByWhereMap(m, condition.WorkflowUuids,
		condition.WorkflowSerialIds, condition.WorkflowName,
		condition.WorkflowTemplateUuids, condition.WorkflowTemplateGroupIds,
		condition.StartTime,
		condition.EndTime,
		condition.UpdateStartTime,
		condition.UpdateEndTime, condition.States,
		condition.TagIds, condition.NoneTag, userIds,
		offset, limit, archive)
	if err != nil {
		return nil, 0, err
	}

	//为了批量查询process用于填充
	var workflowUuids []string
	//为了批量查询模板组用于填充
	var groupIds []int64
	var tmplUuids []string

	var self *staffLogic.UserInfo //在c端列表，需要填入发起人信息
	for _, workflowTable := range workflowTables {
		w := &WorkflowInfo{
			WorkflowUuid:         workflowTable.Uuid,
			WorkflowSerialId:     workflowTable.SerialId,
			WorkflowTemplateUuid: workflowTable.TemplateUuid,
			CreateTime:           workflowTable.CreateTime,
			UpdateTime:           workflowTable.UpdateTime,
			State:                workflowTable.State,
			Edition:              workflowTable.Edition,
			Tags:                 []*WorkflowTemplateTagInfo{},
		}
		if archive { //归档页面需要提供每个流程的发起人信息
			user, err := staffOperator.GetUserInfoByUserId(workflowTable.UserId)
			if err != nil {
				op.ctx.Log().Errorf("QueryUserInfoByUserId[%d] err[%v]", workflowTable.UserId, err)
				user = &staffLogic.UserInfo{UserId: workflowTable.UserId}
			}
			user.ParentDept, err = staffOperator.GetParentDeptInfoByUserId(workflowTable.UserId)
			if err != nil {
				op.ctx.Log().Errorf("QueryUserParentDeptInfoByUserId[%d] err[%v]", workflowTable.UserId, err)
			}
			user.Email = common.StringEmpty //抹去邮箱信息
			user.Phone = common.StringEmpty //抹去手机信息
			w.User = user
		} else { //否则是正常列表，所有流程的user都是同一个，这里统一处理
			if self == nil {
				self, err = staffOperator.GetUserInfoByUserId(workflowTable.UserId)
				if err != nil {
					op.ctx.Log().Errorf("QueryUserInfoByUserId[%d] err[%v]", workflowTable.UserId, err)
					self = &staffLogic.UserInfo{UserId: workflowTable.UserId}
				}
				self.ParentDept, err = staffOperator.GetParentDeptInfoByUserId(workflowTable.UserId)
				if err != nil {
					op.ctx.Log().Errorf("QueryUserParentDeptInfoByUserId[%d] err[%v]", workflowTable.UserId, err)
				}
				self.Email = common.StringEmpty //抹去邮箱信息
				self.Phone = common.StringEmpty //抹去手机信息
			}
			w.User = self
		}
		if workflowTable.GroupId > 0 {
			w.WorkflowTemplateGroup = &wCommon.GroupInfoForTmplInfo{
				WorkflowTemplateGroupId: workflowTable.GroupId,
			}
			groupIds = append(groupIds, workflowTable.GroupId)
		}
		if w.WorkflowName, err = common.JsonStringToRaw(workflowTable.Name); err != nil {
			return nil, 0, err
		}
		if w.Description, err = common.JsonStringToRaw(workflowTable.Description); err != nil {
			op.Ctx().Log().Errorln(err)
		}
		workflowInfos = append(workflowInfos, w)
		workflowUuids = append(workflowUuids, workflowTable.Uuid)
		tmplUuids = append(tmplUuids, workflowTable.TemplateUuid)
	}

	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, 0, err
	}

	processTables, err := pModel.QueryProcessRuntimeByWorkflowUuidsAndStates(workflowUuids,
		[]interCommon.ProcessStateEnum{interCommon.PROCESS_STATE_UNAPPROVED, interCommon.PROCESS_STATE_UNDONE, interCommon.PROCESS_STATE_POSTED})

	workflowUuidProcessTablesMap := make(map[string][]*processModel.ProcessTable)
	for _, t := range processTables {
		workflowUuidProcessTablesMap[t.WorkflowUuid] = append(workflowUuidProcessTablesMap[t.WorkflowUuid], t)
	}

	//tag
	tagInfoMap, err := wModel.QueryTemplateTagsInfoByUuid(tmplUuids)
	if err != nil {
		return nil, 0, err
	}

	//查询模板组数据
	groupInfos, err := wModel.QueryTemplateGroupByGroupIds(groupIds)
	if err != nil {
		op.ctx.Log().Errorf("QueryGroupInfosByGroupIds[%v] err[%v]", groupIds, err)
	}
	groupIdNameMap := make(map[int64]json.RawMessage)
	for _, groupInfo := range groupInfos {
		if name, err := common.JsonStringToRaw(groupInfo.Name); err != nil {
			return nil, 0, err
		} else {
			groupIdNameMap[groupInfo.ID] = name
		}
	}

	for _, info := range workflowInfos {
		if info.WorkflowTemplateGroup != nil {
			info.WorkflowTemplateGroup.Name = groupIdNameMap[info.WorkflowTemplateGroup.WorkflowTemplateGroupId]
		}
		//填充催办的信息
		urgeInfo := &UrgeInfo{}
		var maxTimestamp int64
		if ps, ok := workflowUuidProcessTablesMap[info.WorkflowUuid]; !ok {
			urgeInfo.ProcessUuids = make([]string, 0)
			urgeInfo.CurApprovers = make([]*staffLogic.UserInfo, 0)
			urgeInfo.LastUrgeTime = 0
		} else {
			userIdCollection := collection.NewInt64Collection(nil)
			for _, p := range ps {
				if p.Type == interCommon.PROCESS_TYPE_INTERACT {
					urgeInfo.Interact = true //外部系统
					continue
				}
				userInfo, err := staffOperator.GetUserInfoByUserId(p.UserId)
				if err != nil {
					op.Ctx().Log().Errorf("get userInfo [%d] err [%v]", p.UserId, err)
					userInfo = &staffLogic.UserInfo{
						UserId: p.UserId,
					}
				}
				userInfo.Email = common.StringEmpty //抹掉email
				userInfo.Phone = common.StringEmpty //抹去手机信息
				processData := &processModel.ApprovalProcessData{}
				if err := common.JsonDecode([]byte(p.Data), &processData); err != nil {
					return nil, 0, err
				}
				if !userIdCollection.Contains(p.UserId) { //去重
					userIdCollection.Append(p.UserId)
					urgeInfo.CurApprovers = append(urgeInfo.CurApprovers, userInfo)
				}
				urgeInfo.ProcessUuids = append(urgeInfo.ProcessUuids, p.Uuid)
				if processData.LastUrgeTime > maxTimestamp {
					maxTimestamp = processData.LastUrgeTime
				}
				urgeInfo.LastUrgeTime = maxTimestamp
			}
		}

		info.CurUrgeInfo = urgeInfo

		if tags, ok := tagInfoMap[info.WorkflowTemplateUuid][info.Edition]; ok {
			for _, t := range tags {
				info.Tags = append(info.Tags, &WorkflowTemplateTagInfo{
					Id:      t.ID,
					TagName: t.TagName,
				})
			}
		}
	}

	if workflowInfos == nil {
		workflowInfos = make([]*WorkflowInfo, 0)
	}

	return workflowInfos, totalCount, nil
}

func (op *WorkflowOperator) WithdrawWorkflow(workflowUuids []string) error {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	workflowTables, err := wModel.QueryWorkflowRuntimeByUuids(workflowUuids)
	if err != nil {
		return err
	}
	for _, t := range workflowTables {
		if op.Ctx().User().UserId != t.UserId {
			return common.ErrNoPermission
		} else if t.State == interCommon.STATE_DISCARD ||
			t.State == interCommon.STATE_FINISHED ||
			t.State == interCommon.STATE_FAILED {
			return interCommon.ErrWorkflowState
		}
	}
	//撤销workflow
	err = wModel.WithdrawWorkflowRuntime(workflowUuids)
	if err != nil {
		return err
	}
	//撤销node
	err = wModel.WithdrawNodeRuntimeByWorkflowUuids(workflowUuids...)
	if err != nil {
		return err
	}
	//删除process
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return err
	}
	_, err = pModel.SetProcessRuntimeExpiredByWorkflowUuids(workflowUuids...)
	//通知所有已做过审批操作的人
	notifyOperator := notifyLogic.NewNotifyOperator(op.ctx)
	for _, workflowTable := range workflowTables {
		processes, err := pModel.QueryProcessRuntimeByWorkflowUuid(workflowTable.Uuid)
		if err != nil {
			op.Ctx().Log().Errorln("withdraw workflow: query process runtime error:", err)
		} else {
			//统计需要同时通知的人
			needNotifyProcesses := engine.SortStakeholderProcessToNotify(processes, common.StringEmpty, []int64{workflowTable.UserId}, false)
			workflowName, initiatorName := engine.GetWorkflowNameAndUserName(op.Ctx(), workflowTable)
			for _, proc := range needNotifyProcesses {
				//给退回途径中的人发消息
				emailContent, err := engine.GenerateEmailContentByTmpl(workflowName, initiatorName,
					engine.WITHDRAW_NOTIFY_OTHERS_EMAIL_TMPL, engine.GetToCProcessUrl(workflowTable.Uuid), nil)
				if err != nil {
					emailContent = engine.GenerateEmailWithdrawNotifyOthersContent(workflowTable.Uuid)
				}
				if errNotify := notifyOperator.SendNotify(proc.UserId, notifyLogic.NotifyMsg{
					LarkContent: notifyLogic.GenerateLarkWithdrawNotifyOthersContent(workflowName, initiatorName, proc.Uuid, workflowTable.CreateTime, notifyLogic.LarkInteractiveCustomTemplate{}, nil),
					MailTitle:   "流程撤回通知",
					MailContent: emailContent,
				}, []interCommon.NotifyTypeEnum{interCommon.NotifyTypeLark, interCommon.NotifyTypeEmail}); errNotify != nil {
					op.Ctx().Log().Errorln(errNotify)
				}
			}
		}
	}
	return err
}

/**
 * @note
 * 此方法在workflow/detail和process/detail中复用。
 * isProcess表示是在"process/detail"场景内，不需要根据commentVisible来隐藏comment
 */
func (op *WorkflowOperator) QueryWorkflowDetail(workflowUuid string, isProcess bool) (detail *WorkflowDetailInfo, err error) {
	staffOperator := staffLogic.NewStaffOperator(op.ctx)
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	detail = &WorkflowDetailInfo{
		WorkflowUuid: workflowUuid,
	}
	//查workflow
	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(workflowUuid)
	if err != nil {
		return nil, err
	}
	detail.User, err = staffOperator.GetUserInfoByUserId(workflowTable.UserId)
	if err != nil {
		op.ctx.Log().Errorf("QueryUserInfoByUserId[%d] err[%v]", workflowTable.UserId, err)
		detail.User = &staffLogic.UserInfo{
			UserId: workflowTable.UserId,
		}
	}
	//如果是代办项，把发起人的部门树信息给前端
	if detail.User.ParentDept, err = staffOperator.GetParentDeptInfoByUserId(workflowTable.UserId); err != nil {
		return nil, err
	}
	detail.User.Email = common.StringEmpty //抹去邮箱信息
	detail.User.Phone = common.StringEmpty //抹去手机信息
	detail.WorkflowSerialId = workflowTable.SerialId
	detail.WorkflowTemplateUuid = workflowTable.TemplateUuid
	detail.CreateTime = workflowTable.CreateTime
	detail.UpdateTime = workflowTable.UpdateTime
	detail.State = workflowTable.State
	detail.WorkflowSerialId = workflowTable.SerialId
	detail.WorkflowName, err = common.JsonStringToRaw(workflowTable.Name)
	if err != nil {
		return nil, err
	}
	detail.Description, err = common.JsonStringToRaw(workflowTable.Description)
	if err != nil {
		op.Ctx().Log().Errorln(err)
	}
	//填充group信息
	if workflowTable.GroupId > 0 {
		if group, err := wModel.QueryTemplateGroupByGroupId(workflowTable.GroupId); err == nil {
			if name, err := common.JsonStringToRaw(group.Name); err == nil {
				detail.WorkflowTemplateGroup = &wCommon.GroupInfoForTmplInfo{
					Name:                    name,
					WorkflowTemplateGroupId: workflowTable.GroupId,
				}
			}
		}
	}
	//转换拓扑
	topo := &engine.WorkflowDetail{}
	err = common.JsonDecode([]byte(workflowTable.Detail), &topo)
	detail.RemoveDuplicate = topo.RemoveDuplicate
	if err != nil {
		return nil, err
	}
	//查node
	nodeTables, err := wModel.QueryNodeRuntimeByParentUuid(workflowUuid)
	if err != nil {
		return nil, err
	}
	nodeUuidTableMap := make(map[string]*workflowModel.NodeTable)
	for _, t := range nodeTables {
		nodeUuidTableMap[t.Uuid] = t
	}
	for _, n := range topo.Nodes {
		if t, ok := nodeUuidTableMap[n.Uuid]; !ok || t == nil {
			return nil, interCommon.ErrNodeTopology
		} else {
			node := &NodeRuntimeInfo{
				Next:       n.Next,
				Uuid:       t.Uuid,
				Type:       t.Type,
				State:      t.State,
				Extra:      &NodeExtraInfo{},
				CreateTime: t.CreateTime,
				UpdateTime: t.UpdateTime,
			}
			if detail, err := common.JsonStringToRaw(t.Detail); err != nil {
				return nil, err
			} else if data, err := extractDataInput(t.Data); err != nil {
				return nil, err
			} else {
				node.Detail = detail
				node.DataHistory = wCommon.ExtractDataHistory(data)
				node.Data = wCommon.DeleteDataHistory(data) //2020-05-09 抹去data中的__history信息，不给用户展示
			}
			if node.Type == interCommon.NODE_TYPE_START {
				var startData engine.StartTaskData
				err = common.JsonStringDecode(t.Data, &startData)
				if err != nil {
					op.Ctx().Log().Errorln(err)
				} else if len(startData.Complete) > 0 {
					node.Extra.Complete = func(complete []*engine.CompleteInfo) (ret []*engine.CompleteInfo) {
						if !isProcess || op.Ctx().User().UserId == workflowTable.UserId { //如果是发起人自己查看，一定可以看
							return complete
						}
					NEXTCOMPLETE:
						for _, c := range complete {
							for _, visibleStaff := range c.VisibleStaff {
								if visibleStaff.Type == interCommon.STAFF_TYPE_ALL ||
									(visibleStaff.UserId != 0 && visibleStaff.UserId == staffOperator.Ctx().User().UserId) {
									ret = append(ret, c)
									continue NEXTCOMPLETE
								}
							}
						}
						return
					}(startData.Complete)
				}
			}
			if name, err := common.JsonStringToRaw(t.Name); err == nil {
				node.Name = name
			}
			//填充表单相关配置
			if len(t.Front) > 0 {
				f := formLogic.Front{}
				if err := common.JsonDecode([]byte(t.Front), &f); err != nil {
					return nil, err
				}
				if detail.Form == nil { //如果detail里的form还是nil，顺手赋个值 TODO 二期不能再假设所有form一致了
					detail.Form = f.Form
				}
				node.FormPermission = f.FormPermission
			}
			detail.Nodes = append(detail.Nodes, node)
		}
	}
	//拼extra
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}
	processTables, err := pModel.QueryProcessRuntimeByWorkflowUuid(workflowUuid)
	if err != nil {
		return nil, err
	}
	//拼个map方便查询
	nodeUuidProcessTablesMap := make(map[string][]*processModel.ProcessTable)
	for _, t := range processTables {
		nodeUuidProcessTablesMap[t.NodeUuid] = append(nodeUuidProcessTablesMap[t.NodeUuid], t)
	}
	//正式拼
	for _, n := range detail.Nodes {
		processes := nodeUuidProcessTablesMap[n.Uuid]
		if n.Type == interCommon.NODE_TYPE_NOTIFY { //抄送节点直接处理
			for _, process := range processes {
				n.Extra.Processes = append(n.Extra.Processes, &NodeProcessInfo{
					ProcessUuid: process.Uuid,
					Type:        process.Type,
					State:       process.State,
					User:        fillProcessUserInfoWithoutEmailAndPhoneByUserId(staffOperator, process.UserId),
					CreateTime:  process.CreateTime,
					UpdateTime:  process.UpdateTime,
				})
			}
		} else if n.Type == interCommon.NODE_TYPE_APPROVAL || n.Type == interCommon.NODE_TYPE_TRANSACTOR { //审批节点特殊处理
			var approvalNodeDetail engine.ApprovalTaskDetail
			common.JsonDecode([]byte(nodeUuidTableMap[n.Uuid].Detail), &approvalNodeDetail)
			processInfos, _, err := engine.AssembleProcessTablesToSortedProcessInfos(processes)
			if err != nil {
				return nil, err
			}
			n.Extra.Processes = assembleSortedProcessInfoToNodeProcessInfo(staffOperator, processInfos, approvalNodeDetail.CommentVisible, isProcess)
		} else if n.Type == interCommon.NODE_TYPE_INTERACT {
			n.Extra.Processes = assembleInteractProcessInfoToNodeProcessInfo(staffOperator, processes)
		}
	}
	return detail, nil
}

func fillProcessUserInfoWithoutEmailAndPhoneByUserId(staffOperator *staffLogic.StaffOperator, userId int64) *staffLogic.UserInfo {
	if user, err := staffOperator.GetUserInfoByUserIdInAllStatus(userId); err != nil {
		staffOperator.Ctx().Log().Errorf("QueryUserInfoByUserId[%d] err[%v]", userId, err)
		return &staffLogic.UserInfo{UserId: userId}
	} else {
		user.Email = common.StringEmpty //抹掉email
		user.Phone = common.StringEmpty //抹去手机信息
		return user
	}
}

func assembleInteractProcessInfoToNodeProcessInfo(staffOperator *staffLogic.StaffOperator,
	processTables []*processModel.ProcessTable) (res []*NodeProcessInfo) {
	for _, process := range processTables {
		if process.State == interCommon.PROCESS_STATE_INITIALIZED { //不显示初始化的process
			continue
		}
		var interactProcessData processModel.InteractProcessData
		err := common.JsonStringDecode(process.Data, &interactProcessData)
		if err != nil {
			staffOperator.Ctx().Log().Errorln(err)
		}
		res = append(res, &NodeProcessInfo{
			ProcessUuid: process.Uuid,
			Type:        process.Type,
			State:       process.State,
			Comment: func() string {
				if interactProcessData.CallbackResult != nil {
					return interactProcessData.CallbackResult.Comment
				}
				if !common.IsNil(interactProcessData.Result) {
					var r processModel.InteractResult
					common.InterfaceToStruct(interactProcessData.Result, &r)
					return r.Data.Comment
				}
				return common.StringEmpty
			}(),
			RollbackNodeName: interactProcessData.RollbackNodeName,
			CreateTime:       process.CreateTime,
			UpdateTime:       process.UpdateTime,
		})
	}
	if len(res) > 0 {
		sort.Slice(res, func(i, j int) bool {
			if isRollbackOrNeedComplete(res[i]) && isRollbackOrNeedComplete(res[j]) {
				return res[i].UpdateTime > res[j].UpdateTime
			} else if isRollbackOrNeedComplete(res[i]) {
				return true
			} else if isRollbackOrNeedComplete(res[j]) {
				return false
			}
			return res[i].CreateTime < res[j].CreateTime
		})
	}
	return res
}

func assembleSortedProcessInfoToNodeProcessInfo(staffOperator *staffLogic.StaffOperator, sortedInfos []*engine.SortedProcessInfo, commentVisible, isProcess bool) (res []*NodeProcessInfo) {
	if len(sortedInfos) == 0 {
		return nil
	}
	explainFunc := func(sortedInfo *engine.SortedProcessInfo, explain string, explainVisible bool) string {
		if staffOperator.Ctx().User().UserId == sortedInfo.Table.UserId { //审批人一定能看到自己发的comment
			return explain
		}
		//转交人和加签人也一定能看到comment
		for _, subInfo := range sortedInfo.SubProcess {
			if staffOperator.Ctx().User().UserId == subInfo.Table.UserId {
				return explain
			}
		}
		if !explainVisible {
			return common.StringEmpty
		}
		return explain
	}
	for _, sortedInfo := range sortedInfos {
		nodePInfo := &NodeProcessInfo{
			ProcessUuid: sortedInfo.Table.Uuid,
			Type:        sortedInfo.Table.Type,
			State:       sortedInfo.Table.State,
			User:        fillProcessUserInfoWithoutEmailAndPhoneByUserId(staffOperator, sortedInfo.Table.UserId),
			Comment: func() string {
				if sortedInfo.Table.State == interCommon.PROCESS_STATE_ROLLBACKED || //2020-07-04 退回和补充资料的意见一定对所有人可见
					sortedInfo.Table.State == interCommon.PROCESS_STATE_NEED_COMPLETE {
					return sortedInfo.Data.Comment
				}
				if staffOperator.Ctx().User().UserId == sortedInfo.Table.UserId { //审批人一定能看到自己发的comment
					return sortedInfo.Data.Comment
				}
				if !isProcess && !commentVisible { //在workflow详情中，不给发起人看comment
					return common.StringEmpty
				}
				return sortedInfo.Data.Comment
			}(),
			RollbackNodeName:         sortedInfo.Data.RollbackNodeName,
			HandoverExplain:          explainFunc(sortedInfo, sortedInfo.Data.HandoverExplain, sortedInfo.Data.HandoverExplainVisible),
			CountersignBeforeExplain: explainFunc(sortedInfo, sortedInfo.Data.CountersignBeforeExplain, sortedInfo.Data.CountersignBeforeExplainVisible),
			CountersignAfterExplain:  explainFunc(sortedInfo, sortedInfo.Data.CountersignAfterExplain, sortedInfo.Data.CountersignAfterExplainVisible),
			Remark: func(remarks []*processModel.ProcessRemarkInfo) (ret []*processModel.ProcessRemarkInfo) {
			NEXTREMARK:
				for _, r := range remarks {
					for _, visibleStaff := range r.VisibleStaff {
						if visibleStaff.Type == interCommon.STAFF_TYPE_ALL ||
							(visibleStaff.UserId != 0 && visibleStaff.UserId == staffOperator.Ctx().User().UserId) {
							ret = append(ret, r)
							continue NEXTREMARK
						}
					}
					for _, atUser := range r.AtUser {
						if atUser.UserId != 0 && atUser.UserId == staffOperator.Ctx().User().UserId {
							ret = append(ret, r)
							continue NEXTREMARK
						}
					}
				}
				return
			}(sortedInfo.Data.Remark),
			SubProcess: assembleSortedProcessInfoToNodeProcessInfo(staffOperator, sortedInfo.SubProcess, commentVisible, isProcess),
			SubType:    sortedInfo.Detail.SubType,
			CreateTime: sortedInfo.Table.CreateTime,
			UpdateTime: sortedInfo.Table.UpdateTime,
		}
		res = append(res, nodePInfo)
		sort.Slice(res, func(i, j int) bool {
			if isRollbackOrNeedComplete(res[i]) && isRollbackOrNeedComplete(res[j]) {
				return res[i].UpdateTime > res[j].UpdateTime
			} else if isRollbackOrNeedComplete(res[i]) {
				return true
			} else if isRollbackOrNeedComplete(res[j]) {
				return false
			}
			return res[i].CreateTime < res[j].CreateTime
		})
	}
	return res
}

func isRollbackOrNeedComplete(p *NodeProcessInfo) bool {
	return p.State == interCommon.PROCESS_STATE_ROLLBACKED || p.State == interCommon.PROCESS_STATE_NEED_COMPLETE
}

func extractDataInput(data string) (input interface{}, err error) {
	m := make(map[string]interface{})
	if err = common.JsonDecode([]byte(data), &m); err != nil {
		return nil, err
	}
	return m["input"], nil
}

func (op *WorkflowOperator) ValidateRelatedWorkflow(relatedWorkflowUuid, workflowUuid string) error {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	relatedNodeTables, err := wModel.QueryNodeRuntimeByParentUuid(relatedWorkflowUuid)
	if err != nil {
		return err
	}
	for _, relatedNodeTable := range relatedNodeTables {
		if relatedNodeTable.Type == interCommon.NODE_TYPE_START { //校验起始节点
			if relatedNodeTable.UserId != op.Ctx().User().UserId { //如果这个NodeTable不是本人的流程，还得查一下是否有相关的process
				pModel, err := processModel.NewProcessModel(op, false)
				if err != nil {
					return err
				}
				processTables, err := pModel.QueryUserProcessRuntimeByWorkflowUuids([]string{relatedWorkflowUuid})
				if err != nil {
					return err
				}
				if len(processTables) == 0 { //没有相关的process
					return interCommon.ErrNotRelateWorkflow
				}
			}
			//input, err := extractDataInput(relatedNodeTable.Data)
			//if err != nil {
			//	return err
			//}
			//formOperator := formLogic.NewFormOperator(op.Ctx())
			//return formOperator.ValidateWorkflowUuid(relatedNodeTable.Front, workflowUuid, input)
			return nil
		}
	}
	return interCommon.ErrNotRelateWorkflow
}

func (op *WorkflowOperator) CreateWorkflow(workflowTemplateUuid string, customWorkflowName interCommon.I18nString,
	data interface{}, approvers []*SelfSelectStaffInfo, notifiers []*SelfSelectStaffInfo) (workflowUuid string, serialId int64, workflowName interCommon.I18nString, err error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return common.StringEmpty, 0, nil, err
	}
	//查workflow template
	workflowTemplateTable, err := wModel.QueryEnabledWorkflowTemplateByUuid(workflowTemplateUuid)
	if err != nil {
		return common.StringEmpty, 0, nil, err
	}
	//校验发起方是否合法
	if op.Ctx().User().Origin == interCommon.OriginBpm { //发起方是BPM
		if workflowTemplateTable.Origin != interCommon.OriginAll &&
			workflowTemplateTable.Origin != interCommon.OriginBpm {
			return common.StringEmpty, 0, nil, interCommon.ErrWorkflowTmplOrigin
		}
	} else { //发起方是内部系统
		if workflowTemplateTable.Origin != interCommon.OriginAll &&
			workflowTemplateTable.Origin != interCommon.OriginInternalSystem &&
			strings.ToLower(workflowTemplateTable.Origin) != strings.ToLower(op.Ctx().User().Origin) {
			return common.StringEmpty, 0, nil, interCommon.ErrWorkflowTmplOrigin
		}
	}
	//检查是否有权限
	staffOperator := staffLogic.NewStaffOperator(op.ctx)
	allDeptIds, _, allUserGroupIds, err := staffOperator.GetUserAllDeptIdsAndDeptNamesAndUserGroupIds()
	if err != nil {
		return common.StringEmpty, 0, nil, err
	}
	//筛选是否有权限看到的模板
	var staffInfos []staffLogic.StaffInfo
	if err = common.JsonDecode([]byte(workflowTemplateTable.Staff), &staffInfos); err != nil {
		return common.StringEmpty, 0, nil, err
	}
	if !staffOperator.JudgeIfStaffInfosContainsAnyUserIdsOrDeptIdsOrUserGroupIds(
		staffInfos, []int64{op.ctx.User().UserId}, allDeptIds, allUserGroupIds) {
		return common.StringEmpty, 0, nil, common.ErrNoPermission
	}
	//生成uuid 和 name
	workflowUuid = common.NewUuid(common.RESOURCE_TYPE_WORKFLOW)
	if err = customWorkflowName.Validate(); err == nil && customWorkflowName.MaxLength() > 0 {
		workflowName = customWorkflowName
	} else {
		workflowName, err = interCommon.NewI18nString(workflowTemplateTable.Name)
	}
	if err != nil {
		return common.StringEmpty, 0, nil, err
	}
	workflowDescription, err := interCommon.NewI18nString(workflowTemplateTable.Description)
	if err != nil {
		op.Ctx().Log().Errorln("workflow template description error:", err)
	}
	//读取旧的workflow template detail
	topo := &engine.WorkflowDetail{}
	err = common.JsonDecode([]byte(workflowTemplateTable.Detail), &topo)
	if err != nil {
		return common.StringEmpty, 0, nil, err
	}
	nodeTemplateUuids := make([]string, 0, len(topo.Nodes))
	for _, n := range topo.Nodes {
		nodeTemplateUuids = append(nodeTemplateUuids, n.TemplateUuid)
	}
	//查所有的node template
	nodeTemplateTables, err := wModel.QueryNodeTemplatesByUuids(nodeTemplateUuids)
	if err != nil {
		return common.StringEmpty, 0, nil, err
	}
	//执行剪枝操作
	var finish bool
	topo, nodeTemplateTables, finish, err = op.PreHandleWorkflowByWorkflowDetail(data.(map[string]interface{}), topo, nodeTemplateTables)
	if err != nil {
		return common.StringEmpty, 0, nil, err
	} else if !finish {
		return common.StringEmpty, 0, nil, interCommon.ErrWorkflowImpossibleAfterCut
	}
	//生成node runtime uuid
	nodeTemplateUuidNodeUuidMap := op.generateNodeRuntimeUuidByWorkflowDetail(
		topo)
	//生成新的topology
	workflowDetailByte, err := common.JsonEncode(topo)
	if err != nil {
		return common.StringEmpty, 0, nil, err
	}
	//组装新的node table
	var nodeTables []*workflowModel.NodeTable
	for _, nodeTemplate := range nodeTemplateTables {
		if len(nodeTemplateUuidNodeUuidMap[nodeTemplate.Uuid]) == 0 {
			return common.StringEmpty, 0, nil, interCommon.ErrNodeTopology
		}
		nodeTable := &workflowModel.NodeTable{
			UserId:       op.ctx.User().UserId,
			InitiatorId:  op.ctx.User().UserId, //TODO 暂时不做代发功能
			TemplateUuid: nodeTemplate.Uuid,
			Uuid:         nodeTemplateUuidNodeUuidMap[nodeTemplate.Uuid],
			ParentUuid:   workflowUuid,
			Name:         nodeTemplate.Name,
			Type:         nodeTemplate.Type,
			Detail:       nodeTemplate.Detail,
			Front:        nodeTemplate.Front,
			Data:         common.StringJsonEmpty,
			State:        interCommon.STATE_READY,
			Status:       interCommon.STATUS_NORMAL,
		}
		//如果是开始节点，传input的数据进去
		if nodeTable.Type == interCommon.NODE_TYPE_START {
			formOperator := formLogic.NewFormOperator(op.ctx) //2020-5-24 开始校验用户输入的data
			if err = formOperator.ValidateUserInputData(nodeTable.Front, data); err != nil {
				return common.StringEmpty, 0, nil, err
			}
			d, err := common.JsonEncode(&engine.StartTaskData{Input: data})
			if err != nil {
				return common.StringEmpty, 0, nil, err
			}
			nodeTable.Data = string(d)
		}
		nodeTables = append(nodeTables, nodeTable)
	}
	procTables, err := op.preGenerateProcessesByNodeDetail(workflowName.Marshal(), workflowDescription.Marshal(),
		nodeTables, approvers, notifiers, data)
	if err != nil { //预生成process出错
		return common.StringEmpty, 0, nil, err
	}
	//执行插入
	record, err := wModel.BatchInsertWorkflowAndNodeAndProcessRuntime(nodeTables, procTables,
		workflowTemplateUuid, workflowTemplateTable.GroupId, workflowUuid, workflowName.Marshal(), workflowDescription.Marshal(),
		string(workflowDetailByte), workflowTemplateTable.Edition, interCommon.STATE_READY, interCommon.STATUS_NORMAL)
	if err != nil {
		return common.StringEmpty, 0, nil, err
	}
	serialId = record.SerialId
	return
}

// 根据一个template的detail，为其中所有node生成独立的uuid
func (op *WorkflowOperator) generateNodeRuntimeUuidByWorkflowDetail(topo *engine.WorkflowDetail) (
	nodeTemplateUuidNodeUuidMap map[string]string) {
	nodeTemplateUuidNodeUuidMap = make(map[string]string)
	//组装一个新的workflow detail
	f := func(t *engine.TopologyDetail) {
		if nodeUuid, ok := nodeTemplateUuidNodeUuidMap[t.TemplateUuid]; ok {
			t.Uuid = nodeUuid
		} else {
			t.Uuid = common.NewUuid(common.RESOURCE_TYPE_NODE)
			nodeTemplateUuidNodeUuidMap[t.TemplateUuid] = t.Uuid
		}
		t.TemplateUuid = common.StringEmpty //赋为空串
	}
	for _, n := range topo.Nodes {
		f(n)
		for _, pn := range n.Prev {
			f(pn)
		}
		for _, nn := range n.Next {
			f(nn)
		}
	}
	return
}

func (op *WorkflowOperator) QueryWorkflowInfosByUuids(workflowUuids []string) (
	workflowInfos []*WorkflowInfo, err error) {
	if len(workflowUuids) == 0 {
		return nil, nil
	}
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	workflowTables, err := wModel.QueryWorkflowRuntimeByUuids(workflowUuids)
	if err != nil {
		return nil, err
	}

	staffOperator := staffLogic.NewStaffOperator(op.ctx)
	var groupIds []int64
	var templateUuids []string
	for _, workflowTable := range workflowTables {
		user, err := staffOperator.GetUserInfoByUserId(workflowTable.UserId)
		if err != nil {
			op.ctx.Log().Errorf("QueryUserInfoByUserId[%d] err[%v]", workflowTable.UserId, err)
			user = &staffLogic.UserInfo{UserId: workflowTable.UserId}
		}
		user.ParentDept, err = staffOperator.GetParentDeptInfoByUserId(workflowTable.UserId)
		if err != nil {
			op.ctx.Log().Errorf("QueryUserParentDeptInfoByUserId[%d] err[%v]", workflowTable.UserId, err)
		}
		user.Email = common.StringEmpty //抹去邮箱信息
		user.Phone = common.StringEmpty //抹去手机信息
		w := &WorkflowInfo{
			WorkflowUuid:         workflowTable.Uuid,
			WorkflowSerialId:     workflowTable.SerialId,
			WorkflowTemplateUuid: workflowTable.TemplateUuid,
			User:                 user,
			CreateTime:           workflowTable.CreateTime,
			UpdateTime:           workflowTable.UpdateTime,
			State:                workflowTable.State,
			Edition:              workflowTable.Edition,
			Tags:                 []*WorkflowTemplateTagInfo{},
		}
		if workflowTable.GroupId > 0 {
			w.WorkflowTemplateGroup = &wCommon.GroupInfoForTmplInfo{
				WorkflowTemplateGroupId: workflowTable.GroupId,
			}
			groupIds = append(groupIds, workflowTable.GroupId)
		}
		if w.WorkflowName, err = common.JsonStringToRaw(workflowTable.Name); err != nil {
			return nil, err
		}
		if w.Description, err = common.JsonStringToRaw(workflowTable.Description); err != nil {
			op.Ctx().Log().Errorln(err)
		}
		templateUuids = append(templateUuids, workflowTable.TemplateUuid)
		workflowInfos = append(workflowInfos, w)
	}

	//tag
	tagInfoMap, err := wModel.QueryTemplateTagsInfoByUuid(templateUuids)
	if err != nil {
		return nil, err
	}

	//查询模板组数据
	groupInfos, err := wModel.QueryTemplateGroupByGroupIds(groupIds)
	if err != nil {
		op.ctx.Log().Errorf("QueryGroupInfosByGroupIds[%v] err[%v]", groupIds, err)
	}
	groupIdNameMap := make(map[int64]json.RawMessage)
	for _, groupInfo := range groupInfos {
		if name, err := common.JsonStringToRaw(groupInfo.Name); err != nil {
			return nil, err
		} else {
			groupIdNameMap[groupInfo.ID] = name
		}
	}
	//拼一下
	for _, info := range workflowInfos {
		if info.WorkflowTemplateGroup != nil {
			info.WorkflowTemplateGroup.Name = groupIdNameMap[info.WorkflowTemplateGroup.WorkflowTemplateGroupId]
		}

		if tags, ok := tagInfoMap[info.WorkflowTemplateUuid][info.Edition]; ok {
			for _, t := range tags {
				info.Tags = append(info.Tags, &WorkflowTemplateTagInfo{
					Id:      t.ID,
					TagName: t.TagName,
				})
			}
		}
	}
	return workflowInfos, nil
}

func (op *WorkflowOperator) QueryWorkflowUuidsByNameVagueAndTmplUuidsAndUserIds(workflowName string,
	templateUuids []string, workflowStates []string, userIds []int64, workflowTemplateGroupIds []int64,
	bothWorkflowAndUserName bool, queryWorkflowUuids []string, workflowSerialIds []string) (
	workflowUuids []string, err error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	if bothWorkflowAndUserName {
		workflowResult, err := wModel.QueryWorkflowUuidsByNameAndTmplUuidsAndUserIds(workflowName, templateUuids,
			workflowStates, nil, workflowTemplateGroupIds, queryWorkflowUuids, workflowSerialIds)
		if err != nil {
			return workflowResult, err
		}
		s := sets.NewString(workflowResult...) //去重
		if len(userIds) > 0 {                  //如果查出了用户名，则添加用户
			userResult, err := wModel.QueryWorkflowUuidsByNameAndTmplUuidsAndUserIds(common.StringEmpty, templateUuids,
				workflowStates, userIds, workflowTemplateGroupIds, queryWorkflowUuids, workflowSerialIds)
			if err != nil {
				return userResult, err
			}
			s.AddString(userResult...)
		}
		return s.ToStringArray(), nil
	} else {
		return wModel.QueryWorkflowUuidsByNameAndTmplUuidsAndUserIds(workflowName, templateUuids, workflowStates,
			userIds, workflowTemplateGroupIds, queryWorkflowUuids, workflowSerialIds)
	}
}

func (op *WorkflowOperator) ReCreateWorkflow(workflowUuid string,
	data interface{}) (err error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	//查workflow
	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(workflowUuid)
	if err != nil {
		return err
	} else if workflowTable.UserId != op.ctx.User().UserId {
		return common.ErrNoPermission
	} else if workflowTable.State != interCommon.STATE_RESET {
		return interCommon.ErrWorkflowState
	}
	//查所有的node template
	nodeTables, err := wModel.QueryNodeRuntimeByParentUuid(workflowUuid)
	if err != nil {
		return err
	}
	//找到start node
	var startNode *workflowModel.NodeTable
	for _, nodeTable := range nodeTables {
		if nodeTable.Type == interCommon.NODE_TYPE_START {
			startNode = nodeTable
			break
		}
	}
	if startNode == nil {
		return common.ErrRecordNotFound
	}
	var d engine.StartTaskData
	err = common.JsonStringDecode(startNode.Data, &d)
	if err != nil {
		return err
	}
	formOperator := formLogic.NewFormOperator(op.Ctx())
	err = formOperator.ValidateUserInputData(startNode.Front, data)
	if err != nil {
		return err
	}

	//处理历史记录的增加
	dataHistory := wCommon.ExtractDataHistory(d.Input)
	wCommon.DeleteDataHistory(d.Input)
	staffOperator := staffLogic.NewStaffOperator(op.Ctx())
	userInfo, err := staffOperator.GetUserInfoByUserId(op.Ctx().User().UserId)
	if err != nil {
		op.Ctx().Log().Errorln("update process: query userInfo error:", err)
		userInfo = &staffLogic.UserInfo{
			UserId:      op.Ctx().User().UserId,
			Account:     op.Ctx().User().AccountName,
			NameZh:      op.Ctx().User().DisplayName,
			NameEn:      op.Ctx().User().DisplayName,
			NameDisplay: op.Ctx().User().DisplayName,
			EmployeeId:  op.Ctx().User().EmployeeId,
		}
	}
	userInfo.Email = common.StringEmpty //抹去用户敏感信息
	userInfo.Phone = common.StringEmpty
	dataHistory = append(dataHistory, &wCommon.DataHistoryInfo{
		PrevData: d.Input,
		Data:     data,
		User:     userInfo,
		OpTime:   time.Now().Unix(),
	})

	mergeOperator := engine.NewMergeOperator(engine.MergeConfig{
		Mode: interCommon.MERGE_MODE_OVERRIDE,
	})

	d.Input = make(map[string]interface{})
	d.Input = mergeOperator.Merge([]interface{}{
		d.Input, data,
	})

	wCommon.SetDataHistory(d.Input, dataHistory)
	newData, err := common.JsonStringEncode(d)
	if err != nil {
		return err
	}
	//更新节点data
	if err = wModel.UpdateNodeRuntimeData(startNode.Uuid, newData); err != nil {
		return err
	}
	//设回Ready状态，准备再次执行
	_, err = wModel.UpdateWorkflowRuntimeState(workflowUuid, interCommon.STATE_READY)
	return err
}

func (op *WorkflowOperator) CompleteWorkflow(workflowUuid string,
	remark string, file []*processModel.RemarkFileInfo,
	visibleStaff []*staffLogic.StaffInfo) (err error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	//查workflow
	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(workflowUuid)
	if err != nil {
		return err
	} else if workflowTable.UserId != op.ctx.User().UserId {
		return common.ErrNoPermission
	} else if workflowTable.State != interCommon.STATE_NEED_COMPLETE {
		return interCommon.ErrWorkflowState
	}
	//查所有的node template
	nodeTables, err := wModel.QueryNodeRuntimeByParentUuid(workflowUuid)
	if err != nil {
		return err
	}
	//找到start node
	var startNode *workflowModel.NodeTable
	for _, nodeTable := range nodeTables {
		if nodeTable.Type == interCommon.NODE_TYPE_START {
			startNode = nodeTable
			break
		}
	}
	if startNode == nil {
		return common.ErrRecordNotFound
	}
	var d engine.StartTaskData
	err = common.JsonStringDecode(startNode.Data, &d)
	if err != nil {
		return err
	}
	d.Complete = append(d.Complete, &engine.CompleteInfo{
		Remark:       remark,
		File:         file,
		VisibleStaff: visibleStaff,
		CreateTime:   time.Now().Unix(),
	})
	newData, err := common.JsonStringEncode(d)
	if err != nil {
		return err
	}
	//更新节点data
	if err = wModel.UpdateNodeRuntimeData(startNode.Uuid, newData); err != nil {
		return err
	}
	//设回Ready状态，准备再次执行
	_, err = wModel.UpdateWorkflowRuntimeState(workflowUuid, interCommon.STATE_READY)
	return err
}

/*
*
有两种实现方案（当前实现的第一种方案）：
方案1.前端只传当前Node的未审批ProcessUuids,后端拿到ProcessUuid后直接发送钉钉通知
方案1.前端在workflow详情页中,将workflowUuid传给后端。后端从process_runtime表中查出该workflow和node下的所有未审批的任务
*/
func (op *WorkflowOperator) UrgeMyflow(processUuids []string) error {
	notifyOperator := notifyLogic.NewNotifyOperator(op.Ctx())
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return err
	}
	processes, err := pModel.QueryProcessRuntimeByUuids(processUuids)
	if err != nil {
		return err
	}
	//过滤一下process
	var filteredProcesses []*processModel.ProcessTable
	for _, p := range processes {
		if p.InitiatorId != op.ctx.User().UserId {
			return interCommon.ErrUrgeWorkflow
		} else if len(p.WorkflowUuid) == 0 { //催办的都是BPM流程中的任务
			return interCommon.ErrNotWorkflowProcess
		} else if p.State != interCommon.PROCESS_STATE_UNAPPROVED &&
			p.State != interCommon.PROCESS_STATE_UNDONE {
			continue
		}
		filteredProcesses = append(filteredProcesses, p)
	}
	processes = filteredProcesses //过滤
	for _, p := range processes {
		processData := &processModel.ApprovalProcessData{}
		if err := common.JsonDecode([]byte(p.Data), &processData); err != nil {
			return err
		}
		// 如果当前时间与上次催办的时间间隔<30分钟，则忽略此次催办
		curTimestamp := time.Now().Unix()
		if curTimestamp-processData.LastUrgeTime < 30*60 {
			continue
		}
		processData.LastUrgeTime = curTimestamp
		if body, err := common.JsonEncode(processData); err != nil {
			return err
		} else if _, err = pModel.UpdateProcessRuntimeDataByProcessUuid(p.Uuid, string(body)); err != nil {
			return err
		}

		workflowName, initiatorName, err := op.GetWorkflowNameAndInitiatorName(p.WorkflowUuid)
		if err != nil {
			return err
		}
		var action string
		if p.Type == interCommon.PROCESS_TYPE_TRANSACTOR {
			action = "办理"
		} else {
			action = "审批"
		}
		err = notifyOperator.SendNotify(p.UserId, notifyLogic.NotifyMsg{
			LarkContent: notifyLogic.GenerateLarkUrgeContent(workflowName, initiatorName, p.Uuid, action, curTimestamp, notifyLogic.LarkInteractiveCustomTemplate{}, nil),
		}, []interCommon.NotifyTypeEnum{interCommon.NotifyTypeEmail, interCommon.NotifyTypeLark})
		if err != nil {
			return err
		}
	}
	return nil
}

func (op *WorkflowOperator) GetWorkflowNameAndInitiatorName(workflowUuid string) (workflowName, userName string, err error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return common.StringEmpty, common.StringEmpty, err
	}
	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(workflowUuid)
	if err != nil {
		return common.StringEmpty, common.StringEmpty, err
	}
	i18nName, err := interCommon.NewI18nString(workflowTable.Name)
	if err != nil {
		workflowName = workflowTable.Name
	} else {
		workflowName = i18nName.GetOneByOrder()
	}
	staffOperator := staffLogic.NewStaffOperator(op.Ctx())
	userInfo, err := staffOperator.GetUserInfoByUserId(workflowTable.InitiatorId)
	if err != nil {
		userName = "某申请人"
	} else if len(userInfo.NameZh) > 0 {
		userName = userInfo.NameZh
	} else if len(userInfo.NameDisplay) > 0 {
		userName = userInfo.NameDisplay
	} else if len(userInfo.NameEn) > 0 {
		userName = userInfo.NameEn
	} else {
		userName = "某申请人"
	}

	return workflowName, userName, nil
}

func (op *WorkflowOperator) QueryNodeRuntimeByUuidsAndAssembleToCollection(nodeUuids []string) (collection.ICollection, error) {

	if len(nodeUuids) == 0 {
		return nil, nil
	}

	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}

	nodeRuntimeInfos, err := wModel.QueryNodeRuntimeByUuids(nodeUuids)

	if err != nil {
		return nil, err
	}

	return collection.NewObjCollection(nodeRuntimeInfos), nil
}

func (op *WorkflowOperator) QueryWorkflowTableByUuid(uuid string) (*workflowModel.WorkflowTable, error) {
	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil, err
	}
	return wModel.QueryWorkflowRuntimeByUuid(uuid)
}
