/**
 * @note
 * 审批task
 *
 * <AUTHOR>
 * @date 	2019-11-18
 */
package engine

import (
	"encoding/json"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	notifyLogic "gitlab.docsl.com/security/bpm/internal/logic/notify"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	"gitlab.docsl.com/security/bpm/internal/model/workflow"
	"sort"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

////////////////////init////////////////////

func init() {
	approvalBeginHandlers = []ApprovalHandlerFunc{
		HandleApprovalInput,
		HandleSortAndAssembleApprovalProcess, //预处理process，拼装成树状结构
		HandleRemoveDuplicateApprover,        //审批人去重
		HandleApprovalProcessJudgement,       //查看当前的process是否已经满足审批通过条件
		HandleApprovalInputToOutput,
	}
	approvalContinueHandlers = []ApprovalHandlerFunc{
		HandleApprovalInput,
		HandleSortAndAssembleApprovalProcess, //预处理process，拼装成树状结构
		HandleRemoveDuplicateApprover,        //审批人去重
		HandleApprovalProcessJudgement,       //查看当前的process是否已经满足审批通过条件
		HandleApprovalInputToOutput,
	}
	approvalPendHandlers = []ApprovalHandlerFunc{
		HandleInitializedApprovalProcess, //将initialized的process转化为unApproved
		HandleNotifyApprover,             //在流程pend的时候给用户发送邮件、钉钉通知
	}
	approvalFailHandlers = []ApprovalHandlerFunc{
		HandleDiscardUnapprovedAndInitializedProcess,
	}
	approvalDiscardHandlers = []ApprovalHandlerFunc{
		HandleDiscardUnapprovedAndInitializedProcess,
	}
}

////////////////////init////////////////////

type ApprovalHandlerFunc func(*ApprovalTask) (pass bool, err error)

var (
	approvalBeginHandlers    []ApprovalHandlerFunc
	approvalContinueHandlers []ApprovalHandlerFunc
	approvalPendHandlers     []ApprovalHandlerFunc
	approvalFinishHandlers   []ApprovalHandlerFunc
	approvalFailHandlers     []ApprovalHandlerFunc
	approvalDiscardHandlers  []ApprovalHandlerFunc
)

type ApprovalTaskDetail struct {
	Name              json.RawMessage             `json:"name"` //给前端加的回显字段
	Staff             []*staffLogic.StaffInfo     `json:"staff"`
	EmptyStaff        []*staffLogic.StaffInfo     `json:"emptyStaff"`
	StaffFromForm     []*formLogic.StaffFromForm  `json:"staffFromForm"` //从表单中获取联系人
	IsEmpty           bool                        `json:"isEmpty"`       //是否因找不到审批人触发了转交机制
	SelfSelect        bool                        `json:"selfSelect"`
	AllowedAction     []common2.ProcessActionEnum `json:"allowedAction"`
	Notify            notifyLogic.NotifyConfig    `json:"notify"`
	Mode              common2.ApprovalModeEnum    `json:"mode"`
	EmptyMode         common2.ApprovalModeEnum    `json:"emptyMode"`
	Comment           bool                        `json:"comment"`
	CommentVisible    bool                        `json:"commentVisible"`
	OtherApproverUser *staffLogic.UserInfo        `json:"otherApprovalUser"` //审批被其它人同意/拒绝
}

type ApprovalTaskData struct {
	Input  interface{} `json:"input"`
	Output interface{} `json:"output"`
}

type ApprovalTask struct {
	node
	Detail              ApprovalTaskDetail
	Form                formLogic.FormConfig
	Data                ApprovalTaskData
	ProcessInfos        []*SortedProcessInfo //全部的processInfo
	ProcessInfoMap      map[string]*SortedProcessInfo
	CurrentProcessInfos []*SortedProcessInfo //当前需要关注的processInfo
}

type SortedProcessInfo struct {
	Table         *processModel.ProcessTable
	Detail        processModel.ApprovalProcessDetail
	Data          processModel.ApprovalProcessData
	SubProcess    []*SortedProcessInfo //子process
	ParentProcess *SortedProcessInfo   //父process
}

func (info *SortedProcessInfo) isFailed() bool {
	return info.Table.State == common2.PROCESS_STATE_REJECTED ||
		info.Table.State == common2.PROCESS_STATE_REJECTED_BY_OTHERS
}

func (info *SortedProcessInfo) isUnDone() bool {
	return info.Table.State == common2.PROCESS_STATE_INITIALIZED ||
		info.Table.State == common2.PROCESS_STATE_UNAPPROVED ||
		info.Table.State == common2.PROCESS_STATE_UNDONE
}

func (info *SortedProcessInfo) isApproved() bool {
	return info.Table.State == common2.PROCESS_STATE_APPROVED ||
		info.Table.State == common2.PROCESS_STATE_DONE_BY_OTHERS ||
		info.Table.State == common2.PROCESS_STATE_DONE ||
		info.Table.State == common2.PROCESS_STATE_APPROVED_BY_OTHERS ||
		info.Table.State == common2.PROCESS_STATE_HANDOVERED
}

func NewApprovalTask(op common.Operator, w *Workflow, uuid string) (t *ApprovalTask, err error) {
	t = &ApprovalTask{}
	t.NodeBase, err = workflow.InitNodeBase(op, uuid)
	t.Workflow = w
	return t, err
}

func (t *ApprovalTask) Load() error {
	if err := t.NodeBase.Load(); err != nil {
		return err
	}
	//将表中的data还原到task中
	if err := common.JsonDecode([]byte(t.Table.Data), &t.Data); err != nil {
		return err
	}
	//将表中的detail还原到Detail中
	if err := common.JsonDecode([]byte(t.Table.Detail), &t.Detail); err != nil {
		return err
	}
	//将表中的front还原到form中
	if err := common.JsonDecode([]byte(t.Table.Front), &t.Form); err != nil {
		return err
	}
	t.SetInput(t.Data.Input, 0)
	t.SetOutput(t.Data.Output, 0)
	return nil
}

func (at *ApprovalTask) Begin() error {
	//先将任务begin
	if err := at.node.Begin(); err != nil {
		return err
	}
	for _, handler := range approvalBeginHandlers {
		pass, err := handler(at)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				at.Error = err
				return at.Fail()
			} else {
				return at.Pend()
			}
		}
	}
	return at.Finish()
}

func (at *ApprovalTask) Continue() error {
	//先将任务Continue
	if err := at.node.Continue(); err != nil {
		return err
	}
	for _, handler := range approvalContinueHandlers {
		pass, err := handler(at)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				at.Error = err
				return at.Fail()
			} else {
				return at.Pend()
			}
		}
	}
	return at.Finish()
}

func (at *ApprovalTask) Pend() error {
	//先执行Pend前的流程
	for _, handler := range approvalPendHandlers {
		_, err := handler(at)
		if err != nil { //根据是否产生错误决定是否失败
			at.Error = err
			return at.Fail()
		}
	}
	return at.node.Pend()
}

func (at *ApprovalTask) Finish() error {
	//先执行Finish前的流程
	for _, handler := range approvalFinishHandlers {
		pass, err := handler(at)
		if !pass || err != nil { //如果没有pass或产生错误，都直接失败
			at.Error = err
			return at.Fail()
		}
	}
	return at.node.Finish()
}

func (at *ApprovalTask) Fail() error {
	//先执行Fail前的流程
	for _, handler := range approvalFailHandlers {
		pass, err := handler(at)
		if !pass || err != nil {
			at.Error = err
		}
	}
	return at.node.Fail()
}

func (at *ApprovalTask) Discard() error {
	//先执行Finish前的流程
	for _, handler := range approvalDiscardHandlers {
		pass, err := handler(at)
		if !pass || err != nil { //如果没有pass或产生错误，都直接失败
			at.Error = err
		}
	}
	return at.node.Discard()
}

func (at *ApprovalTask) Persist() error {
	d, err := common.JsonEncode(at.Data)
	if err != nil {
		return err
	}
	at.Table.Data = string(d)
	return at.NodeBase.Persist()
}

////////////////////handle func////////////////////

func HandleApprovalInput(at *ApprovalTask) (pass bool, err error) {
	if !common.IsNil(at.Data.Input) {
		return true, nil
	}
	inputs := at.GetInput()
	if len(inputs) == 0 || common.IsNil(inputs[0]) {
		return false, common2.ErrNodeInvalidInput
	}
	at.Data.Input = inputs[0]
	return true, at.Persist()
}

func HandleApprovalInputToOutput(at *ApprovalTask) (pass bool, err error) {
	if common.IsNil(at.Data.Input) {
		return false, common2.ErrNodeInvalidInput
	}
	at.Data.Output = at.Data.Input
	at.SetOutput(at.Data.Output, 0)
	return true, at.Persist()
}

// 读取所有的Process，组装成多根树结构，此步为一个预处理，之后需要用到
func HandleSortAndAssembleApprovalProcess(at *ApprovalTask) (pass bool, err error) {
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}
	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		at.Uuid, common2.PROCESS_TYPE_APPROVAL)
	if err != nil {
		return false, err
	}
	//过滤退回的process
	for idx, p := range processes {
		if p.State == common2.PROCESS_STATE_ROLLBACKED {
			processes = append(processes[:idx], processes[idx+1:]...)
			break
		}
	}
	at.ProcessInfos, at.ProcessInfoMap, err = AssembleProcessTablesToSortedProcessInfos(processes)
	if err != nil {
		return false, err
	}
	at.CurrentProcessInfos = make([]*SortedProcessInfo, 0)
	for _, procInfo := range at.ProcessInfos { //收集当前需要concern的process
		at.CurrentProcessInfos = append(at.CurrentProcessInfos, FindCurrentProcessInRoot(procInfo))
	}
	return true, nil
}

func AssembleProcessTablesToSortedProcessInfos(processes []*processModel.ProcessTable) ([]*SortedProcessInfo, map[string]*SortedProcessInfo, error) {
	processInfos := make([]*SortedProcessInfo, len(processes))
	processInfoMap := make(map[string]*SortedProcessInfo)
	if len(processes) == 0 {
		return processInfos, processInfoMap, nil
	}
	for idx, p := range processes { //初始化processInfos数组
		processInfos[idx] = &SortedProcessInfo{
			Table: p,
		}
		processInfoMap[p.Uuid] = processInfos[idx]
		if err := common.JsonStringDecode(p.Detail, &processInfos[idx].Detail); err != nil {
			return nil, nil, err
		} else if err = common.JsonStringDecode(p.Data, &processInfos[idx].Data); err != nil {
			return nil, nil, err
		}
	}
	var sortedProcessInfos []*SortedProcessInfo
	for _, pInfo := range processInfos { //组装树形结构
		if len(pInfo.Detail.ParentUuid) == 0 { //表明是个根process
			sortedProcessInfos = append(sortedProcessInfos, pInfo)
		} else if parentProcess := processInfoMap[pInfo.Detail.ParentUuid]; parentProcess != nil {
			parentProcess.SubProcess = append(parentProcess.SubProcess, pInfo) //装到树下的结构
			pInfo.ParentProcess = parentProcess                                //指向父亲
		}
	}
	//依次审批的情况下，根据order排序
	sort.Slice(sortedProcessInfos, func(i, j int) bool {
		if sortedProcessInfos[i].Detail.Order < sortedProcessInfos[j].Detail.Order {
			return true
		} else {
			return sortedProcessInfos[i].Table.CreateTime < sortedProcessInfos[j].Table.CreateTime
		}
	})
	return sortedProcessInfos, processInfoMap, nil
}

// 处理状态为初始化的流程，置为待审批等状态，再交由后续处理
func HandleInitializedApprovalProcess(at *ApprovalTask) (pass bool, err error) {
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}
	var needChangeInfos []*SortedProcessInfo //含义是状态需要变更为unApproved的用户process
	for _, curProcess := range at.CurrentProcessInfos {
		if curProcess.Table.State == common2.PROCESS_STATE_INITIALIZED {
			needChangeInfos = append(needChangeInfos, curProcess)
			if at.Detail.Mode == common2.APPROVAL_MODE_INTURN_SIGN { //依次审批的情况下，一次只初始化一个process
				break
			}
		}
	}
	needChangeUuids := make([]string, len(needChangeInfos))
	for idx, info := range needChangeInfos {
		needChangeUuids[idx] = info.Table.Uuid
	}
	_, err = pModel.UpdateProcessRuntimeFromOldStatesByUuids(
		needChangeUuids, []common2.ProcessStateEnum{common2.PROCESS_STATE_INITIALIZED}, common2.PROCESS_STATE_UNAPPROVED)
	if err != nil { //修改失败，流程
		return false, err
	}
	for _, info := range needChangeInfos { //修改成功，修改状态
		info.Table.State = common2.PROCESS_STATE_UNAPPROVED
	}
	return true, nil
}

/* @note
 * 审批人去重，校验上一个节点是否有相同审批人且为已通过的审批任务，如果有，此审批人任务自动通过
 */
func HandleRemoveDuplicateApprover(at *ApprovalTask) (pass bool, err error) {
	if !at.Workflow.RemoveDuplicate { //无需去重，直接通过
		return true, nil
	}
	prevNodes := at.GetPrev()
	if len(prevNodes) == 0 || len(prevNodes) > 1 || prevNodes[0].Base().Table.Type != common2.NODE_TYPE_APPROVAL {
		//前序节点不只一个，或者不是审批节点也直接通过
		return true, nil
	}
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}
	//查询前序节点所有审批通过的的用户
	prevApprovedProcs, err := pModel.QueryApprovalProcessRuntimeByNodeUuidAndState(prevNodes[0].GetUuid(), common2.PROCESS_STATE_APPROVED, common2.PROCESS_TYPE_APPROVAL)
	if err != nil { //查询错误，不影响主流程，所以这里直接通过吧
		at.GetParent().Ctx().Log().Errorln(err)
		return true, nil
	}
	prevApprovedUserIdMap := make(map[int64]bool)
	for _, p := range prevApprovedProcs {
		prevApprovedUserIdMap[p.UserId] = true
	}
	var needUpdateProcessInfos bool
	for _, curPInfo := range at.CurrentProcessInfos {
		if prevApprovedUserIdMap[curPInfo.Table.UserId] && curPInfo.isUnDone() { //如果需要自动通过
			needUpdateProcessInfos = true
			curPInfo.Data.Comment = "审批人去重，自动通过"
			curPInfo.Table.State = common2.PROCESS_STATE_APPROVED
			dataStr, err := common.JsonStringEncode(curPInfo.Data)
			if err != nil {
				at.GetParent().Ctx().Log().Errorln(err)
				continue
			}

			if _, err = pModel.UpdateProcessRuntimeDataAndStateByProcessUuid(curPInfo.Table.Uuid, dataStr, common2.PROCESS_STATE_APPROVED); err != nil {
				at.GetParent().Ctx().Log().Errorln(err)
				continue
			}

			//更新审批时间
			if _, err = pModel.UpdateProcessRuntimeApprovalTime(curPInfo.Table.Uuid); err != nil {
				at.GetParent().Ctx().Log().Errorln(err)
				continue
			}

			if at.Detail.Mode == common2.APPROVAL_MODE_OR_SIGN {
				//否则需要看情况，看审批人的这次审批是否会直接影响审批结果
				var needDealWithOtherProcesses bool
				if curPInfo.SubProcess == nil { //无子process
					if curPInfo.ParentProcess == nil { //也没有父process，表示是个根process
						needDealWithOtherProcesses = true
					} else if curPInfo.Detail.SubType != common2.PROCESS_SUBTYPE_COUNTERSIGN_BEFORE { //不是父process加签出来的先审批process
						needDealWithOtherProcesses = true
					}
				} else if hasCountersignAfterSubProcess := func(sp *SortedProcessInfo) bool {
					for _, p := range sp.SubProcess {
						if p.Detail.SubType == common2.PROCESS_SUBTYPE_COUNTERSIGN_AFTER {
							return true
						}
					}
					return false
				}; !hasCountersignAfterSubProcess(curPInfo) { //子process中不存在一个在此后审批的加签process
					needDealWithOtherProcesses = true
				}
				if needDealWithOtherProcesses {
					var needUpdateProcessUuids []string
					for _, p := range at.ProcessInfos {
						if p.Table.Uuid != curPInfo.Table.Uuid && p.isUnDone() {
							needUpdateProcessUuids = append(needUpdateProcessUuids, p.Table.Uuid)
						}
					}
					//记录OtherApprover
					staffOperator := staffLogic.NewStaffOperator(at.GetParent().Ctx())
					if userInfo, err := staffOperator.GetUserInfoByUserId(curPInfo.Table.UserId); err != nil {
						at.GetParent().Ctx().Log().Errorln(err)
					} else {
						at.Detail.OtherApproverUser = &staffLogic.UserInfo{
							UserId:      userInfo.UserId,
							Account:     userInfo.Account,
							NameZh:      userInfo.NameZh,
							NameEn:      userInfo.NameEn,
							NameDisplay: userInfo.NameDisplay,
							EmployeeId:  userInfo.EmployeeId,
						}
						if nodeDetail, err := common.JsonStringEncode(at.Detail); err != nil {
							at.GetParent().Ctx().Log().Errorln(err)
						} else {
							wModel, err := workflow.NewWorkflowModel(at.GetParent(), false)
							if err != nil {
								at.GetParent().Ctx().Log().Errorln(err)
							} else if err = wModel.UpdateNodeRuntimeDetail(at.Table.Uuid, nodeDetail); err != nil {
								at.GetParent().Ctx().Log().Errorln(err)
							}
						}
					}
					//更新其它Process
					_, err = pModel.UpdateProcessRuntimeFromOldStatesByUuids(needUpdateProcessUuids, []common2.ProcessStateEnum{
						common2.PROCESS_STATE_INITIALIZED, common2.PROCESS_STATE_UNAPPROVED,
					}, common2.PROCESS_STATE_APPROVED_BY_OTHERS)

					if err != nil {
						at.GetParent().Ctx().Log().Errorln(err)
					} else {
						for _, needUpdateProcessUuid := range needUpdateProcessUuids {
							if p, ok := at.ProcessInfoMap[needUpdateProcessUuid]; ok {
								p.Table.State = common2.PROCESS_STATE_APPROVED_BY_OTHERS
							}
						}
						notifyOperator := notifyLogic.NewNotifyOperator(at.GetParent().Ctx())
						workflowName, initiatorName := GetWorkflowNameAndUserName(at.GetParent().Ctx(), at.Workflow.Table)

						for _, uuid := range needUpdateProcessUuids {
							if pInfo, exist := at.ProcessInfoMap[uuid]; exist && pInfo.Data.Messaged { //已经给这个人发过通知的情况下，要告知他已被他人处理
								//通知给其它人
								emailContent, err := GenerateEmailContentByTmpl(workflowName, initiatorName, DONE_BY_OTHERS_EMAIL_TMPL, GetToCProcessUrl(pInfo.Table.Uuid), nil)
								if err != nil {
									emailContent = GenerateEmailNotifyOtherApprovalContent(pInfo.Table.Uuid)
								}
								if errNotify := notifyOperator.SendNotify(pInfo.Table.UserId, notifyLogic.NotifyMsg{
									LarkContent: notifyLogic.GenerateLarkNotifyOtherApproverContent(workflowName, initiatorName, pInfo.Table.Uuid, at.Workflow.Table.CreateTime, at.Detail.Notify.LarkCustomTemplate, at.Data),
									MailTitle:   "流程被他人处理通知",
									MailContent: emailContent,
								}, at.Detail.Notify.Mode); errNotify != nil {
									at.GetParent().Ctx().Log().Errorln(err)
								}
							}
						}
					}

					break //直接break，不需要再去重了
				}
			}
		}
	}
	if needUpdateProcessInfos { //如果需要更新SortedProcessInfo结构
		HandleSortAndAssembleApprovalProcess(at)
	}
	return true, nil
}

/* @note
 * 这个函数的作用是返回一个sortedProcessInfo里具体需要处理/观察的那个process
 * 例如：存在handover的情况下，需要处理转交的那个人，而存在加签的情况下，需要视加签方式（前/后）而定
 */
func FindCurrentProcessInRoot(info *SortedProcessInfo) *SortedProcessInfo {
	if len(info.SubProcess) > 0 { //有子process的情况，看子process
		var h, cb, ca *SortedProcessInfo
		for i := range info.SubProcess {
			switch info.SubProcess[i].Detail.SubType {
			case common2.PROCESS_SUBTYPE_HANDOVER:
				h = info.SubProcess[i]
			case common2.PROCESS_SUBTYPE_COUNTERSIGN_BEFORE:
				cb = info.SubProcess[i]
			case common2.PROCESS_SUBTYPE_COUNTERSIGN_AFTER:
				ca = info.SubProcess[i]
			}
		}
		if cb != nil { //先处理before的
			if sub := FindCurrentProcessInRoot(cb); sub.isUnDone() || sub.isFailed() {
				return sub
			}
		}
		if h != nil {
			if ca == nil {
				return FindCurrentProcessInRoot(h)
			} else if sub := FindCurrentProcessInRoot(h); sub.isUnDone() || sub.isFailed() {
				return sub
			} else {
				return FindCurrentProcessInRoot(ca)
			}
		}
		if ca != nil {
			if info.isUnDone() || info.isFailed() { //如果是未处理状态则需要处理
				return info
			}
			return FindCurrentProcessInRoot(ca)
		}
	}
	//没有子process，或子process已处理完，需要看本process
	return info
}

// 判断是否已满足审批条件，若满足则Finish，若不满足则Pend，若拒绝则Fail
func HandleApprovalProcessJudgement(at *ApprovalTask) (pass bool, err error) {
	if len(at.ProcessInfos) == 0 {
		return true, nil
	}
	for _, curProcInfo := range at.CurrentProcessInfos {
		if curProcInfo.isFailed() { //有一个被拒绝直接拒绝
			return false, common2.ErrApproverRejected
		}
	}
	switch at.Detail.Mode {
	case common2.APPROVAL_MODE_AND_SIGN:
		fallthrough
	case common2.APPROVAL_MODE_INTURN_SIGN: //依次审批也是另一种形式的会签
		for _, curProcInfo := range at.CurrentProcessInfos {
			if curProcInfo.isUnDone() { //有一个是未审批状态就继续等待
				return false, nil
			}
		}
		return true, nil //已被全部通过
	case common2.APPROVAL_MODE_OR_SIGN:
		for _, curProcInfo := range at.CurrentProcessInfos {
			if curProcInfo.isApproved() { //有一个被批准直接通过
				return true, nil
			}
		}
		return false, nil //继续等待
	default:
		return false, common2.ErrInvalidApprovalMode //配置有问题
	}
}

// 读取需要审批的人的process，判断是否未发通知，若未发则发送通知（钉钉，邮件或短信等，若已发送，则不重复发送），发送后修改process中状态为已发送
func HandleNotifyApprover(at *ApprovalTask) (pass bool, err error) {
	notifyOperator := notifyLogic.NewNotifyOperator(at.GetParent().Ctx())
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}
	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		at.Uuid, common2.PROCESS_TYPE_APPROVAL)
	if err != nil {
		at.GetParent().Ctx().Log().Errorf("query process error [%v]", err)
		return false, nil
	}
	if len(processes) == 0 { //如果根本没process，表示无人审批，直接通过
		return true, nil
	}
	for _, proc := range processes {
		data := processModel.ApprovalProcessData{} //是否已发通知（jsonDecode出现错误默认为未发通知）
		_ = common.JsonStringDecode(proc.Data, &data)
		if proc.State != common2.PROCESS_STATE_UNAPPROVED || data.Messaged { //已发通知，继续下一条
			continue
		}
		workflowName, initiatorName := GetWorkflowNameAndUserName(at.GetParent().Ctx(), at.Workflow.Table)
		emailContent, err := GenerateEmailContentByTmpl(workflowName, initiatorName, NEED_APPROVE_EMAIL_TMPL, GetToCProcessUrl(proc.Uuid), nil)
		if err != nil {
			emailContent = GenerateEmailApprovalContent(proc.Uuid)
		}

		errNotify := notifyOperator.SendNotify(proc.UserId, notifyLogic.NotifyMsg{
			LarkContent: notifyLogic.GenerateLarkApprovalContent(workflowName, initiatorName, proc.Uuid, at.Workflow.Table.CreateTime, at.Detail.Notify.LarkCustomTemplate, at.Data),
			MailTitle:   "流程审批通知",
			MailContent: emailContent,
		}, at.Detail.Notify.Mode)
		data.Messaged = true
		if errNotify != nil {
			at.GetParent().Ctx().Log().Errorf("send notify to user [%d] error [%v]", proc.UserId, err)
		} else if dataBytes, err := common.JsonEncode(data); err != nil {
			at.GetParent().Ctx().Log().Errorf("json encode data error [%v]", err)
		} else if _, err = pModel.UpdateProcessRuntimeDataByProcessUuid(proc.Uuid, string(dataBytes)); err != nil {
			at.GetParent().Ctx().Log().Errorf("update process runtime data error [%v]", err)
		}
	}
	return true, nil
}

// 被失效时令所有还没审批的process直接Expire
func HandleDiscardUnapprovedAndInitializedProcess(at *ApprovalTask) (pass bool, err error) {
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return true, err
	}
	_, err = pModel.SetProcessRuntimeExpiredByNodeUuid(at.Table.Uuid)
	return true, err
}
