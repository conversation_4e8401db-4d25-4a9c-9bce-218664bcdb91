/**
 * @note
 * interact.go
 *
 * <AUTHOR>
 * @date 	2020-04-14
 */
package engine

import (
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	apiLogic "gitlab.docsl.com/security/bpm/internal/logic/api"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	wCommon "gitlab.docsl.com/security/bpm/internal/logic/workflow/common"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"net/http"
	"time"
)

////////////////////init////////////////////

func init() {
	interactBeginHandlers = []InteractHandlerFunc{
		HandleInteractInput,
		HandleInteractProcessCreate,
		HandlePostData,
		HandleInteractProcessCallback,
		HandleInteractInputToOutput,
	}
	interactContinueHandlers = []InteractHandlerFunc{
		HandleInteractInput,
		HandleInteractProcessCreate,
		HandlePostData,
		HandleInteractProcessCallback,
		HandleInteractInputToOutput,
	}
	interactPendHandlers = []InteractHandlerFunc{}
	interactFailHandlers = []InteractHandlerFunc{
		HandleFailInteractProcess,
	}
	interactDiscardHandlers = []InteractHandlerFunc{
		HandlePostData,
	}
}

////////////////////init////////////////////

type InteractHandlerFunc func(*InteractTask) (pass bool, err error)

var (
	interactBeginHandlers    []InteractHandlerFunc
	interactContinueHandlers []InteractHandlerFunc
	interactPendHandlers     []InteractHandlerFunc
	interactFinishHandlers   []InteractHandlerFunc
	interactFailHandlers     []InteractHandlerFunc
	interactDiscardHandlers  []InteractHandlerFunc
)

type InteractTask struct {
	node
	Detail InteractTaskDetail
	Data   InteractTaskData
	Form   formLogic.FormConfig
}

type InteractTaskData struct {
	Input     interface{} `json:"input"`
	Output    interface{} `json:"output"`
	ApiResult interface{} `json:"apiResult,omitempty"`
	Comment   string      `json:"comment,omitempty"`
}

type InteractApiResult struct {
	Pass    bool                   `json:"pass"`
	Data    map[string]interface{} `json:"data"`
	Comment string                 `json:"comment"`
}

type InteractTaskDetail struct {
	Url      string `json:"url"`
	Method   string `json:"method"`
	AppName  string `json:"appName"`
	Callback bool   `json:"callback"`
}

func NewInteractTask(op common.Operator, w *Workflow, uuid string) (t *InteractTask, err error) {
	t = &InteractTask{}
	t.NodeBase, err = workflowModel.InitNodeBase(op, uuid)
	t.Workflow = w
	return t, err
}

func (it *InteractTask) Load() error {
	if err := it.NodeBase.Load(); err != nil {
		return err
	}
	//将表中的data还原到task中
	if err := common.JsonDecode([]byte(it.Table.Data), &it.Data); err != nil {
		return err
	}
	//将表中的detail还原到Detail中
	if err := common.JsonDecode([]byte(it.Table.Detail), &it.Detail); err != nil {
		return err
	}
	//将表中的front还原到form中
	if err := common.JsonDecode([]byte(it.Table.Front), &it.Form); err != nil {
		return err
	}
	it.SetInput(it.Data.Input, 0)
	it.SetOutput(it.Data.Output, 0)
	return nil
}

func (it *InteractTask) Begin() error {
	//先将任务begin
	if err := it.node.Begin(); err != nil {
		return err
	}
	for _, handler := range interactBeginHandlers {
		pass, err := handler(it)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				it.Error = err
				return it.Fail()
			} else {
				return it.Pend()
			}
		}
	}
	return it.Finish()
}

func (it *InteractTask) Continue() error {
	//先将任务Continue
	if err := it.node.Continue(); err != nil {
		return err
	}
	for _, handler := range interactContinueHandlers {
		pass, err := handler(it)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				it.Error = err
				return it.Fail()
			} else {
				return it.Pend()
			}
		}
	}
	return it.Finish()
}

func (it *InteractTask) Pend() error {
	//先执行Pend前的流程
	for _, handler := range interactPendHandlers {
		_, err := handler(it)
		if err != nil { //根据是否产生错误决定是否失败
			it.Error = err
			return it.Fail()
		}
	}
	return it.node.Pend()
}

func (it *InteractTask) Finish() error {
	//先执行Finish前的流程
	for _, handler := range interactFinishHandlers {
		pass, err := handler(it)
		if !pass || err != nil { //如果没有pass或产生错误，都直接失败
			it.Error = err
			return it.Fail()
		}
	}
	return it.node.Finish()
}

func (it *InteractTask) Fail() error {
	//先执行Fail前的流程
	for _, handler := range interactFailHandlers {
		pass, err := handler(it)
		if !pass || err != nil {
			it.Error = err
		}
	}
	it.Persist()
	return it.node.Fail()
}

func (it *InteractTask) Discard() error {
	//先执行Finish前的流程
	for _, handler := range interactDiscardHandlers {
		pass, err := handler(it)
		if !pass || err != nil { //如果没有pass或产生错误，都直接失败
			it.Error = err
		}
	}
	return it.node.Discard()
}

func (it *InteractTask) Persist() error {
	d, err := common.JsonEncode(it.Data)
	if err != nil {
		return err
	}
	it.Table.Data = string(d)
	return it.NodeBase.Persist()
}

////////////////////handle func////////////////////

func HandleInteractInput(it *InteractTask) (pass bool, err error) {
	if !common.IsNil(it.Data.Input) {
		return true, nil
	}
	inputs := it.GetInput()
	if len(inputs) == 0 || common.IsNil(inputs[0]) {
		return false, common2.ErrNodeInvalidInput
	}
	it.Data.Input = inputs[0]
	return true, it.Persist()
}

func HandleInteractInputToOutput(it *InteractTask) (pass bool, err error) {
	if common.IsNil(it.Data.Input) {
		return false, common2.ErrNodeInvalidInput
	}
	it.Data.Output = it.Data.Input
	it.SetOutput(it.Data.Output, 0)
	return true, it.Persist()
}

// 读取需要post的系统，创建process，如果已创建则跳过
func HandleInteractProcessCreate(it *InteractTask) (pass bool, err error) {
	pModel, err := processModel.NewProcessModel(it.GetParent(), false)
	if err != nil {
		return false, err
	}
	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		it.Uuid, common2.PROCESS_TYPE_INTERACT)
	if err != nil {
		return false, err
	}
	if len(processes) > 0 { //如果已经有对应process了，则表示已经插入过process，直接结束
		return true, nil
	}
	if _, err := pModel.InsertOrUpdateInteractProcessRuntime(0,
		common.NewUuid(common.RESOURCE_TYPE_PROCESS),
		it.Table.ParentUuid,
		it.Uuid,
		processModel.InteractProcessDetail{
			Url:      it.Detail.Url,
			Method:   it.Detail.Method,
			AppName:  it.Detail.AppName,
			Callback: it.Detail.Callback,
		},
		processModel.InteractProcessData{},
		common2.STATUS_NORMAL,
		common2.PROCESS_STATE_INITIALIZED,
		it.Workflow.Table.Name,
		it.Workflow.Table.Description,
		it.Table.InitiatorId,
	); err != nil {
		return false, err
	}
	return true, nil
}

func HandlePostData(it *InteractTask) (pass bool, err error) {
	pModel, err := processModel.NewProcessModel(it.GetParent(), false)
	if err != nil {
		return false, err
	}
	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		it.Uuid, common2.PROCESS_TYPE_INTERACT)
	if err != nil {
		return false, err
	}
	if len(processes) == 0 {
		return true, nil
	}
	var proc *processModel.ProcessTable
	for _, p := range processes {
		if p.State == common2.PROCESS_STATE_INITIALIZED {
			proc = p
		}
	}
	if proc == nil { //只处理initialized的process
		return true, nil
	}
	procData := processModel.InteractProcessData{}
	newState := common2.PROCESS_STATE_FINISHED
	if it.Detail.Callback {
		newState = common2.PROCESS_STATE_POSTED
	}
	if len(it.Detail.Url) > 0 {
		apiOperator := apiLogic.NewApiOperator(it.GetParent().Ctx())
		paramMap := make(map[string]interface{})
		paramMap["workflowSerialId"] = it.Workflow.Table.SerialId
		paramMap["workflowUuid"] = it.Table.ParentUuid //添加条件
		paramMap["nodeUuid"] = it.Table.Uuid
		paramMap["userId"] = it.Table.UserId
		if common.IsNil(it.Data.Input) {
			paramMap["state"] = common2.STATE_FAILED
		} else {
			paramMap["state"] = common2.STATE_PENDING
		}
		paramMap["createTime"] = it.Table.CreateTime
		inputMap, isMap := it.Data.Input.(map[string]interface{})
		if !isMap {
			it.GetParent().Ctx().Log().Warningln("input is not a json map")
			paramMap["data"] = it.Data.Input
		} else {
			if it.Form.FieldPermission != nil && !it.Form.FieldPermission.AllWritable {
				for _, hiddenKey := range it.Form.FieldPermission.HiddenFields { //需要对下游系统隐藏
					delete(inputMap, hiddenKey)
				}
			}
			wCommon.DeleteDataHistory(inputMap)
			paramMap["data"] = inputMap
		}
		//请求下游系统
		resp, code, err := apiOperator.SignAndDo(it.Detail.Url, it.Detail.Method, it.Detail.AppName, paramMap, nil, 10*time.Second, 3)
		if err != nil {
			return false, err
		}
		respMap, err := common.JsonToMap(resp)
		if err != nil {
			return false, common.ErrHttpResponse
		}
		it.Data.ApiResult = respMap  //持久化node
		procData.PostData = inputMap //持久化process
		procData.Result = respMap
		if newDataStr, err := common.JsonStringEncode(procData); err != nil {
			return false, err
		} else if _, err := pModel.UpdateProcessRuntimeDataByProcessUuid(proc.Uuid, newDataStr); err != nil {
			return false, err
		}
		if code != http.StatusOK {
			return false, common.ErrHttpResponse
		}
		//解析下游返回结果
		var res apiLogic.ApiResult
		var interactRes InteractApiResult
		if err = common.JsonStringDecode(resp, &res); err != nil {
			return false, err
		} else if res.Code != common.ERR_OK && res.Code != http.StatusOK {
			return false, common.ErrBizCode
		} else if err := common.InterfaceToStruct(res.Data, &interactRes); err != nil {
			return false, err
		}
		it.Data.Comment = interactRes.Comment
		if !interactRes.Pass { //下游系统没有通过
			return false, common2.ErrInteractRejected
		} else if interactRes.Data != nil && it.Form.FieldPermission != nil { //下游系统回复了data，且其有操作权限
			if !isMap { //输入就不是个map，没法改
				return false, common2.ErrNodeInvalidInput
			}
			if !it.Form.FieldPermission.AllWritable { //下游并没有全部操作权限
				interactRes.Data = extractWritableFieldsInMap(interactRes.Data, it.Form.FieldPermission.WritableFields)
			}
			for k, v := range interactRes.Data { //对于下游系统的修改处理，存在于inputMap中的替换掉
				if _, ok := inputMap[k]; ok {
					inputMap[k] = v
				}
			}
			it.Data.Input = inputMap
		}
		//否则默认全部只读，什么也不做
	}
	//运行到这里，只剩一步更新process就算是成功了
	if rowsAffected, err := pModel.UpdateProcessRuntimeFromOldStatesByUuids([]string{proc.Uuid},
		[]common2.ProcessStateEnum{common2.PROCESS_STATE_INITIALIZED}, newState); err != nil {
		return false, err
	} else if rowsAffected == 0 { //看看是不是已经被回调了
		proc, err = pModel.QueryProcessByUuid(proc.Uuid)
		if err != nil {
			return false, err
		}
		if proc.State == common2.PROCESS_STATE_POSTED || //可能已经被其他系统回调更新了，忽略了
			proc.State == common2.PROCESS_STATE_FINISHED ||
			proc.State == common2.PROCESS_STATE_FAILED {
			return true, it.Persist()
		}
	}
	return true, it.Persist()
}

func extractWritableFieldsInMap(mapToExtract map[string]interface{}, fields []string) map[string]interface{} {
	newMap := make(map[string]interface{})
	for _, f := range fields { //过滤系统无权限操作的字段部分
		if v, ok := mapToExtract[f]; ok {
			newMap[f] = v
		}
	}
	return newMap
}

// 判断是否已满足通过条件，若满足则Finish，若不满足则Pend，若拒绝则Fail
func HandleInteractProcessCallback(it *InteractTask) (pass bool, err error) {
	if !it.Detail.Callback {
		return true, nil
	}
	pModel, err := processModel.NewProcessModel(it.GetParent(), false)
	if err != nil {
		return false, err
	}
	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		it.Uuid, common2.PROCESS_TYPE_INTERACT)
	if err != nil {
		return false, err
	}
	if len(processes) == 0 {
		return true, nil
	}
	var proc *processModel.ProcessTable
	for _, p := range processes {
		if p.State == common2.PROCESS_STATE_POSTED ||
			p.State == common2.PROCESS_STATE_FINISHED ||
			p.State == common2.PROCESS_STATE_FAILED {
			proc = p
		}
	}
	if proc == nil {
		return false, nil
	}
	if proc.State == common2.PROCESS_STATE_POSTED {
		return false, nil
	} else if proc.State == common2.PROCESS_STATE_FAILED {
		return false, common2.ErrInteractRejected
	}
	//开始处理callback
	procData := processModel.InteractProcessData{}
	if err = common.JsonStringDecode(proc.Data, &procData); err != nil {
		return false, err
	}
	//处理回调数据
	var interactRes InteractApiResult
	if err := common.InterfaceToStruct(procData.CallbackResult, &interactRes); err != nil {
		return false, err
	}
	it.Data.Comment = interactRes.Comment
	if !interactRes.Pass { //下游系统没有通过
		return false, common2.ErrInteractRejected
	} else if interactRes.Data != nil && it.Form.FieldPermission != nil { //下游系统回复了data，且其有操作权限
		inputMap, isMap := it.Data.Input.(map[string]interface{})
		if !isMap { //输入就不是个map，没法改
			return false, common2.ErrNodeInvalidInput
		}
		if !it.Form.FieldPermission.AllWritable { //下游并没有全部操作权限
			interactRes.Data = extractWritableFieldsInMap(interactRes.Data, it.Form.FieldPermission.WritableFields)
		}
		for k, v := range interactRes.Data { //对于下游系统的修改处理，存在于inputMap中的替换掉
			if _, ok := inputMap[k]; ok {
				inputMap[k] = v
			}
		}
		it.Data.Input = inputMap
	}
	//否则默认全部只读，什么也不做
	//能够运行到这里就是通过了
	return true, nil
}

func HandleFailInteractProcess(it *InteractTask) (pass bool, err error) {
	pModel, err := processModel.NewProcessModel(it.GetParent(), false)
	if err != nil {
		return true, err
	}
	_, err = pModel.SetProcessRuntimeFailedByNodeUuid(it.Table.Uuid)
	return true, err
}
