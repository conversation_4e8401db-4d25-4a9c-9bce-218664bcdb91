/**
 * @note
 * workflow的logic层结构体定义
 *
 * <AUTHOR>
 * @date 	2019-11-07
 */
package engine

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
)

/**
 * @note
 * 定义Node接口，作为Workflow的操作对象
 */
type Node interface {
	CloseChanIgnorePanic()           //已由base实现，不需要重写，关闭chan
	InitFinishChanIfClosed()         //已有base实现，不需要重写，如果chan已关闭则重新init一个
	WaitFinishChan()                 //已由base实现，不需要重写，等待chan关闭
	GetUuid() string                 //已由base实现，不需要重写，获得节点Uuid
	Pulse()                          //已由base实现，不需要重写，单次心跳
	HeartBeat()                      //已由base实现，不需要重写，保持心跳
	Wait() error                     //已由base实现，不需要重写，等待另外的进程完成此节点
	Discard() error                  //已由base实现，不需要重写，在前向节点已经失败的情况下，将Ready的节点丢弃
	State() bpmCom.WorkflowStateEnum //已由base实现，不需要重写，获得节点状态

	Load() error     //已由base实现，需要重写，从库中读取此节点信息
	Begin() error    //已由base实现，需要重写，任务开始
	Pend() error     //已由base实现，需要重写，任务阻塞
	Continue() error //已由base实现，需要重写，任务继续
	Finish() error   //已由base实现，需要重写，任务完成
	Fail() error     //已由base实现，需要重写，任务失败
	Persist() error  //已由base实现，需要重写，持久化此节点内容

	Base() *workflowModel.NodeBase         //由node实现，不需要重写，取model
	SetPrev(prev Node, idx int)            //由node实现，不需要重写，设置前向节点
	SetNext(next Node, idx int)            //由node实现，不需要重写，设置后向节点
	GetPrev() (prev []Node)                //由node实现，不需要重写，取前向节点
	GetPrevIdx(uuid string) (idxs []int)   //由node实现，不需要重写，根据前向节点的uuid取其index值，有可能有多个！
	GetNext() (next []Node)                //由node实现，不需要重写，取后向节点
	GetNextIdx(uuid string) []int          //由node实现，不需要重写，根据后向节点的uuid取其index值，有可能有多个！
	GetInput() (input []interface{})       //由node实现，不需要重写，获取此节点的input
	SetInput(input interface{}, idx int)   //由node实现，不需要重写，设置此节点的input
	GetOutput() (output []interface{})     //由node实现，不需要重写，获取此节点的output
	SetOutput(output interface{}, idx int) //由node实现，不需要重写，设置此节点的output
	GetError() (err error)                 //由node实现，不需要重写，获取此节点的运行时error
}

type node struct {
	workflowModel.NodeBase
	Workflow *Workflow     `json:"workflow"` //所属的workflow实例
	Prev     []Node        `json:"prev"`     //前面的节点
	Next     []Node        `json:"next"`     //后面的节点
	Error    error         `json:"error"`    //运行时产生的错误
	Input    []interface{} `json:"input"`    //代表此节点的输入
	Output   []interface{} `json:"output"`   //代表此节点的输出
}

func (n *node) Base() *workflowModel.NodeBase {
	return &n.NodeBase
}

func (n *node) GetPrev() []Node {
	return n.Prev
}

func (n *node) GetNext() []Node {
	return n.Next
}

func (n *node) GetPrevIdx(uuid string) (idxs []int) {
	for i, nn := range n.Prev {
		if uuid == nn.GetUuid() {
			idxs = append(idxs, i)
		}
	}
	return idxs
}

func (n *node) GetNextIdx(uuid string) (idxs []int) {
	for i, nn := range n.Next {
		if uuid == nn.GetUuid() {
			idxs = append(idxs, i)
		}
	}
	return idxs
}

func (n *node) SetPrev(node Node, index int) {
	if len(n.Prev) <= index {
		n.Prev = append(n.Prev, make([]Node, index-len(n.Prev)+1)...)
	}
	n.Prev[index] = node
}

func (n *node) SetNext(node Node, index int) {
	if len(n.Next) <= index {
		n.Next = append(n.Next, make([]Node, index-len(n.Next)+1)...)
	}
	n.Next[index] = node
}

func (n *node) SetInput(input interface{}, index int) {
	if len(n.Input) <= index {
		n.Input = append(n.Input, make([]interface{}, index-len(n.Input)+1)...)
	}
	n.Input[index] = input
}

func (n *node) SetOutput(output interface{}, index int) {
	if len(n.Output) <= index {
		n.Output = append(n.Output, make([]interface{}, index-len(n.Output)+1)...)
	}
	n.Output[index] = output
}

func (n *node) GetInput() []interface{} {
	return n.Input
}

func (n *node) GetOutput() []interface{} {
	return n.Output
}

func (n *node) GetError() (err error) {
	return n.Error
}
