/**
 * @note
 * workflow功能实体
 *
 * <AUTHOR>
 * @date 	2019-10-31
 */
package engine

import (
	"fmt"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"os"

	"github.com/sirupsen/logrus"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type Workflow struct {
	*workflowModel.WorkflowBase
	Topology
	RemoveDuplicate bool
	Error           error
}

type WorkflowDetail struct {
	Nodes           []*TopologyDetail `json:"nodes,omitempty"`
	RemoveDuplicate bool              `json:"removeDuplicate,omitempty"`
}

type TopologyDetail struct {
	TemplateUuid string               `json:"templateUuid,omitempty"`
	Uuid         string               `json:"uuid,omitempty"`
	Prev         []*TopologyDetail    `json:"prev,omitempty"`
	Next         []*TopologyDetail    `json:"next,omitempty"`
	Type         common2.NodeTypeEnum `json:"type,omitempty"`
}

type Topology struct {
	Nodes   []Node          `json:"nodes"` //所有node slice
	nodeMap map[string]Node //包含所有node的uuid->map
	Start   Node            //只能有一个无prev的node
	End     []Node          //所有无next的node
}

func NewWorkflow(op common.Operator, uuid string) (w *Workflow, err error) {
	w = &Workflow{
		Topology: Topology{
			nodeMap: make(map[string]Node),
		},
	}
	w.WorkflowBase, err = workflowModel.InitWorkflowBase(op, uuid)
	op.Ctx().Log().AppendFields(logrus.Fields{
		"workflow": uuid,
	})
	return w, err
}

func (w *Workflow) newNodeByUuidAndType(uuid string, nodeType common2.NodeTypeEnum) (Node, error) {
	switch nodeType {
	case common2.NODE_TYPE_START:
		return NewStartTask(w.GetParent(), w, uuid)
	case common2.NODE_TYPE_END:
		return NewEndTask(w.GetParent(), w, uuid)
	case common2.NODE_TYPE_EXAMPLE_TASK:
		return NewExampleTask(w.GetParent(), w, uuid)
	case common2.NODE_TYPE_GATEWAY:
		return NewGateway(w.GetParent(), w, uuid)
	case common2.NODE_TYPE_APPROVAL:
		return NewApprovalTask(w.GetParent(), w, uuid)
	case common2.NODE_TYPE_TRANSACTOR:
		return NewTransactorTask(w.GetParent(), w, uuid)
	case common2.NODE_TYPE_NOTIFY:
		return NewNotifyTask(w.GetParent(), w, uuid)
	case common2.NODE_TYPE_INTERACT:
		return NewInteractTask(w.GetParent(), w, uuid)
	default:
		return nil, common2.ErrInvalidNodeType
	}
}

func (w *Workflow) registerNodeIntoMap(uuid string, nodeType common2.NodeTypeEnum) (Node, error) {
	if existNode, ok := w.nodeMap[uuid]; !ok {
		n, err := w.newNodeByUuidAndType(uuid, nodeType)
		if err != nil {
			return nil, err
		}
		w.nodeMap[uuid] = n
		w.Nodes = append(w.Nodes, n)
	} else {
		return existNode, nil
	}
	return w.nodeMap[uuid], nil
}

func (w *Workflow) Load() error {
	//从表中加载workflow到内存
	if err := w.WorkflowBase.Load(); err != nil {
		return err
	}
	if err := staffLogic.FillCtxUserByUserId(w.GetParent().Ctx(), w.Table.UserId); err != nil {
		return err
	}
	detail := &WorkflowDetail{}
	err := common.JsonDecode([]byte(w.Table.Detail), &detail)
	if err != nil {
		return err
	}
	w.RemoveDuplicate = detail.RemoveDuplicate
	//还原workflow的node结构
	for _, n := range detail.Nodes {
		existNode, err := w.registerNodeIntoMap(n.Uuid, n.Type)
		if err != nil {
			return err
		}
		if len(n.Prev) == 0 { //Start的初始化
			w.Start = existNode
		}
		if len(n.Next) == 0 { //End的初始化
			w.End = append(w.End, existNode)
		}
		for i, prev := range n.Prev { //初始化prev们
			if prevNode, err := w.registerNodeIntoMap(prev.Uuid, prev.Type); err != nil {
				return err
			} else {
				existNode.SetPrev(prevNode, i)
			}
		}
		for i, next := range n.Next { //初始化next们
			if nextNode, err := w.registerNodeIntoMap(next.Uuid, next.Type); err != nil {
				return err
			} else {
				existNode.SetNext(nextNode, i)
			}
		}
	}
	for _, node := range w.Nodes {
		//从表中加载node到内存
		if err := node.Load(); err != nil {
			return err
		}
	}
	return nil
}

// 执行workflow的每个Node
func (w *Workflow) Begin() error {
	if err := w.WorkflowBase.Begin(); err != nil {
		return err
	}
	if w.Start == nil {
		return w.Finish()
	}
	state, err := w.Do(w.Start.GetUuid())
	if err != nil {
		w.GetParent().Ctx().Log().Errorf("workflow [%s] failed, err [%v]", w.Uuid, err)
		return w.Fail()
	}
	switch state {
	case common2.STATE_FINISHED: //节点完成情况
		return w.Finish()
	case common2.STATE_PENDING: //节点阻塞情况
		if judgeIfAllFailed(w.End) { //虽然有阻塞，但结束节点已经提前全部失败，那么直接失败此workflow
			return w.Fail()
		}
		return w.Pend()
	case common2.STATE_FAILED: //节点失败情况
		return w.Fail()
	default:
		return w.Fail() //默认情况下失败
	}
}

func (w *Workflow) Continue(nodeUuid ...string) error {
	if err := w.WorkflowBase.Continue(); err != nil {
		return err
	}
	var uuid string
	if len(nodeUuid) > 0 && len(nodeUuid[0]) > 0 {
		uuid = nodeUuid[0]
	} else if w.Start == nil {
		return w.Finish()
	} else {
		uuid = w.Start.GetUuid()
	}
	state, err := w.Do(uuid)
	if err != nil {
		w.GetParent().Ctx().Log().Errorf("workflow [%s] failed, err [%v]", w.GetUuid(), err)
		return w.Fail()
	}
	switch state {
	case common2.STATE_FINISHED: //节点完成情况
		return w.Finish()
	case common2.STATE_PENDING: //节点阻塞情况
		if judgeIfAllFailed(w.End) { //虽然有阻塞，但结束节点已经提前全部失败，那么直接失败此workflow
			return w.Fail()
		}
		return w.Pend()
	case common2.STATE_FAILED: //节点失败情况
		if judgeIfAllFailed(w.End) { //要判断一下，因为可能只是一个分支失败，不代表全部失败
			return w.Fail()
		}
		return w.Pend()
	default:
		return w.Fail() //默认情况下失败
	}
}

/**
 * @note
 * 判断传入的Node数组是否全部失败了
 * 注：如果传入为空，默认返回**未全部失败**
 */
func judgeIfAllFailed(nodes []Node) bool {
	if len(nodes) > 0 {
		for _, node := range nodes {
			if node.State() != common2.STATE_FAILED && node.State() != common2.STATE_DISCARD {
				return false
			}
		}
		return true
	}
	return false
}

/**
 * @note
 * 判断传入的Node数组是否全部处于稳定状态，
 * 且所有的已Finish节点都输出为nil的情况，为前序失败，返回true，其余返回false
 * 注：如果传入为空，默认返回**未全部是nil**
 */
func judgeIfAllStableAndFinishInputIsNil(nodes []Node, inputs []interface{}) bool {
	for _, node := range nodes {
		if state := node.State(); state != common2.STATE_FAILED &&
			state != common2.STATE_DISCARD &&
			state != common2.STATE_FINISHED {
			return false
		}
	}
	if len(nodes) > 0 {
		for idx, node := range nodes {
			if node.State() == common2.STATE_FINISHED {
				if len(inputs) <= idx || !common.IsNil(inputs[idx]) {
					return false //这个finish节点还没有输出 或 输出不为0
				}
			}
		}
		return true
	}
	return false
}

/**
 * @note
 * Workflow具体处理node的方法
 *
 * @param uuid 节点的uuid
 *
 * @return state 在此节点之后运行的节点的综合结果
 * @return err 发生的错误
 */
func (w *Workflow) Do(uuid string) (state common2.WorkflowStateEnum, err error) {
	node, ok := w.nodeMap[uuid]
	if !ok {
		return common2.STATE_FAILED, common2.ErrNodeNotExist
	}
	defer func() {
		if err := recover(); err != nil { //极少数未知panic情况的兜底
			trace := common.IdentifyPanic() //报警
			w.GetParent().Ctx().Log().Errorf("do node %s error: %+v, trace: %+v\n", uuid, err, trace)
			Alarm(w.GetParent().Ctx(), w.Uuid, node.GetUuid(), trace, "Error", fmt.Errorf("%+v", err))
			node.CloseChanIgnorePanic() //至少使其不再心跳，否则会一直卡住
			return
		}
	}()
	//step 1. 待运行/运行中/阻塞状态需要本节点做处理
	switch node.State() {
	case common2.STATE_READY: //待执行状态
		if judgeIfAllFailed(node.GetPrev()) ||
			judgeIfAllStableAndFinishInputIsNil(node.GetPrev(), node.GetInput()) { //如果所有的输入都失败了，则直接丢弃本节点
			err = node.Discard()
		} else {
			err = node.Begin()
		}
		node.WaitFinishChan() //等待节点操作完成
	case common2.STATE_EXECUTING: //被其他进程抢占的状态
		if err = node.Wait(); err != nil { //等待其他进程完成
			return node.State(), err
		}
		return w.Do(node.GetUuid()) //继续处理此节点
	case common2.STATE_PENDING: //阻塞状态
		if judgeIfAllFailed(node.GetPrev()) ||
			judgeIfAllStableAndFinishInputIsNil(node.GetPrev(), node.GetInput()) { //如果所有的输入都失败了，则直接丢弃本节点
			err = node.Discard()
		} else {
			err = node.Continue()
		}
		node.WaitFinishChan() //等待节点操作完成
	}
	if err != nil {
		Alarm(w.GetParent().Ctx(), w.Uuid, node.GetUuid(), "Do node error", "Error", err)
	} else if err = node.GetError(); err != nil {
		Alarm(w.GetParent().Ctx(), w.Uuid, node.GetUuid(), "Do node inner error", "Error", err)
	}
	//step 2.如果当前节点没有下一节点，则直接返回此节点结果
	if len(node.GetNext()) == 0 {
		return node.State(), err
	}
	//step 3.已完成/失败/已丢弃状态处理，其余直接返回
	if state := node.State(); state != common2.STATE_FINISHED &&
		state != common2.STATE_FAILED &&
		state != common2.STATE_DISCARD {
		return node.State(), err
	}
	//step 4.如果是个多输入节点，回溯丢弃前序未完成分支
	if prevs := node.GetPrev(); len(prevs) > 1 {
		for _, prev := range prevs {
			w.RecallDiscard(prev.GetUuid())
		}
	}
	//step 5.此节点已完成，那么主要关注此节点之后流程的运行结果
	//pendingCnt	此节点后的分支有几个处于pending状态
	//finishCnt		此节点后的分支有几个处于finish状态
	var pendingCnt, finishCnt int
	nextUuidOutCntMap := make(map[string]int) //FIXME 一个计数器，计数对每个nextNode的输出次数（处理有好事者将两个GATEWAY连在一起的情况）
	outputs := node.GetOutput()
	for i, nextNode := range node.GetNext() {
		//step 5.1. 设置前节点输出为后节点输入
		var out interface{}
		if len(outputs) > i {
			out = outputs[i]
		}
		//FIXME 处理有好事者将两个GATEWAY连在一起的情况（A与B之间有多条直连），按照顺序向nextNode一条一条逐个输出
		nextNode.SetInput(out, nextNode.GetPrevIdx(node.GetUuid())[nextUuidOutCntMap[nextNode.GetUuid()]])
		nextUuidOutCntMap[nextNode.GetUuid()] = nextUuidOutCntMap[nextNode.GetUuid()] + 1 //计数+1
	}
	for _, nextNode := range node.GetNext() {
		//step 5.2. 处理每个后续节点
		if nextStatus, err := w.Do(nextNode.GetUuid()); err != nil { //TODO 并发优化
			return common2.STATE_FAILED, err
		} else if nextStatus == common2.STATE_PENDING {
			pendingCnt++
		} else if nextStatus == common2.STATE_FINISHED {
			finishCnt++
		}
	}
	//step 5.3 统计几个后续节点的成果，有一个finish则认为成功
	if finishCnt > 0 {
		return common2.STATE_FINISHED, nil
	} else if pendingCnt > 0 {
		return common2.STATE_PENDING, nil
	}
	return common2.STATE_FAILED, nil
}

/**
 * @note
 * 当一个具有多个input的节点已经完成以后，需要令它的一些尚未完成的前序节点失效掉
 * @param uuid 节点的uuid
 * @return err 发生的错误
 */
func (w *Workflow) RecallDiscard(uuid string) (err error) {
	node, ok := w.nodeMap[uuid]
	if !ok {
		return common2.ErrNodeNotExist
	}
	//step 1.如果节点已经是稳定状态，直接结束
	if state := node.State(); state == common2.STATE_EXECUTING ||
		state == common2.STATE_FINISHED ||
		state == common2.STATE_FAILED ||
		state == common2.STATE_DISCARD {
		return
	}
	//step 2.如果节点还有输出不是稳定状态，直接结束
	for _, next := range node.GetNext() {
		if state := next.State(); state != common2.STATE_FINISHED &&
			state != common2.STATE_FAILED &&
			state != common2.STATE_DISCARD {
			return
		}
	}
	err = node.Discard()
	if err != nil {
		Alarm(w.GetParent().Ctx(), w.Uuid, uuid, "Recall discard node error", "Error", err)
	}
	for _, prev := range node.GetPrev() {
		w.RecallDiscard(prev.GetUuid())
	}
	return
}

func Alarm(ctx common.HContextIface, workflowUuid, nodeUuid, message string, level string, err error) {
	if err == common2.ErrApproverRejected || err == common2.ErrGatewayImpossibleToPass ||
		err == common2.ErrGatewayNoOutput || err == common.ErrBizCode || err == common2.ErrInteractRejected { //这些错误不报警
		return
	}
	content, _ := common.ToPrettyJsonString(map[string]interface{}{
		"env":   common.GetEnv(),
		"trace": ctx.Log().GetTrace(),
		"content": map[string]interface{}{
			"message":      message,
			"workflowUuid": workflowUuid,
			"nodeUuid":     nodeUuid,
			"level":        level,
			"error": func() string {
				if err != nil {
					return err.Error()
				} else {
					return common.StringEmpty
				}
			}(),
		},
		"host": func() string {
			host, _ := os.Hostname()
			return host
		}(),
	})
	errSend := lark.SendLarkRobotAlarmMessage(ctx.GinCtx(), content)
	if level == "Warning" {
		ctx.Log().Warningf("process workflow[%s] node [%s] error [%v]", workflowUuid, nodeUuid, err)
	} else {
		ctx.Log().Errorf("process workflow[%s] node [%s] error [%v]", workflowUuid, nodeUuid, err)
	}
	if err != nil {
		ctx.Log().Errorln("send dingtalk message err:", errSend)
	}
}
