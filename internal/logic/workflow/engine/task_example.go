/**
 * @note
 * workflow中实现了Node接口的实体，一个task的例子
 * 具体功能是将input加一个"world"后输出
 *
 * <AUTHOR>
 * @date 	2019-10-31
 */
package engine

import (
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"math/rand"
	"time"
)

type ExampleTaskData struct {
	Input  map[string]interface{} `json:"input,omitempty"`
	Output map[string]interface{} `json:"output,omitempty"`
}

////////////////////handle func////////////////////

func NewExampleTask(op common.Operator, w *Workflow, uuid string) (t *ExampleTask, err error) {
	t = &ExampleTask{}
	t.NodeBase, err = workflowModel.InitNodeBase(op, uuid)
	t.Workflow = w
	return t, err
}

type ExampleTask struct {
	node
	Data *ExampleTaskData
}

func (t *ExampleTask) Load() error {
	if err := t.NodeBase.Load(); err != nil {
		return err
	}
	t.Data = &ExampleTaskData{} //将表中的data还原到task中
	if err := common.JsonDecode([]byte(t.Table.Data), &t.Data); err != nil {
		return err
	}
	t.SetInput(t.Data.Input, 0)
	t.SetOutput(t.Data.Output, 0)
	return nil
}
func (t *ExampleTask) Begin() error {
	//先将任务begin
	if err := t.NodeBase.Begin(); err != nil {
		return err
	}
	//模拟20s后完成,for test
	go func() {
		t.GetParent().Ctx().Log().Printf("node [%s] begin...", t.Uuid)
		time.Sleep(time.Duration(rand.Intn(3)) * time.Second)
		t.GetParent().Ctx().Log().Printf("node [%s] input [%v], processing...", t.Uuid, t.Input[0])
		time.Sleep(time.Duration(rand.Intn(3)) * time.Second)
		t.Output = make([]interface{}, 1)
		t.Data.Input = t.Input[0].(map[string]interface{})
		out := make(map[string]interface{})
		for k, v := range t.Data.Input {
			out[k] = v
		}
		out[common.RandNumString(5)] = common.RandNumString(5)
		t.Data.Output = out
		t.Output[0] = t.Data.Output
		if err := t.Persist(); err != nil {
			t.Fail()
		} else {
			t.Finish()
		}
	}()
	return nil
}

func (t *ExampleTask) Finish() error {
	if err := t.NodeBase.Finish(); err != nil {
		return err
	}
	t.GetParent().Ctx().Log().Printf("node [%s] finished...", t.Uuid)
	return nil
}
func (t *ExampleTask) Fail() error {
	t.GetParent().Ctx().Log().Printf("node [%s] failed...", t.Uuid)
	return t.NodeBase.Fail()
}
func (t *ExampleTask) Persist() error {
	t.GetParent().Ctx().Log().Printf("node [%s] output [%v], persisting...", t.Uuid, t.Output[0])
	d, err := common.JsonEncode(t.Data)
	if err != nil {
		return err
	}
	t.Table.Data = string(d)
	return t.NodeBase.Persist()
}
