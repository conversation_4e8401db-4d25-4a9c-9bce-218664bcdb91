/**
 * @note
 * task_end
 *
 * <AUTHOR>
 * @date 	2019-12-09
 */
package engine

import (
	"encoding/json"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	apiLogic "gitlab.docsl.com/security/bpm/internal/logic/api"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	notifyLogic "gitlab.docsl.com/security/bpm/internal/logic/notify"
	wCommon "gitlab.docsl.com/security/bpm/internal/logic/workflow/common"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"net/http"
	"time"
)

func init() {
	endBeginHandlers = []EndHandlerFunc{
		HandleEndInput,
		HandleCallApi,
		HandleNotifyWorkflowOwnerFinished,
		HandleNotifyWorkflowStakeholdersFinished,
	}
	endFailHandlers = []EndHandlerFunc{
		HandleNotifyWorkflowOwnerFailed,
		HandleNotifyWorkflowStakeholdersFailed,
	}
	endDiscardHandlers = []EndHandlerFunc{
		HandleCallApi,
		HandleNotifyWorkflowOwnerFailed,
		HandleNotifyWorkflowStakeholdersFailed,
	}
}

////////////////////init////////////////////

type EndHandlerFunc func(t *EndTask) (pass bool, err error)

var (
	endBeginHandlers   []EndHandlerFunc
	endFailHandlers    []EndHandlerFunc
	endDiscardHandlers []EndHandlerFunc
)

type EndTaskDetail struct {
	Name            json.RawMessage          `json:"name"` //给前端加的回显字段
	Api             ApiConfig                `json:"api"`
	Notify          notifyLogic.NotifyConfig `json:"notify"`
	NotifyApprovers bool                     `json:"notifyApprovers"` //是否在完成时通知所有已审批人
}

type ApiConfig struct {
	Url     string   `json:"url"`
	Method  string   `json:"method"`
	AppName string   `json:"appName"`
	Keys    []string `json:"keys"`
}

type EndTaskData struct {
	Input     interface{} `json:"input"`
	Output    interface{} `json:"output"`
	ApiResult interface{} `json:"apiResult,omitempty"`
}

type ApiPostData struct {
	WorkflowUuid string                    `json:"workflowUuid"`
	State        common2.WorkflowStateEnum `json:"state"`
	Data         interface{}               `json:"data,omitempty"`
}

type EndTask struct {
	node
	Detail EndTaskDetail
	Form   formLogic.FormConfig
	Data   EndTaskData
}

func NewEndTask(op common.Operator, w *Workflow, uuid string) (t *EndTask, err error) {
	t = &EndTask{}
	t.NodeBase, err = workflowModel.InitNodeBase(op, uuid)
	t.Workflow = w
	return t, err
}

func (et *EndTask) Persist() error {
	d, err := common.JsonEncode(et.Data)
	if err != nil {
		return err
	}
	et.Table.Data = string(d)
	return et.NodeBase.Persist()
}

func (et *EndTask) Load() error {
	if err := et.NodeBase.Load(); err != nil {
		return err
	}
	//将表中的data还原到task中
	if err := common.JsonDecode([]byte(et.Table.Data), &et.Data); err != nil {
		return err
	}
	//将表中的detail还原到Detail中
	if err := common.JsonDecode([]byte(et.Table.Detail), &et.Detail); err != nil {
		return err
	}
	//将表中的front还原到form中
	if err := common.JsonDecode([]byte(et.Table.Front), &et.Form); err != nil {
		return err
	}
	et.SetInput(et.Data.Input, 0)
	et.SetOutput(et.Data.Output, 0)
	return nil
}

func (et *EndTask) Begin() error {
	//先将任务begin
	if err := et.node.Begin(); err != nil {
		return err
	}
	for _, handler := range endBeginHandlers {
		pass, err := handler(et)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				et.Error = err

			}
			return et.Fail()
		}
	}
	return et.Finish()
}

func (et *EndTask) Fail() error {
	//先执行Fail前的流程
	for _, handler := range endFailHandlers {
		pass, err := handler(et)
		if !pass || err != nil {
			et.Error = err
		}
	}
	return et.node.Fail()
}

func (et *EndTask) Discard() error {
	//先执行Discard前的流程
	for _, handler := range endDiscardHandlers {
		pass, err := handler(et)
		if !pass || err != nil {
			et.Error = err
		}
	}
	return et.node.Discard()
}

////////////////////handle func////////////////////

func HandleEndInput(et *EndTask) (pass bool, err error) {
	if !common.IsNil(et.Data.Input) {
		return true, nil
	}
	inputs := et.GetInput()
	if len(inputs) == 0 || common.IsNil(inputs[0]) {
		return false, common2.ErrNodeInvalidInput
	}
	et.Data.Input = inputs[0]
	return true, et.Persist()
}

func HandleCallApi(et *EndTask) (pass bool, err error) {
	if len(et.Detail.Api.Url) > 0 {
		apiOperator := apiLogic.NewApiOperator(et.GetParent().Ctx())
		paramMap := make(map[string]interface{})
		paramMap["workflowUuid"] = et.Table.ParentUuid //添加条件
		if common.IsNil(et.Data.Input) {
			paramMap["state"] = common2.STATE_FAILED
		} else {
			paramMap["state"] = common2.STATE_FINISHED
			m, ok := et.Data.Input.(map[string]interface{})
			if !ok {
				et.GetParent().Ctx().Log().Warningln("input is not a json map")
				paramMap["data"] = et.Data.Input
			} else if len(et.Detail.Api.Keys) > 0 { //需要提取参数
				data := make(map[string]interface{})
				for _, key := range et.Detail.Api.Keys {
					if v, ok := m[key]; ok {
						data[key] = v
					}
				}
				wCommon.DeleteDataHistory(data)
				paramMap["data"] = data
			} else {
				wCommon.DeleteDataHistory(m)
				paramMap["data"] = m
			}
		}
		resp, code, err := apiOperator.SignAndDo(et.Detail.Api.Url, et.Detail.Api.Method, et.Detail.Api.AppName, paramMap, nil, 10*time.Second, 3)
		if err != nil {
			return false, err
		} else if code != http.StatusOK {
			return false, common.ErrHttpResponse
		}
		et.Data.ApiResult = resp
		et.Persist()
		resMap := make(map[string]interface{})
		var res apiLogic.ApiResult
		if err := common.JsonStringDecode(resp, &resMap); err != nil {
			return false, err
		} else if _, ok := resMap["code"]; !ok {
			return false, common.ErrHttpResponse
		} else if err = common.JsonStringDecode(resp, &res); err != nil {
			return false, err
		} else if res.Code != common.ERR_OK && res.Code != http.StatusOK {
			return false, common.ErrBizCode
		}
	}
	return true, et.Persist()
}

// 读取流程创建者，提单人发送完成通知
func HandleNotifyWorkflowOwnerFinished(et *EndTask) (pass bool, err error) {
	workflowName, initiatorName := GetWorkflowNameAndUserName(et.GetParent().Ctx(), et.Workflow.Table)
	emailContent, err := GenerateEmailContentByTmpl(workflowName, initiatorName, FINISHED_EMAIL_TMPL, GetToCWorkflowUrl(et.Table.ParentUuid), nil)
	if err != nil {
		emailContent = GenerateEmailFinishedContent(et.Table.ParentUuid)
	}

	notifyOperator := notifyLogic.NewNotifyOperator(et.GetParent().Ctx())
	if err := notifyOperator.SendNotify(et.Table.UserId,
		notifyLogic.NotifyMsg{
			LarkContent: notifyLogic.GenerateLarkFinishedContent(workflowName, et.Table.ParentUuid, et.Workflow.Table.CreateTime, notifyLogic.LarkInteractiveCustomTemplate{}, nil),
			MailTitle:   "流程通过通知",
			MailContent: emailContent,
		}, []common2.NotifyTypeEnum{common2.NotifyTypeEmail, common2.NotifyTypeLark}); err != nil {
		et.GetParent().Ctx().Log().Errorf("send notify to user [%d] error [%v]", et.Table.UserId, err)
	}
	return true, nil
}

// 读取流程创建者，提单人发送完成通知
func HandleNotifyWorkflowOwnerFailed(et *EndTask) (pass bool, err error) {
	workflowName, initiatorName := GetWorkflowNameAndUserName(et.GetParent().Ctx(), et.Workflow.Table)
	emailContent, err := GenerateEmailContentByTmpl(workflowName, initiatorName, FAILED_EMAIL_TMPL, GetToCWorkflowUrl(et.Table.ParentUuid), nil)
	if err != nil {
		emailContent = GenerateEmailFailedContent(et.Table.ParentUuid)
	}

	notifyOperator := notifyLogic.NewNotifyOperator(et.GetParent().Ctx())
	if err := notifyOperator.SendNotify(et.Table.UserId,
		notifyLogic.NotifyMsg{
			LarkContent: notifyLogic.GenerateLarkFailedContent(workflowName, et.Table.ParentUuid, et.Workflow.Table.CreateTime, notifyLogic.LarkInteractiveCustomTemplate{}, nil),
			MailTitle:   "流程失败通知",
			MailContent: emailContent,
		}, []common2.NotifyTypeEnum{common2.NotifyTypeEmail, common2.NotifyTypeLark}); err != nil {
		et.GetParent().Ctx().Log().Errorf("send notify to user [%d] error [%v]", et.Table.UserId, err)
	}
	return true, nil
}

func SortStakeholderProcessToNotify(processes []*processModel.ProcessTable, exceptNodeUuid string, exceptUserIds []int64, includeNotifiers bool) (
	ret []*processModel.ProcessTable) {
	m := make(map[int64]bool) //用于去重
	for _, userId := range exceptUserIds {
		m[userId] = true
	}
	for _, proc := range processes { //目标节点的所有人不应该收到通知
		if proc.NodeUuid == exceptNodeUuid {
			m[proc.UserId] = true
		}
	}
	for _, proc := range processes {
		if m[proc.UserId] {
			continue
		}
		if proc.Type == common2.PROCESS_TYPE_APPROVAL &&
			(proc.State == common2.PROCESS_STATE_APPROVED ||
				proc.State == common2.PROCESS_STATE_REJECTED) {
			m[proc.UserId] = true
			ret = append(ret, proc)
		} else if proc.Type == common2.PROCESS_TYPE_NOTIFY &&
			(proc.State == common2.PROCESS_STATE_NOT_NOTIFIED || proc.State == common2.PROCESS_STATE_NOTIFIED) &&
			includeNotifiers {
			m[proc.UserId] = true
			ret = append(ret, proc)
		} else if proc.Type == common2.PROCESS_TYPE_TRANSACTOR &&
			proc.State == common2.PROCESS_STATE_DONE {
			m[proc.UserId] = true
			ret = append(ret, proc)
		}
	}
	return
}

// 读取流程审批和抄送过的人，发送完成通知
func HandleNotifyWorkflowStakeholdersFinished(et *EndTask) (pass bool, err error) {
	if !et.Detail.NotifyApprovers {
		return true, nil
	}
	pModel, err := processModel.NewProcessModel(et.GetParent(), false)
	if err != nil {
		return false, err
	}
	allProcesses, err := pModel.QueryProcessRuntimeByWorkflowUuid(
		et.Workflow.Table.Uuid)
	if err != nil {
		et.GetParent().Ctx().Log().Errorf("query process error [%v]", err)
		return true, nil
	}
	processesToNotify := SortStakeholderProcessToNotify(allProcesses, common.StringEmpty, []int64{et.Table.UserId}, true) //这块要除去发起人（万一发起人也是审批人）
	if len(processesToNotify) == 0 {
		return true, nil
	}

	workflowName, initiatorName := GetWorkflowNameAndUserName(et.GetParent().Ctx(), et.Workflow.Table)
	for _, proc := range processesToNotify {
		if proc.UserId == et.Table.UserId {
			continue
		}
		var action, result string
		if proc.Type == common2.PROCESS_TYPE_NOTIFY {
			action = "抄送"
		} else {
			action = "已处理"
		}
		result = "已通过"
		emailContent, err := GenerateEmailContentByTmpl(workflowName, initiatorName, FINISHED_NOTIFY_OTHERS_EMAIL_TMPL, GetToCProcessUrl(proc.Uuid), map[string]interface{}{
			"action": action,
		})
		if err != nil {
			emailContent = GenerateEmailFinishedNotifyOthersContent(proc.Uuid)
		}
		notifyOperator := notifyLogic.NewNotifyOperator(et.GetParent().Ctx())
		if err := notifyOperator.SendNotify(proc.UserId,
			notifyLogic.NotifyMsg{
				LarkContent: notifyLogic.GenerateLarkResultNotifyOthersContent(workflowName, initiatorName, proc.Uuid, action, result, et.Workflow.Table.CreateTime, et.Detail.Notify.LarkCustomTemplate, et.Data),
				MailTitle:   "流程结果通知",
				MailContent: emailContent,
			}, []common2.NotifyTypeEnum{common2.NotifyTypeEmail, common2.NotifyTypeLark}); err != nil {
			et.GetParent().Ctx().Log().Errorf("send notify to user [%d] error [%v]", et.Table.UserId, err)
		}
	}

	return true, nil
}

// 读取流程审批和抄送过的人，发送失败通知
func HandleNotifyWorkflowStakeholdersFailed(et *EndTask) (pass bool, err error) {
	if !et.Detail.NotifyApprovers {
		return true, nil
	}
	pModel, err := processModel.NewProcessModel(et.GetParent(), false)
	if err != nil {
		return false, err
	}
	allProcesses, err := pModel.QueryProcessRuntimeByWorkflowUuid(
		et.Workflow.Table.Uuid)
	if err != nil {
		et.GetParent().Ctx().Log().Errorf("query process error [%v]", err)
		return true, nil
	}
	processesToNotify := SortStakeholderProcessToNotify(allProcesses, common.StringEmpty, []int64{et.Table.UserId}, true) //这块要除去发起人（万一发起人也是审批人）
	if len(processesToNotify) == 0 {
		return true, nil
	}

	workflowName, initiatorName := GetWorkflowNameAndUserName(et.GetParent().Ctx(), et.Workflow.Table)
	for _, proc := range processesToNotify {
		if proc.UserId == et.Table.UserId {
			continue
		}
		var action, result string
		if proc.Type == common2.PROCESS_TYPE_NOTIFY {
			action = "抄送"
		} else {
			action = "已处理"
		}
		result = "已拒绝"
		emailContent, err := GenerateEmailContentByTmpl(workflowName, initiatorName, FAILED_NOTIFY_OTHERS_EMAIL_TMPL, GetToCProcessUrl(proc.Uuid), map[string]interface{}{
			"action": action,
		})
		if err != nil {
			emailContent = GenerateEmailFailedNotifyOthersContent(proc.Uuid)
		}
		notifyOperator := notifyLogic.NewNotifyOperator(et.GetParent().Ctx())
		if err := notifyOperator.SendNotify(proc.UserId,
			notifyLogic.NotifyMsg{
				LarkContent: notifyLogic.GenerateLarkResultNotifyOthersContent(workflowName, initiatorName, proc.Uuid, action, result, et.Workflow.Table.CreateTime, et.Detail.Notify.LarkCustomTemplate, et.Data),
				MailTitle:   "流程结果通知",
				MailContent: emailContent,
			}, []common2.NotifyTypeEnum{common2.NotifyTypeEmail, common2.NotifyTypeLark}); err != nil {
			et.GetParent().Ctx().Log().Errorf("send notify to user [%d] error [%v]", et.Table.UserId, err)
		}
	}
	return true, nil
}
