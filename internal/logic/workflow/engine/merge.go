/**
 * @note
 * 合并多个来源的数据
 *
 * <AUTHOR>
 * @date 	2019-11-14
 */
package engine

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type MergeConfig struct {
	Mode  bpmCom.MergeModeEnum `json:"mode"`
	Rules []*MergeRule         `json:"rules,omitempty"`
}

type MergeRule struct {
	All  bool     `json:"all"`
	Keys []string `json:"keys,omitempty"`
}

type MergeOperator struct {
	MergeConfig
}

func NewMergeOperator(config MergeConfig) *MergeOperator {
	return &MergeOperator{config}
}

func (op *MergeOperator) Merge(targets []interface{}) (result interface{}) {
	if len(targets) == 0 {
		return nil
	}
	result = targets[0]
	var ruleA, ruleB *MergeRule
	if len(op.Rules) > 0 {
		ruleA = op.Rules[0]
	}
	for i := 1; i < len(targets); i++ {
		if len(op.Rules) > i {
			ruleB = op.Rules[i]
		} else {
			ruleB = nil
		}
		result, ruleA = op.MergeTwo(result, targets[i], ruleA, ruleB)
	}
	return
}

func (op *MergeOperator) MergeTwoObj(targetA, targetB interface{}, ruleA, ruleB *MergeRule) (result interface{}, rule *MergeRule) {
	sliceA, okA := targetA.([]interface{})
	sliceB, okB := targetB.([]interface{})
	if op.Mode != bpmCom.MERGE_MODE_OVERRIDE || (!okA && !okB) { //直接覆盖模式，或者两个都不是数组
		if !common.IsNil(targetB) {
			return targetB, ruleB
		} else {
			return targetA, ruleA
		}
	} else if okA && okB { //两个都是数组
		return append(sliceA, sliceB...), ruleA
	} else if okA && !okB { //一个是数组一个不是
		if common.IsNil(targetB) {
			return sliceA, ruleA
		} else {
			return append(sliceA, targetB), ruleA
		}
	} else {
		if common.IsNil(targetA) {
			return sliceB, ruleB
		} else {
			return append(sliceB, targetA), ruleB
		}
	}
}

func (op *MergeOperator) MergeTwo(targetA, targetB interface{}, ruleA, ruleB *MergeRule) (result interface{}, rule *MergeRule) {
	mapA, okA := targetA.(map[string]interface{})
	mapB, okB := targetB.(map[string]interface{})
	if !okA || !okB {
		return op.MergeTwoObj(targetA, targetB, ruleA, ruleB)
	}
	resultMap := make(map[string]interface{})
	//根据规则合并
	if ruleA == nil || ruleA.All {
		for keyA, valueA := range mapA {
			resultMap[keyA] = valueA
		}
	} else {
		for _, keyA := range ruleA.Keys {
			if valueA, ok := mapA[keyA]; ok {
				resultMap[keyA] = valueA
			}
		}
	}
	setValueByMode := func(keyB string, valueB interface{}) {
		valueA, ok := resultMap[keyB]
		if !ok { //key不存在
			resultMap[keyB] = valueB
		} else if op.Mode == bpmCom.MERGE_MODE_COMBINE { //往下层就没有rule了，默认都是all
			resultMap[keyB], _ = op.MergeTwo(valueA, valueB, nil, nil)
		} else { //否则默认override
			resultMap[keyB] = valueB
		}
	}
	//根据规则合并
	if ruleB == nil || ruleB.All {
		for keyB, valueB := range mapB {
			setValueByMode(keyB, valueB)
		}
	} else {
		for _, keyB := range ruleB.Keys {
			if valueB, ok := mapB[keyB]; ok {
				setValueByMode(keyB, valueB)
			}
		}
	}
	return resultMap, nil
}
