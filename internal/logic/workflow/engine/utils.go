/**
 * @note
 * 一些工具
 *
 * <AUTHOR>
 * @date 	2019-12-08
 */
package engine

import (
	"fmt"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"strconv"
	"time"

	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/tmpl_parser"
)

const (
	FAILED_EMAIL_TMPL                 = "failed-email.html"
	FINISHED_EMAIL_TMPL               = "finished-email.html"
	NEED_APPROVE_EMAIL_TMPL           = "need-approve-email.html"
	NEED_HANDLE_EMAIL_TMPL            = "need-handle-email.html"
	NOTIFY_EMAIL_TMPL                 = "notify-email.html"
	REMARK_EMAIL_TMPL                 = "remark-email.html"
	ROLLBACK_EMAIL_TMPL               = "rollback-email.html"
	NEED_COMPLETE_EMAIL_TMPL          = "need-complete-email.html"
	DONE_BY_OTHERS_EMAIL_TMPL         = "done-by-others-email.html"
	ROLLBACK_NOTIFY_OTHERS_EMAIL_TMPL = "rollback-notify-others-email.html"
	WITHDRAW_NOTIFY_OTHERS_EMAIL_TMPL = "withdraw-notify-others-email.html"
	FINISHED_NOTIFY_OTHERS_EMAIL_TMPL = "finished-notify-others-email.html"
	FAILED_NOTIFY_OTHERS_EMAIL_TMPL   = "failed-notify-others-email.html"
)

/**
 * @note
 * 提取Workflow_table中的detail(str)，转化成node的拓扑
 * template与runtime的detail均可使用
 */
func GetNodeUuidNextUuidsMapByWorkflowDetailStr(detail string) (nodeUuidNextUuidsMap map[string][]string, topo *WorkflowDetail, err error) {
	topo = &WorkflowDetail{}
	err = common.JsonDecode([]byte(detail), &topo)
	if err != nil {
		return nil, nil, err
	}
	return GetNodeUuidNextUuidsMapByWorkflowDetail(topo)
}

func GetNodeUuidNextUuidsMapByWorkflowDetail(topo *WorkflowDetail) (nodeUuidNextUuidsMap map[string][]string, d *WorkflowDetail, err error) {
	if topo == nil {
		return nil, nil, common.ErrInvalidFieldf("topo")
	}
	nodeUuidNextUuidsMap = make(map[string][]string)
	for _, topologyDetail := range topo.Nodes {
		if _, ok := nodeUuidNextUuidsMap[topologyDetail.TemplateUuid]; !ok {
			nodeUuidNextUuidsMap[topologyDetail.TemplateUuid] = make([]string, 0)
		}
		for _, next := range topologyDetail.Next {
			nodeUuidNextUuidsMap[topologyDetail.TemplateUuid] =
				append(nodeUuidNextUuidsMap[topologyDetail.TemplateUuid], next.TemplateUuid)
		}
	}
	return nodeUuidNextUuidsMap, topo, nil
}

func GetWorkflowNameAndUserName(ctx common.HContextIface, w *workflowModel.WorkflowTable) (workflowName, userName string) {
	i18nName, err := common2.NewI18nString(w.Name)
	if err != nil {
		workflowName = w.Name
	} else {
		workflowName = i18nName.GetOneByOrder()
	}
	staffOperator := staffLogic.NewStaffOperator(ctx)
	userInfo, err := staffOperator.GetUserInfoByUserId(w.UserId)
	if err != nil {
		userName = "某申请人"
	} else if len(userInfo.NameZh) > 0 {
		userName = userInfo.NameZh
	} else if len(userInfo.NameDisplay) > 0 {
		userName = userInfo.NameDisplay
	} else if len(userInfo.NameEn) > 0 {
		userName = userInfo.NameEn
	} else {
		userName = "某申请人"
	}
	return
}

func GetProcessUserName(ctx common.HContextIface, p *processModel.ProcessTable) (userName string) {
	staffOperator := staffLogic.NewStaffOperator(ctx)
	userInfo, err := staffOperator.GetUserInfoByUserId(p.UserId)
	if err != nil {
		userName = "某审批人"
	} else if len(userInfo.NameZh) > 0 {
		userName = userInfo.NameZh
	} else if len(userInfo.NameDisplay) > 0 {
		userName = userInfo.NameDisplay
	} else if len(userInfo.NameEn) > 0 {
		userName = userInfo.NameEn
	} else {
		userName = "某审批人"
	}
	return
}

func getTimeFormatByStamp(timeStamp int64) string {
	return time.Unix(timeStamp, 0).In(common.LocalLocation).Format("2006-01-02 15:04:05")
}

func GenerateEmailContentByTmpl(workflowName, initiatorName, tmplName, url string, customParams map[string]interface{}) (string, error) {
	d := map[string]interface{}{
		"initiator":    initiatorName,
		"workflowName": workflowName,
		"url":          url,
	}
	for k, v := range customParams {
		d[k] = v
	}
	return tmpl_parser.Parse(tmplName, d)
}

// Desperate
func GenerateEmailNotifyContent(processUuid string) string {
	return fmt.Sprintf(`您有一个流程待查看，请及时查看<br> <h3>详情：</h3> %s`, GetToCProcessUrl(processUuid))
}

func GenerateEmailRemarkContent(processUuid string) string {
	return fmt.Sprintf(`您有一个流程评论待查看，请及时查看<br> <h3>详情：</h3> %s`, GetToCProcessUrl(processUuid))
}

func GenerateEmailRemarkContentByWorkflowUuid(workflowUuid string) string {
	return fmt.Sprintf(`您有一个流程评论待查看，请及时查看<br> <h3>详情：</h3> %s`, GetToCWorkflowUrl(workflowUuid))
}

func GenerateEmailApprovalContent(processUuid string) string {
	return fmt.Sprintf(`您有一个流程待审批，请及时处理<br> <h3>详情：</h3> %s`, GetToCProcessUrl(processUuid))
}

func GenerateEmailHandleContent(processUuid string) string {
	return fmt.Sprintf(`您有一个流程待办理，请及时处理<br> <h3>详情：</h3> %s`, GetToCProcessUrl(processUuid))
}

func GenerateEmailFinishedContent(workflowUuid string) string {
	return fmt.Sprintf(`您有一个流程已完成，请及时查看<br> <h3>详情：</h3> %s`, GetToCWorkflowUrl(workflowUuid))
}

func GenerateEmailFailedNotifyOthersContent(workflowUuid string) string {
	return fmt.Sprintf(`您有一条已处理的流程已失败，请及时查看<br> <h3>详情：</h3> %s`, GetToCWorkflowUrl(workflowUuid))
}

func GenerateEmailFinishedNotifyOthersContent(workflowUuid string) string {
	return fmt.Sprintf(`您有一条已处理的流程已通过，请及时查看<br> <h3>详情：</h3> %s`, GetToCWorkflowUrl(workflowUuid))
}

func GenerateEmailFailedContent(workflowUuid string) string {
	return fmt.Sprintf(`您有一个流程已失败，请及时查看<br> <h3>详情：</h3> %s`, GetToCWorkflowUrl(workflowUuid))
}

func GenerateEmailRollbackedContent(workflowUuid string) string {
	return fmt.Sprintf(`您有一个流程已被退回，请修改并重新提交<br> <h3>详情：</h3> %s`, GetToCWorkflowUrl(workflowUuid))
}

func GenerateEmailWithdrawNotifyOthersContent(workflowUuid string) string {
	return fmt.Sprintf(`您有一条已处理的流程被撤回，请及时查看<br> <h3>详情：</h3> %s`, GetToCWorkflowUrl(workflowUuid))
}

func GenerateEmailRollbackNotifyOthersContent(processUuid string) string {
	return fmt.Sprintf(`您有一条已处理的流程被退回，请及时查看<br> <h3>详情：</h3> %s`, GetToCProcessUrl(processUuid))
}

func GenerateEmailNeedCompleteContent(workflowUuid string) string {
	return fmt.Sprintf(`您有一个流程需要补充资料，请填写并再次提交<br> <h3>详情：</h3> %s`, GetToCWorkflowUrl(workflowUuid))
}

func GenerateEmailNotifyOtherApprovalContent(processUuid string) string {
	return fmt.Sprintf(`您有一个流程已被已被他人处理，请及时查看<br> <h3>详情：<h3> %s`, GetToCProcessUrl(processUuid))
}

func GetToCProcessUrl(processUuid string) string {
	return common2.GetConfig().ToCAddr + "business/approval-form/edit/" + processUuid +
		"?timestamp=" + strconv.FormatInt(time.Now().Unix(), 10)
}

func GetToCWorkflowUrl(workflowUuid string) string {
	return common2.GetConfig().ToCAddr + "business/approval-form/initiate/" + workflowUuid +
		"?timestamp=" + strconv.FormatInt(time.Now().Unix(), 10)
}

func GeneratePortalTaskEmailContent(processDetailUrl string) string {
	return fmt.Sprintf(`您有一个任务待处理，请及时查看<br> <h3>详情：</h3> %s`, processDetailUrl)
}
