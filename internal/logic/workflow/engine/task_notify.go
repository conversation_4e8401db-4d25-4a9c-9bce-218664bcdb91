/**
 * @note
 * 审批task
 *
 * <AUTHOR>
 * @date 	2019-11-18
 */
package engine

import (
	"encoding/json"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	notifyLogic "gitlab.docsl.com/security/bpm/internal/logic/notify"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

////////////////////init////////////////////

func init() {
	notifyBeginHandlers = []NofifyHandlerFunc{
		HandleNotifyInput,
		HandleInitializedNotifyProcess,
		HandleNotifyPeopleToBeNotified,
		HandleNotifyInputToOutput,
	}
	notifyDiscardHandlers = []NofifyHandlerFunc{
		HandleDiscardInitializedNotifyProcess,
	}
}

////////////////////init////////////////////

type NofifyHandlerFunc func(*NotifyTask) (pass bool, err error)

var (
	notifyBeginHandlers   []NofifyHandlerFunc
	notifyDiscardHandlers []NofifyHandlerFunc
)

type NotifyTaskDetail struct {
	Name          json.RawMessage            `json:"name"` //给前端加的回显字段
	Staff         []*staffLogic.StaffInfo    `json:"staff"`
	StaffFromForm []*formLogic.StaffFromForm `json:"staffFromForm"` //从表单中获取联系人
	SelfSelect    bool                       `json:"selfSelect"`    //自选抄送人
	Notify        notifyLogic.NotifyConfig   `json:"notify"`
	IsEmpty       bool                       `json:"isEmpty"` //是否空了
}

type NotifyTaskData struct {
	Input  interface{} `json:"input"`
	Output interface{} `json:"output"`
}

type NotifyTask struct {
	node
	Detail NotifyTaskDetail
	Data   NotifyTaskData
	Form   formLogic.FormConfig
}

func NewNotifyTask(op common.Operator, w *Workflow, uuid string) (t *NotifyTask, err error) {
	t = &NotifyTask{}
	t.NodeBase, err = workflowModel.InitNodeBase(op, uuid)
	t.Workflow = w
	return t, err
}

func (t *NotifyTask) Load() error {
	if err := t.NodeBase.Load(); err != nil {
		return err
	}
	//将表中的data还原到task中
	if err := common.JsonDecode([]byte(t.Table.Data), &t.Data); err != nil {
		return err
	}
	//将表中的detail还原到Detail中
	if err := common.JsonDecode([]byte(t.Table.Detail), &t.Detail); err != nil {
		return err
	}
	//将表中的front还原到form中
	if err := common.JsonDecode([]byte(t.Table.Front), &t.Form); err != nil {
		return err
	}
	t.SetInput(t.Data.Input, 0)
	t.SetOutput(t.Data.Output, 0)
	return nil
}

func (nt *NotifyTask) Begin() error {
	//先将任务begin
	if err := nt.node.Begin(); err != nil {
		return err
	}
	for _, handler := range notifyBeginHandlers {
		pass, err := handler(nt)
		if !pass { //如果没有pass，直接fail
			nt.Error = err
			return nt.Fail()
		}
	}
	return nt.Finish()
}

func (at *NotifyTask) Discard() error {
	for _, handler := range notifyDiscardHandlers {
		pass, err := handler(at)
		if !pass || err != nil { //如果没有pass或产生错误，都直接失败
			at.Error = err
		}
	}
	return at.node.Discard()
}

func (nt *NotifyTask) Persist() error {
	d, err := common.JsonEncode(nt.Data)
	if err != nil {
		return err
	}
	nt.Table.Data = string(d)
	return nt.NodeBase.Persist()
}

////////////////////handle func////////////////////

func HandleNotifyInput(at *NotifyTask) (pass bool, err error) {
	if !common.IsNil(at.Data.Input) {
		return true, nil
	}
	inputs := at.GetInput()
	if len(inputs) == 0 || common.IsNil(inputs[0]) {
		return false, common2.ErrNodeInvalidInput
	}
	at.Data.Input = inputs[0]
	return true, at.Persist()
}

func HandleNotifyInputToOutput(at *NotifyTask) (pass bool, err error) {
	if common.IsNil(at.Data.Input) {
		return false, common2.ErrNodeInvalidInput
	}
	at.Data.Output = at.Data.Input
	at.SetOutput(at.Data.Output, 0)
	return true, at.Persist()
}

// 读取需要抄送的人，创建抄送process，如果已创建则跳过
func HandleInitializedNotifyProcess(at *NotifyTask) (pass bool, err error) {
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}
	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		at.Uuid, common2.PROCESS_TYPE_NOTIFY)
	if err != nil {
		return false, err
	}
	var needChangeUuids []string
	for _, proc := range processes {
		needChangeUuids = append(needChangeUuids, proc.Uuid)
	}
	_, err = pModel.UpdateProcessRuntimeFromOldStatesByUuids(needChangeUuids,
		[]common2.ProcessStateEnum{common2.PROCESS_STATE_INITIALIZED}, common2.PROCESS_STATE_NOT_NOTIFIED)
	if err != nil {
		return false, err
	}
	return true, nil
}

// 读取需要抄送的人的process，判断是否未发通知，若未发则发送通知（钉钉，邮件或短信等，若已发送，则不重复发送），发送后修改process状态为已发送
func HandleNotifyPeopleToBeNotified(at *NotifyTask) (pass bool, err error) {
	notifyOperator := notifyLogic.NewNotifyOperator(at.GetParent().Ctx())
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}
	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		at.Uuid, common2.PROCESS_TYPE_NOTIFY)
	if err != nil {
		at.GetParent().Ctx().Log().Errorf("query process error [%v]", err)
		return false, nil
	}
	if len(processes) == 0 { //如果根本没process，表示无人抄送，直接通过
		return true, nil
	}
	for _, proc := range processes {
		data := processModel.NotifyProcessData{} //是否已发通知（jsonDecode出现错误默认为未发通知）
		_ = common.JsonStringDecode(proc.Data, &data)
		if data.Messaged { //已发通知，继续下一条
			continue
		}
		workflowName, initiatorName := GetWorkflowNameAndUserName(at.GetParent().Ctx(), at.Workflow.Table)
		emailContent, err := GenerateEmailContentByTmpl(workflowName, initiatorName, NOTIFY_EMAIL_TMPL, GetToCProcessUrl(proc.Uuid), nil)
		if err != nil {
			emailContent = GenerateEmailNotifyContent(proc.Uuid)
		}
		errNotify := notifyOperator.SendNotify(proc.UserId, notifyLogic.NotifyMsg{
			LarkContent: notifyLogic.GenerateLarkNotifyContent(workflowName, initiatorName, proc.Uuid, at.Workflow.Table.CreateTime, at.Detail.Notify.LarkCustomTemplate, at.Data),
			MailTitle:   "流程抄送通知",
			MailContent: emailContent,
		}, at.Detail.Notify.Mode)
		data.Messaged = true
		if errNotify != nil {
			at.GetParent().Ctx().Log().Errorf("send notify to user [%d] error [%v]", proc.UserId, err)
		} else if dataBytes, err := common.JsonEncode(data); err != nil {
			at.GetParent().Ctx().Log().Errorf("json encode data error [%v]", err)
		} else if _, err = pModel.UpdateProcessRuntimeDataByProcessUuid(proc.Uuid, string(dataBytes)); err != nil {
			at.GetParent().Ctx().Log().Errorf("update process runtime data error [%v]", err)
		}
	}
	return true, nil
}

// 被失效时令所有还没审批的process直接Expire
func HandleDiscardInitializedNotifyProcess(at *NotifyTask) (pass bool, err error) {
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return true, err
	}
	_, err = pModel.SetProcessRuntimeExpiredByNodeUuid(at.Table.Uuid)
	return true, err
}
