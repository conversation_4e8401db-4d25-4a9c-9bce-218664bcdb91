/**
 * @note
 * 网关节点定义
 *
 * <AUTHOR>
 * @date 	2019-11-08
 */
package engine

import (
	"encoding/json"
	"fmt"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	wCommon "gitlab.docsl.com/security/bpm/internal/logic/workflow/common"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"sort"
)

////////////////////init////////////////////

func init() {
	gatewayBeginHandlers = []GatewayHandlerFunc{
		HandleGatewayInput,
		HandleGatewayMerge,
		HandleGatewayOutput,
	}
	gatewayContinueHandlers = []GatewayHandlerFunc{
		HandleGatewayInput,
		HandleGatewayMerge,
		HandleGatewayOutput,
	}
}

////////////////////init////////////////////

type GatewayHandlerFunc func(*Gateway) (pass bool, err error)

var (
	gatewayBeginHandlers    []GatewayHandlerFunc
	gatewayContinueHandlers []GatewayHandlerFunc
)

type GatewayDetail struct {
	OutRule                  []OutputRule `json:"output,omitempty"`
	Merge                    MergeConfig  `json:"merge"`
	Spec                     GatewaySpec  `json:"spec"`
	TemplateUuidForFront     string       `json:"templateUuid"`     //给前端预留，用于匹配两个对称gateway的一个字段，后端完全不用
	PrevTemplateUuidForFront string       `json:"prevTemplateUuid"` //给前端预留，用于匹配两个对称gateway的一个字段，后端完全不用
}

type OutputRule struct {
	Default   bool            `json:"default"`
	Else      bool            `json:"else"` //判断如果其他条件都不满足，就走这个
	Condition string          `json:"condition"`
	Comment   string          `json:"comment"`
	Note      json.RawMessage `json:"note"`
	Name      json.RawMessage `json:"name"`
}

type GatewaySpec struct {
	Type         common2.GatewayTypeEnum `json:"type"`
	AtLeastValue int                     `json:"atLeastValue,omitempty"`
}

type GatewayData struct {
	Input  []interface{} `json:"input,omitempty"`
	Output []interface{} `json:"output,omitempty"`
	Result interface{}   `json:"result,omitempty"`
}

type Gateway struct {
	node
	Detail GatewayDetail
	Data   GatewayData
}

func NewGateway(op common.Operator, w *Workflow, uuid string) (g *Gateway, err error) {
	g = &Gateway{}
	g.NodeBase, err = workflowModel.InitNodeBase(op, uuid)
	g.Workflow = w
	return g, err
}

func (g *Gateway) Load() error {
	if err := g.node.Load(); err != nil {
		return err
	}
	g.Data = GatewayData{} //恢复Input和Output
	if err := common.JsonDecode([]byte(g.Table.Data), &g.Data); err != nil {
		return err
	} else if err = common.JsonDecode([]byte(g.Table.Detail), &g.Detail); err != nil { //恢复配置信息
		return err
	}
	for idx := range g.Data.Input {
		g.SetInput(g.Data.Input[idx], idx)
	}
	for idx := range g.Data.Output {
		g.SetOutput(g.Data.Output[idx], idx)
	}
	return nil
}

func (g *Gateway) Begin() error {
	//先将任务begin
	if err := g.node.Begin(); err != nil {
		return err
	}
	for _, handler := range gatewayBeginHandlers {
		pass, err := handler(g)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				g.Error = err
				return g.Fail()
			} else {
				return g.Pend()
			}
		}
	}
	return g.Finish()
}

func (g *Gateway) Continue() error {
	//先将任务Continue
	if err := g.node.Continue(); err != nil {
		return err
	}
	for _, handler := range gatewayContinueHandlers {
		pass, err := handler(g)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				g.Error = err
				return g.Fail()
			} else {
				return g.Pend()
			}
		}
	}
	return g.Finish()
}

func (g *Gateway) Persist() error {
	d, err := common.JsonEncode(g.Data)
	if err != nil {
		return err
	}
	g.Table.Data = string(d)
	return g.NodeBase.Persist()
}

func (g *Gateway) SetInputToData(input interface{}, index int) {
	if len(g.Data.Input) <= index {
		g.Data.Input = append(g.Data.Input, make([]interface{}, index-len(g.Data.Input)+1)...)
	}
	g.Data.Input[index] = input
}

func (g *Gateway) SetOutputToData(output interface{}, index int) {
	if len(g.Data.Output) <= index {
		g.Data.Output = append(g.Data.Output, make([]interface{}, index-len(g.Data.Output)+1)...)
	}
	g.Data.Output[index] = output
}

////////////////////handle func////////////////////

// 处理输入
func HandleGatewayInput(g *Gateway) (pass bool, err error) {
	prevNodes := g.GetPrev()
	prevNum := len(prevNodes)
	var failedOrDiscardNum, hasInputNum int
	for _, prevNode := range prevNodes { //统计前序节点的完成状态
		state := prevNode.State()
		if state == common2.STATE_DISCARD ||
			state == common2.STATE_FAILED {
			failedOrDiscardNum++
		}
	}
	for idx, input := range g.GetInput() { //统计本节点的输入
		if prevNodes[idx].State() == common2.STATE_FINISHED {
			if !common.IsNil(input) {
				g.SetInputToData(input, idx)
				hasInputNum++
			} else {
				failedOrDiscardNum++
			}
		}
	}
	g.GetParent().Ctx().Log().Debugln("failedOrDiscardNum:", failedOrDiscardNum, "hasInputNum:", hasInputNum)
	pass, err = ValuateInput(g.Detail.Spec, prevNum, failedOrDiscardNum, hasInputNum)
	if errPersist := g.Persist(); errPersist != nil { //此处持久化失败会有问题，直接失败
		return false, fmt.Errorf("gateway [%s] persist failed, err [%v]", g.Uuid, err)
	}
	return
}

// 处理合并
func HandleGatewayMerge(g *Gateway) (pass bool, err error) {
	//step 3. 处理合并
	mergeOperator := NewMergeOperator(g.Detail.Merge)
	dataHistoryMap := make(map[string]*wCommon.DataHistoryInfo)
	newDataHistory := make([]*wCommon.DataHistoryInfo, 0)
	for idx := range g.Input { //去重并合并
		dataHistory := wCommon.ExtractDataHistory(g.Input[idx])
		for _, h := range dataHistory {
			dataHistoryMap[fmt.Sprintf("%s_%d", h.ProcessUuid, h.OpTime)] = h
		}
		wCommon.DeleteDataHistory(g.Input[idx])
	}
	for _, h := range dataHistoryMap {
		newDataHistory = append(newDataHistory, h)
	}
	sort.Slice(newDataHistory, func(i, j int) bool {
		return newDataHistory[i].OpTime < newDataHistory[j].OpTime
	})
	g.Data.Result = mergeOperator.Merge(g.Input)
	wCommon.SetDataHistory(g.Data.Result, newDataHistory)
	return true, nil
}

// 处理输出
func HandleGatewayOutput(g *Gateway) (pass bool, err error) {
	nextNodes := g.GetNext()
	var outputCnt int
	setOutput := func(result interface{}, index int) {
		g.SetOutputToData(result, index)
		g.SetOutput(result, index)
	}
	//step 1. 先看输出条件是否满足
	for idx := range nextNodes {
		if len(g.Detail.OutRule) > idx && g.Detail.OutRule[idx].Else { //如果是一个'else'分支，即其他分支条件都不满足时走的分支，先set一个nil
			setOutput(nil, idx) //set一个nil作为标记，暂时不输出
		} else if len(g.Detail.OutRule) <= idx || //没有条件的情况下默认直接输出
			len(g.Detail.OutRule[idx].Condition) == 0 {
			outputCnt++
			setOutput(g.Data.Result, idx)
		} else if resultMap, ok := g.Data.Result.(map[string]interface{}); !ok { //进入valuate阶段
			g.GetParent().Ctx().Log().Errorf("Result [%v] is not a map, skip condition [%s]",
				g.Data.Result, g.Detail.OutRule[idx].Condition)
			setOutput(nil, idx) //set一个nil表示失败
		} else if expr, err := InitExpression(g.GetParent().Ctx(), g.Detail.OutRule[idx].Condition); err != nil {
			g.GetParent().Ctx().Log().Errorln(err)
			setOutput(nil, idx) //set一个nil表示失败
		} else if pass, reason := ValuateExpression(g.Detail.OutRule[idx].Condition, expr, resultMap); !pass {
			g.GetParent().Ctx().Log().Errorln(reason)
			setOutput(nil, idx) //set一个nil表示失败
		} else { //通过了
			outputCnt++
			setOutput(g.Data.Result, idx)
		}
	}
	//step 2. 如果一次输出下来，没有一个分支成功输出，则根据else开始输出
	if outputCnt == 0 {
		for idx := range nextNodes {
			if len(g.Detail.OutRule) > idx && g.Detail.OutRule[idx].Else {
				outputCnt++
				setOutput(g.Data.Result, idx)
			}
		}
	}
	//step 3. 如果还是没有一个分支成功输出，则根据default开始输出
	if outputCnt == 0 {
		for idx := range nextNodes {
			if len(g.Detail.OutRule) > idx && g.Detail.OutRule[idx].Default {
				outputCnt++
				setOutput(g.Data.Result, idx)
			}
		}
	}
	//step 4. 如果还是没能输出，则此节点失败，否则成功
	if outputCnt == 0 {
		return false, common2.ErrGatewayNoOutput
	} else if err := g.Persist(); err != nil { //最终的持久化还错就不能忍了
		g.GetParent().Ctx().Log().Errorf("gateway [%s] persist failed, err [%v]", g.Uuid, err)
		return false, err
	} else {
		return true, nil
	}
}
