package engine

import (
	"encoding/json"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	notifyLogic "gitlab.docsl.com/security/bpm/internal/logic/notify"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

////////////////////init////////////////////

func init() {
	transactorBeginHandlers = []TransactorHandlerFunc{
		HandleTransactorInput,
		HandleSortAndAssembleTransactorProcess,
		HandleTransactorProcessJudgement,
		HandleTransactorInputToOutput,
	}
	transactorContinueHandlers = []TransactorHandlerFunc{
		HandleTransactorInput,
		HandleSortAndAssembleTransactorProcess,
		HandleTransactorProcessJudgement,
		HandleTransactorInputToOutput,
	}
	transactorPendHandlers = []TransactorHandlerFunc{
		HandleInitializedTransactorProcess, //将initialized的process转化为unApproved
		HandleNotifyTransactor,             //在流程pend的时候给用户发送邮件、钉钉通知
	}
	transactorFinishHandlers = []TransactorHandlerFunc{}
	transactorFailHandlers = []TransactorHandlerFunc{}
	transactorDiscardHandlers = []TransactorHandlerFunc{}
}

////////////////////init////////////////////

type TransactorHandlerFunc func(*TransactorTask) (pass bool, err error)

var (
	transactorBeginHandlers    []TransactorHandlerFunc
	transactorContinueHandlers []TransactorHandlerFunc
	transactorPendHandlers     []TransactorHandlerFunc
	transactorFinishHandlers   []TransactorHandlerFunc
	transactorFailHandlers     []TransactorHandlerFunc
	transactorDiscardHandlers  []TransactorHandlerFunc
)

type TransactorTaskDetail struct {
	Name                json.RawMessage             `json:"name"` //给前端加的回显字段
	Staff               []*staffLogic.StaffInfo     `json:"staff"`
	EmptyStaff          []*staffLogic.StaffInfo     `json:"emptyStaff"`
	StaffFromForm       []*formLogic.StaffFromForm  `json:"staffFromForm"` //从表单中获取联系人
	IsEmpty             bool                        `json:"isEmpty"`       //是否因找不到审批人触发了转交机制
	SelfSelect          bool                        `json:"selfSelect"`
	AllowedAction       []common2.ProcessActionEnum `json:"allowedAction"`
	Notify              notifyLogic.NotifyConfig    `json:"notify"`
	Mode                common2.ApprovalModeEnum    `json:"mode"`
	EmptyMode           common2.ApprovalModeEnum    `json:"emptyMode"`
	Comment             bool                        `json:"comment"`
	CommentVisible      bool                        `json:"commentVisible"`
	OtherTransactorUser *staffLogic.UserInfo        `json:"otherTransactorUser"`
}

type TransactorTaskData struct {
	Input  interface{} `json:"input"`
	Output interface{} `json:"output"`
}

type TransactorTask struct {
	node
	Detail              TransactorTaskDetail
	Form                formLogic.FormConfig
	Data                TransactorTaskData
	ProcessInfos        []*SortedProcessInfo //全部的processInfo
	ProcessInfoMap      map[string]*SortedProcessInfo
	CurrentProcessInfos []*SortedProcessInfo //当前需要关注的processInfo
}

func NewTransactorTask(op common.Operator, w *Workflow, uuid string) (t *TransactorTask, err error) {
	t = &TransactorTask{}
	t.NodeBase, err = workflowModel.InitNodeBase(op, uuid)
	t.Workflow = w
	return t, err
}

func (t *TransactorTask) Load() error {
	if err := t.NodeBase.Load(); err != nil {
		return err
	}
	//将表中的data还原到task中
	if err := common.JsonDecode([]byte(t.Table.Data), &t.Data); err != nil {
		return err
	}
	//将表中的detail还原到Detail中
	if err := common.JsonDecode([]byte(t.Table.Detail), &t.Detail); err != nil {
		return err
	}
	//将表中的front还原到form中
	if err := common.JsonDecode([]byte(t.Table.Front), &t.Form); err != nil {
		return err
	}
	t.SetInput(t.Data.Input, 0)
	t.SetOutput(t.Data.Output, 0)
	return nil
}

func (at *TransactorTask) Begin() error {
	//先将任务begin
	if err := at.node.Begin(); err != nil {
		return err
	}
	for _, handler := range transactorBeginHandlers {
		pass, err := handler(at)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				at.Error = err
				return at.Fail()
			} else {
				return at.Pend()
			}
		}
	}
	return at.Finish()
}

func (at *TransactorTask) Continue() error {
	//先将任务Continue
	if err := at.node.Continue(); err != nil {
		return err
	}
	for _, handler := range transactorContinueHandlers {
		pass, err := handler(at)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				at.Error = err
				return at.Fail()
			} else {
				return at.Pend()
			}
		}
	}
	return at.Finish()
}

func (at *TransactorTask) Pend() error {
	//先执行Pend前的流程
	for _, handler := range transactorPendHandlers {
		_, err := handler(at)
		if err != nil { //根据是否产生错误决定是否失败
			at.Error = err
			return at.Fail()
		}
	}
	return at.node.Pend()
}

func (at *TransactorTask) Finish() error {
	//先执行Finish前的流程
	for _, handler := range transactorFinishHandlers {
		pass, err := handler(at)
		if !pass || err != nil { //如果没有pass或产生错误，都直接失败
			at.Error = err
			return at.Fail()
		}
	}
	return at.node.Finish()
}

func (at *TransactorTask) Fail() error {
	//先执行Fail前的流程
	for _, handler := range transactorFailHandlers {
		pass, err := handler(at)
		if !pass || err != nil {
			at.Error = err
		}
	}
	return at.node.Fail()
}

func (at *TransactorTask) Discard() error {
	//先执行Finish前的流程
	for _, handler := range transactorDiscardHandlers {
		pass, err := handler(at)
		if !pass || err != nil { //如果没有pass或产生错误，都直接失败
			at.Error = err
		}
	}
	return at.node.Discard()
}

func (at *TransactorTask) Persist() error {
	d, err := common.JsonEncode(at.Data)
	if err != nil {
		return err
	}
	at.Table.Data = string(d)
	return at.NodeBase.Persist()
}

////////////////////handle func////////////////////

// 处理Input
func HandleTransactorInput(at *TransactorTask) (pass bool, err error) {
	if !common.IsNil(at.Data.Input) {
		return true, nil
	}
	inputs := at.GetInput()
	if len(inputs) == 0 || common.IsNil(inputs[0]) {
		return false, common2.ErrNodeInvalidInput
	}
	at.Data.Input = inputs[0]
	return true, at.Persist()
}

// process整理
func HandleSortAndAssembleTransactorProcess(at *TransactorTask) (pass bool, err error) {

	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}

	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		at.Uuid, common2.PROCESS_TYPE_TRANSACTOR)
	if err != nil {
		return false, err
	}

	//过滤退回的process
	for idx, p := range processes {
		if p.State == common2.PROCESS_STATE_ROLLBACKED {
			processes = append(processes[:idx], processes[idx+1:]...)
			break
		}
	}

	at.ProcessInfos, at.ProcessInfoMap, err = AssembleProcessTablesToSortedProcessInfos(processes)
	if err != nil {
		return false, err
	}

	for _, procInfo := range at.ProcessInfos { //收集当前需要concern的process
		at.CurrentProcessInfos = append(at.CurrentProcessInfos, FindCurrentProcessInRoot(procInfo))
	}

	return true, nil
}

// 处理人去重
func HandleRemoveDuplicateTransactor(at *TransactorTask) (pass bool, err error) {

	if !at.Workflow.RemoveDuplicate {
		return true, nil
	}

	prevNodes := at.GetPrev()
	if len(prevNodes) == 0 || len(prevNodes) > 1 || prevNodes[0].Base().Table.Type != common2.NODE_TYPE_TRANSACTOR {
		//前序节点不只一个，或者不是处理节点也直接通过
		return true, nil
	}

	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}

	//查询前序节点所有处理通过的的用户
	prevApprovedProcs, err := pModel.QueryApprovalProcessRuntimeByNodeUuidAndState(prevNodes[0].GetUuid(), common2.PROCESS_STATE_DONE, common2.PROCESS_TYPE_TRANSACTOR)
	if err != nil { //查询错误，不影响主流程，所以这里直接通过吧
		at.GetParent().Ctx().Log().Errorln(err)
		return true, nil
	}

	prevApprovedUserIdMap := make(map[int64]bool)
	for _, p := range prevApprovedProcs {
		prevApprovedUserIdMap[p.UserId] = true
	}
	for _, curPInfo := range at.CurrentProcessInfos {
		if prevApprovedUserIdMap[curPInfo.Table.UserId] && curPInfo.isUnDone() { //如果需要自动通过
			curPInfo.Data.Comment = "办理人去重，自动通过"
			curPInfo.Table.State = common2.PROCESS_STATE_DONE
			dataStr, err := common.JsonStringEncode(curPInfo.Data)
			if err != nil {
				at.GetParent().Ctx().Log().Errorln(err)
				continue
			}

			if _, err = pModel.UpdateProcessRuntimeDataAndStateByProcessUuid(curPInfo.Table.Uuid, dataStr, common2.PROCESS_STATE_DONE); err != nil {
				at.GetParent().Ctx().Log().Errorln(err)
				continue
			}

			//更新审批时间
			if _, err = pModel.UpdateProcessRuntimeApprovalTime(curPInfo.Table.Uuid); err != nil {
				at.GetParent().Ctx().Log().Errorln(err)
				continue
			}

			if at.Detail.Mode == common2.APPROVAL_MODE_OR_SIGN {
				//否则需要看情况，看审批人的这次审批是否会直接影响审批结果
				var needDealWithOtherProcesses bool
				if curPInfo.SubProcess == nil { //无子process
					if curPInfo.ParentProcess == nil { //也没有父process，表示是个根process
						needDealWithOtherProcesses = true
					} else if curPInfo.Detail.SubType != common2.PROCESS_SUBTYPE_COUNTERSIGN_BEFORE { //不是父process加签出来的先审批process
						needDealWithOtherProcesses = true
					}
				} else if hasCountersignAfterSubProcess := func(sp *SortedProcessInfo) bool {
					for _, p := range sp.SubProcess {
						if p.Detail.SubType == common2.PROCESS_SUBTYPE_COUNTERSIGN_AFTER {
							return true
						}
					}
					return false
				}; !hasCountersignAfterSubProcess(curPInfo) { //子process中不存在一个在此后审批的加签process
					needDealWithOtherProcesses = true
				}
				if needDealWithOtherProcesses {
					var needUpdateProcessUuids []string
					for _, p := range at.ProcessInfos {
						if p.Table.Uuid != curPInfo.Table.Uuid && p.isUnDone() {
							needUpdateProcessUuids = append(needUpdateProcessUuids, p.Table.Uuid)
						}
					}
					//更新其他process
					_, err = pModel.UpdateProcessRuntimeFromOldStatesByUuids(needUpdateProcessUuids, []common2.ProcessStateEnum{
						common2.PROCESS_STATE_INITIALIZED, common2.PROCESS_STATE_UNDONE,
					}, common2.PROCESS_STATE_DONE_BY_OTHERS)
					if err != nil {
						at.GetParent().Ctx().Log().Errorln(err)
					} else {
						for _, needUpdateProcessUuid := range needUpdateProcessUuids {
							if p, ok := at.ProcessInfoMap[needUpdateProcessUuid]; ok {
								p.Table.State = common2.PROCESS_STATE_DONE_BY_OTHERS
							}
						}
					}
					break //直接break，不需要再去重了
				}
			}
		}
	}
	return true, nil
}

// 是否已处理
func HandleTransactorProcessJudgement(at *TransactorTask) (pass bool, err error) {

	if len(at.ProcessInfos) == 0 {
		return true, nil
	}

	switch at.Detail.Mode {
	case common2.APPROVAL_MODE_AND_SIGN:
		fallthrough
	case common2.APPROVAL_MODE_INTURN_SIGN: //依次审批也是另一种形式的会签
		for _, curProcInfo := range at.CurrentProcessInfos {
			if curProcInfo.isUnDone() { //有一个是未审批状态就继续等待
				return false, nil
			}
		}
		return true, nil //已被全部通过
	case common2.APPROVAL_MODE_OR_SIGN:
		for _, curProcInfo := range at.CurrentProcessInfos {
			if curProcInfo.isApproved() { //有一个被批准直接通过
				return true, nil
			}
		}
		return false, nil //继续等待
	default:
		return false, common2.ErrInvalidApprovalMode //配置有问题
	}
}

// 处理Output
func HandleTransactorInputToOutput(at *TransactorTask) (pass bool, err error) {
	if common.IsNil(at.Data.Input) {
		return false, common2.ErrNodeInvalidInput
	}
	at.Data.Output = at.Data.Input
	at.SetOutput(at.Data.Output, 0)
	return true, at.Persist()
}

// 流程状态转换
func HandleInitializedTransactorProcess(at *TransactorTask) (pass bool, err error) {

	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}

	var needChangeInfos []*SortedProcessInfo //含义是状态需要变更为unApproved的用户process
	for _, curProcess := range at.CurrentProcessInfos {
		if curProcess.Table.State == common2.PROCESS_STATE_INITIALIZED {
			needChangeInfos = append(needChangeInfos, curProcess)
			if at.Detail.Mode == common2.APPROVAL_MODE_INTURN_SIGN { //依次审批的情况下，一次只初始化一个process
				break
			}
		}
	}

	needChangeUuids := make([]string, len(needChangeInfos))
	for idx, info := range needChangeInfos {
		needChangeUuids[idx] = info.Table.Uuid
	}

	if _, err = pModel.UpdateProcessRuntimeFromOldStatesByUuids(
		needChangeUuids, []common2.ProcessStateEnum{common2.PROCESS_STATE_INITIALIZED}, common2.PROCESS_STATE_UNDONE); err != nil {
		return false, err
	}

	for _, info := range needChangeInfos { //修改成功，修改状态
		info.Table.State = common2.PROCESS_STATE_UNDONE
	}

	return true, nil
}

// 消息发送
func HandleNotifyTransactor(at *TransactorTask) (pass bool, err error) {

	notifyOperator := notifyLogic.NewNotifyOperator(at.GetParent().Ctx())
	pModel, err := processModel.NewProcessModel(at.GetParent(), false)
	if err != nil {
		return false, err
	}

	processes, err := pModel.QueryProcessRuntimeByNodeUuidAndType(
		at.Uuid, common2.PROCESS_TYPE_TRANSACTOR)
	if err != nil {
		at.GetParent().Ctx().Log().Errorf("query process error [%v]", err)
		return false, nil
	}

	if len(processes) == 0 {
		return true, nil
	}

	for _, proc := range processes {
		data := processModel.ApprovalProcessData{} //是否已发通知（jsonDecode出现错误默认为未发通知）
		_ = common.JsonStringDecode(proc.Data, &data)
		if proc.State != common2.PROCESS_STATE_UNDONE || data.Messaged { //已发通知，继续下一条
			continue
		}

		workflowName, initiatorName := GetWorkflowNameAndUserName(at.GetParent().Ctx(), at.Workflow.Table)
		emailContent, err := GenerateEmailContentByTmpl(workflowName, initiatorName, NEED_HANDLE_EMAIL_TMPL, GetToCProcessUrl(proc.Uuid), nil)
		if err != nil {
			emailContent = GenerateEmailHandleContent(proc.Uuid)
		}
		errNotify := notifyOperator.SendNotify(proc.UserId, notifyLogic.NotifyMsg{
			LarkContent: notifyLogic.GenerateLarkHandleContent(workflowName, initiatorName, proc.Uuid, at.Workflow.Table.CreateTime, at.Detail.Notify.LarkCustomTemplate, at.Data),
			MailTitle:   "流程办理通知",
			MailContent: emailContent,
		}, at.Detail.Notify.Mode)
		data.Messaged = true
		if errNotify != nil {
			at.GetParent().Ctx().Log().Errorf("send notify to user [%d] error [%v]", proc.UserId, err)
		} else if dataBytes, err := common.JsonEncode(data); err != nil {
			at.GetParent().Ctx().Log().Errorf("json encode data error [%v]", err)
		} else if _, err = pModel.UpdateProcessRuntimeDataByProcessUuid(proc.Uuid, string(dataBytes)); err != nil {
			at.GetParent().Ctx().Log().Errorf("update process runtime data error [%v]", err)
		}
	}
	return true, nil
}
