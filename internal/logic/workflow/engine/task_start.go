/**
 * @note
 * task_start
 *
 * <AUTHOR>
 * @date 	2019-12-09
 */
package engine

import (
	"encoding/json"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	"gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

func init() {
	startBeginHandlers = []StartHandlerFunc{
		HandleStartInputToOutput,
	}
}

////////////////////init////////////////////

type StartHandlerFunc func(t *StartTask) (pass bool, err error)

var (
	startBeginHandlers []StartHandlerFunc
)

type StartTaskDetail struct {
	Name json.RawMessage `json:"name"` //给前端加的回显字段
}

type StartTaskData struct {
	Input    interface{}     `json:"input"`
	Output   interface{}     `json:"output"`
	Complete []*CompleteInfo `json:"complete,omitempty"`
}

type CompleteInfo struct {
	Remark       string `json:"remark"`
	File         []*process.RemarkFileInfo
	VisibleStaff []*staffLogic.StaffInfo `json:"visibleStaff"`
	CreateTime   int64                   `json:"createTime"`
}

type StartTask struct {
	node
	Detail StartTaskDetail
	Form   formLogic.FormConfig
	Data   StartTaskData
}

func NewStartTask(op common.Operator, w *Workflow, uuid string) (t *StartTask, err error) {
	t = &StartTask{}
	t.NodeBase, err = workflowModel.InitNodeBase(op, uuid)
	t.Workflow = w
	return t, err
}

func (st *StartTask) Persist() error {
	d, err := common.JsonEncode(st.Data)
	if err != nil {
		return err
	}
	st.Table.Data = string(d)
	return st.NodeBase.Persist()
}

func (t *StartTask) Load() error {
	if err := t.NodeBase.Load(); err != nil {
		return err
	}
	//将表中的data还原到task中
	if err := common.JsonDecode([]byte(t.Table.Data), &t.Data); err != nil {
		return err
	}
	//将表中的detail还原到Detail中
	if err := common.JsonDecode([]byte(t.Table.Detail), &t.Detail); err != nil {
		return err
	}
	//将表中的front还原到form中
	if err := common.JsonDecode([]byte(t.Table.Front), &t.Form); err != nil {
		return err
	}
	t.SetInput(t.Data.Input, 0)
	t.SetOutput(t.Data.Output, 0)
	return nil
}

func (st *StartTask) Begin() error {
	//先将任务begin
	if err := st.node.Begin(); err != nil {
		return err
	}
	for _, handler := range startBeginHandlers {
		pass, err := handler(st)
		if !pass { //如果没有pass，则根据是否产生错误决定阻塞或者失败
			if err != nil {
				st.Error = err
				return st.Fail()
			} else {
				return st.Pend()
			}
		}
	}
	return st.Finish()
}

////////////////////handle func////////////////////

func HandleStartInputToOutput(st *StartTask) (pass bool, err error) {
	if common.IsNil(st.Data.Input) {
		return false, bpmCom.ErrNodeInvalidInput
	}
	st.Data.Output = st.Data.Input
	st.SetOutput(st.Data.Output, 0)
	return true, st.Persist()
}
