/**
 * @note
 * 将valuate单独抽出来复用
 *
 * <AUTHOR>
 * @date 	2020-02-11
 */
package engine

import (
	"encoding/json"
	"errors"
	"fmt"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	"strconv"
	"time"

	"github.com/knetic/govaluate"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

// fix cycle import
type RelateWorkflowFieldData = common2.RelateWorkflowFieldData

/**
 * @note
 * gateway在valuate时支持使用的自定义函数
 */
var valFuncs = map[string]govaluate.ExpressionFunction{
	"atoi": func(args ...interface{}) (interface{}, error) {
		if len(args) == 0 {
			return nil, common.ErrNoInput
		}
		var rets []interface{}
		for _, arg := range args {
			if str, ok := arg.(string); ok {
				if f, err := strconv.ParseFloat(str, 64); err == nil {
					rets = append(rets, f)
				} else {
					return nil, err
				}
			} else if s, ok := arg.(json.Number); ok {
				if f, err := strconv.ParseFloat(string(s), 64); err == nil {
					rets = append(rets, f)
				} else {
					return nil, err
				}
			} else if i, ok := arg.(int64); ok {
				rets = append(rets, float64(i))
			} else if i32, ok := args[0].(int); ok {
				rets = append(rets, float64(i32))
			} else if f, ok := args[0].(float64); ok {
				rets = append(rets, f)
			} else if f32, ok := args[0].(float32); ok {
				rets = append(rets, float64(f32))
			}
		}
		if len(rets) == 0 {
			return nil, errors.New("atoi invalid input")
		} else if len(rets) == 1 {
			return rets[0], nil
		}
		return rets, nil
	},
	"contains": func(args ...interface{}) (interface{}, error) { //支持字符串或数字
		if len(args) < 2 {
			return nil, errors.New("contains invalid input")
		}
		target := args[len(args)-1]
		for i := 0; i < len(args)-1; i++ {
			if jn, ok := args[i].(json.Number); ok {
				if f, err := jn.Float64(); err != nil {
					return false, err
				} else if f == target {
					return true, nil
				}
			} else if args[i] == target {
				return true, nil
			}
		}
		return false, nil
	},
	"time": func(args ...interface{}) (interface{}, error) {
		if len(args) == 0 {
			return nil, common.ErrNoInput
		}
		var rets []interface{}
		for _, arg := range args {
			if str, ok := arg.(string); ok {
				if t, ok := tryParseTime(str); ok {
					rets = append(rets, float64(t.Unix()))
				} else {
					return nil, common2.ErrInvalidTimeInput
				}
			} else {
				rets = append(rets, arg)
			}
		}
		if len(rets) == 0 {
			return nil, common2.ErrInvalidTimeInput
		} else if len(rets) == 1 {
			return rets[0], nil
		}
		return rets, nil
	},
}

func getDynamicValFuncs(ctx common.HContextIface) map[string]govaluate.ExpressionFunction {
	ret := make(map[string]govaluate.ExpressionFunction)
	//先赋值静态函数
	for key, staticFunc := range valFuncs {
		ret[key] = staticFunc
	}
	//再生成动态函数
	staffOperator := staffLogic.NewStaffOperator(ctx)
	allDeptIds, _, allUserGroupIds, err := staffOperator.GetUserAllDeptIdsAndDeptNamesAndUserGroupIds()
	if err != nil {
		ctx.Log().Errorln(err)
	}
	deptIdMap, userGroupIdMap := make(map[int64]bool), make(map[int64]bool)
	for _, deptId := range allDeptIds {
		deptIdMap[deptId] = true
	}
	for _, userGroupId := range allUserGroupIds {
		userGroupIdMap[userGroupId] = true
	}
	ret["initiator"] = func(args ...interface{}) (interface{}, error) {
		for _, arg := range args {
			if i, ok := arg.(int64); ok && i == ctx.User().UserId {
				return true, nil
			} else if f, ok := arg.(float64); ok && int64(f) == ctx.User().UserId {
				return true, nil
			}
		}
		return false, nil
	}
	ret["department"] = func(args ...interface{}) (interface{}, error) {
		for _, arg := range args {
			if i, ok := arg.(int64); ok && deptIdMap[i] {
				return true, nil
			} else if f, ok := arg.(float64); ok && deptIdMap[int64(f)] {
				return true, nil
			}
		}
		return false, nil
	}
	ret["role"] = func(args ...interface{}) (interface{}, error) {
		for _, arg := range args {
			if i, ok := arg.(int64); ok && userGroupIdMap[i] {
				return true, nil
			} else if f, ok := arg.(float64); ok && userGroupIdMap[int64(f)] {
				return true, nil
			}
		}
		return false, nil
	}
	return ret
}

func tryParseTime(candidate string) (time.Time, bool) {

	var ret time.Time
	var found bool

	timeFormats := []string{
		"2006-01-02",          // RFC 3339
		"2006-01-02 15:04",    // RFC 3339 with minutes
		"2006-01-02 15:04:05", // RFC 3339 with seconds
	}

	for _, format := range timeFormats {

		ret, found = tryParseExactTime(candidate, format)
		if found {
			return ret, true
		}
	}

	return time.Now(), false
}

func tryParseExactTime(candidate string, format string) (time.Time, bool) {

	var ret time.Time
	var err error

	ret, err = time.ParseInLocation(format, candidate, time.Local)
	if err != nil {
		return time.Now(), false
	}

	return ret, true
}

// 从表达式中提取出所有的可替换变量名称
func GetVariableNamesInExpression(expr *govaluate.EvaluableExpression) (names []string, err error) {
	tokens := expr.Tokens()
	for _, token := range tokens {
		if token.Kind == govaluate.VARIABLE {
			if vName, ok := token.Value.(string); !ok {
				return names, common2.ErrGetVariableNameFromExpr
			} else {
				names = append(names, vName)
			}
		}
	}
	return
}

func InitExpression(ctx common.HContextIface, condition string) (*govaluate.EvaluableExpression, error) {
	expr, err := govaluate.NewEvaluableExpressionWithFunctions(condition, getDynamicValFuncs(ctx))
	if err != nil { //初始化表达式错误
		err = fmt.Errorf("condition [%s] parse error [%v]", condition, err)
	}
	return expr, err
}

func ValuateExpression(condition string, expr *govaluate.EvaluableExpression, params map[string]interface{}) (pass bool, reason string) {
	newParams := make(map[string]interface{})
	for k, v := range params {
		newParams[k] = v
	}
	if resIface, err := expr.Evaluate(HandleJsonNumberInMap(newParams)); err != nil { //计算错误
		reason = fmt.Sprintf("Condition [%s], params [%v], Evaluate error [%v]",
			condition, params, err)
		return
	} else if res, ok := resIface.(bool); !ok { //不是布尔值
		reason = fmt.Sprintf("Condition [%s], params [%v], Evaluate error [%v]",
			condition, params, err)
		return
	} else if !res { //没通过
		reason = fmt.Sprintf("Condition [%s], params [%v], Evaluate not pass",
			condition, params)
		return
	}
	return true, common.StringEmpty
}

func ValuateInput(spec GatewaySpec, prevNum, failedOrDiscardNum, hasInputNum int) (pass bool, err error) {
	switch spec.Type {
	case common2.GATEWAY_TYPE_AND: //且网关
		if failedOrDiscardNum > 0 {
			return false, common2.ErrGatewayImpossibleToPass
		} else if hasInputNum < prevNum {
			return false, nil
		}
	case common2.GATEWAY_TYPE_OR: //或网关
		if failedOrDiscardNum == prevNum {
			return false, common2.ErrGatewayImpossibleToPass
		} else if hasInputNum == 0 {
			return false, nil
		}
	case common2.GATEWAY_TYPE_AT_LEAST: //至少网关
		if failedOrDiscardNum > prevNum-spec.AtLeastValue {
			return false, common2.ErrGatewayImpossibleToPass
		} else if hasInputNum < spec.AtLeastValue {
			return false, nil
		}
	default:
		return false, common2.ErrGatewayTypeInvalid
	}
	return true, nil
}

func HandleJsonNumberInMap(obj map[string]interface{}) map[string]interface{} {
	for k, v := range obj {
		if n, ok := v.(json.Number); ok {
			if i, err := n.Int64(); err == nil {
				obj[k] = i
			} else if f, err := n.Float64(); err == nil {
				obj[k] = f
			} else {
				obj[k] = n.String()
			}
		} else if m, ok := v.(map[string]interface{}); ok {
			var relateWorkflowFieldData RelateWorkflowFieldData
			if err := common.MapToStruct(m, &relateWorkflowFieldData); err == nil {
				obj[k] = relateWorkflowFieldData.FieldValue
			} else {
				obj[k] = HandleJsonNumberInMap(m)
			}
		}
	}
	return obj
}
