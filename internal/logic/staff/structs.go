/**
 * @note
 * staff员工信息相关结构体
 *
 * <AUTHOR>
 * @date 	2019-11-19
 */
package staff

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

const (
	UserStatusNormal    = 0
	UserStatusForbidden = 1
	UserStatusLeaved    = 2

	USER_TYPE_EMPLOYEE          = 1  //正式员工
	USER_TYPE_CONTRACT_EMPLOYEE = 2  //合同工
	USER_TYPE_CONTRACT          = 3  //合约
	USER_TYPE_OUTSOURCED        = 4  //外包
	USER_TYPE_CONSULTANT        = 5  //顾问
	USER_TYPE_INTERN            = 6  //实习生
	USER_TYPE_PART_TIME         = 7  //兼职
	USER_TYPE_LEAVED            = 8  //离职员工
	USER_TYPE_ASSIGNMENT        = 9  //外派
	USER_TYPE_TEST              = 10 //测试账号

	DEPT_STATUS_INIT     = 0
	DEPT_STATUS_NORMAL   = 1
	DEPT_STATUS_DISABLED = 2

	RELATION_SELF             = "Self" //自己
	RELATION_MANAGER          = "Manager"
	RELATION_MANAGER_FROM_TOP = "ManagerFromTop" //从上往下层级找领导
	//Deprecated
	RELATION_2ND_RANK_MANAGER = "2nd Rank Manager" //领导的领导
)

const (
	KEY_DEPARTMENT_USERS         = "bpm_dept_users_info_%d_recursive_%t"
	KEY_DEPARTMENT_INFO          = "bpm_dept_info_%d"
	KEY_ALL_DEPARTMENT           = "bpm_all_dept_info"
	KEY_USER_INFO                = "bpm_user_info_%d"
	KEY_USER_AVAILABLE_APPS_INFO = "bpm_user_available_apps_info_%d"
	KEY_USER_MANAGER_INFO        = "bpm_user_manager_info_%d"
	KEY_DEPT_MANAGER_INFO        = "bpm_dept_manager_info_%d"
	KEY_USER_ALL_DEPT_USERGROUP  = "bpm_user_all_dept_user_group_ids_%d"

	LarkUserTypeEmployee   = 1
	LarkUserTypeIntern     = 2
	LarkUserTypeOutsourced = 3
	LarkUserTypeContract   = 4
	LarkUserTypeConsultant = 5
)

var LarkUserTypeMap = map[int]int{
	LarkUserTypeEmployee:   USER_TYPE_EMPLOYEE,
	LarkUserTypeIntern:     USER_TYPE_INTERN,
	LarkUserTypeOutsourced: USER_TYPE_OUTSOURCED,
	LarkUserTypeContract:   USER_TYPE_CONTRACT,
	LarkUserTypeConsultant: USER_TYPE_CONSULTANT,
}

var (
	UserTypeIntStrMap = map[int]string{
		USER_TYPE_EMPLOYEE:          "正式员工",
		USER_TYPE_CONTRACT_EMPLOYEE: "合同工",
		USER_TYPE_CONTRACT:          "合约",
		USER_TYPE_OUTSOURCED:        "外包",
		USER_TYPE_CONSULTANT:        "顾问",
		USER_TYPE_INTERN:            "实习生",
		USER_TYPE_PART_TIME:         "兼职",
		USER_TYPE_LEAVED:            "离职员工",
		USER_TYPE_ASSIGNMENT:        "外派",
		USER_TYPE_TEST:              "测试账号",
	}
)

// fix cycle import
type StaffInfo = bpmCom.StaffInfo
type UserInfo = bpmCom.UserInfo
type ParentDeptInfo = bpmCom.ParentDeptInfo
type DepartmentInfo = bpmCom.DepartmentInfo

type AvailableApp struct {
	AppId   int64  `json:"appId"`
	AppIcon string `json:"appIcon"`
	AppName string `json:"appName"`
	AppUrl  string `json:"appUrl"`
	Select  int64  `json:"select"`
}
type StaffOperator struct {
	ctx common.HContextIface
}

func (op *StaffOperator) Ctx() common.HContextIface {
	return op.ctx
}

func NewStaffOperator(ctx common.HContextIface) *StaffOperator {
	return &StaffOperator{ctx: ctx}
}

func GetUserTypeAndStatus(larkUserType int, larkStatus bpmCom.UserStatusEnum) (userType int, userStatus int) {
	userType = LarkUserTypeMap[larkUserType]
	if userType == 0 {
		userType = USER_TYPE_EMPLOYEE
	}
	userStatus = UserStatusNormal
	if larkStatus == bpmCom.StatusFrozen || larkStatus == bpmCom.StatusUnJoin {
		userStatus = UserStatusForbidden
	}
	if larkStatus == bpmCom.StatusUnJoin || larkStatus == bpmCom.StatusResigned {
		userType = USER_TYPE_LEAVED
	}
	return
}
