/**
 * @note
 * 人员关系
 *
 * <AUTHOR>
 * @date 	2019-11-19
 */
package staff

import (
	"fmt"
	interCommon "gitlab.docsl.com/security/bpm/internal/common"
	ugLogic "gitlab.docsl.com/security/bpm/internal/logic/user_group"
	"gitlab.docsl.com/security/bpm/pkg/model/staff"
	"time"

	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/common/redis"
)

func (op *StaffOperator) GetStaffUserInfos(staffInfos []*StaffInfo) (
	userInfos []*UserInfo, retErr error) {
	for _, staffInfo := range staffInfos {
		switch staffInfo.Type {
		case interCommon.STAFF_TYPE_PERSON:
			if userInfo, err := op.GetUserInfoByUserId(staffInfo.UserId); err != nil && err != common.ErrRecordNotFound {
				retErr = err
			} else if userInfo != nil {
				userInfos = append(userInfos, userInfo)
			}
		case interCommon.STAFF_TYPE_DEPARTMENT:
			if deptUserInfos, err := op.GetUserInfosByDeptId(staffInfo.DepartmentId, true); err != nil {
				retErr = err
			} else {
				userInfos = append(userInfos, deptUserInfos...)
			}
		case interCommon.STAFF_TYPE_RELATION:
			switch staffInfo.Relation {
			case RELATION_SELF:
				if userInfo, err := op.GetUserInfoByUserId(op.ctx.User().UserId); err != nil && err != common.ErrRecordNotFound {
					retErr = err
					continue
				} else if userInfo != nil {
					userInfos = append(userInfos, userInfo)
				}
			case RELATION_2ND_RANK_MANAGER:
				staffInfo.Level = 2
				staffInfo.Relation = RELATION_MANAGER
				fallthrough
			case RELATION_MANAGER:
				fallthrough
			case RELATION_MANAGER_FROM_TOP:
				if staffInfo.Level <= 0 {
					staffInfo.Level = 1
				}
				var departmentId int64
				if staffInfo.DepartmentId != 0 { //查的是部门的主管
					departmentId = staffInfo.DepartmentId
				} else { //查的是人的主管	 2020-6-16 改成查人所在部门的主管
					userInfo, err := op.GetUserInfoByUserId(op.ctx.User().UserId)
					if err != nil && err != common.ErrRecordNotFound {
						retErr = err
						continue
					} else if userInfo != nil {
						departmentId = userInfo.DepartmentId
					}
				}
				//2020-07-17 如果自己就是自己所在部门的主管，则需要往上推一级
				if deptInfo, err := op.GetDeptInfoByDeptId(departmentId); err != nil {
					retErr = err
					continue
				} else if deptInfo.ManagerId == op.ctx.User().UserId { //自己就是自己所在部门的主管
					departmentId = deptInfo.SuperId
				}
				var infos []*UserInfo
				var err error
				if staffInfo.Relation == RELATION_MANAGER {
					infos, err = op.GetDepartmentManagerInfoByLevel(departmentId, staffInfo.Level, staffInfo.All)
				} else { //否则是从上往下找的情况
					infos, err = op.GetDepartmentManagerInfoByLevelFromTop(departmentId, staffInfo.Level, staffInfo.All)
				}
				if err != nil {
					retErr = err
				} else if len(infos) == 0 {
					err = common.ErrRecordNotFound
				}
				userInfos = append(userInfos, infos...)
			}
		case interCommon.STAFF_TYPE_GROUP:
			userGroupOperator := ugLogic.NewUserGroupOperator(op.ctx)
			userGroups, err := userGroupOperator.QueryUserGroupByGroupId(staffInfo.UserGroupId)
			if err != nil {
				return userInfos, err
			}
			for _, userGroup := range userGroups {
				if op.JudgeUserGroupDetailCondition(userGroup.Detail) {
					userId := userGroup.UserId
					if userInfo, err := op.GetUserInfoByUserId(userId); err != nil &&
						err != common.ErrRecordNotFound {
						retErr = err
						continue
					} else if userInfo != nil {
						userInfos = append(userInfos, userInfo)
					}
				}
			}
		}
	}
	return userInfos, retErr
}

func (op *StaffOperator) JudgeUserGroupDetailCondition(detail string) bool {
	if len(detail) == 0 { //代表所有人
		return true
	}
	var d ugLogic.UserGroupRelationDetail
	err := common.JsonStringDecode(detail, &d)
	if err != nil {
		op.Ctx().Log().Errorln("user group relation detail", detail, "invalid, error:", err)
		return false
	}
	if len(d.Staff) == 0 { //代表所有人
		return true
	}
	allDeptIds, _, _, err := op.GetUserAllDeptIdsAndDeptNamesAndUserGroupIds()
	if op.JudgeIfStaffInfosContainsAnyUserIdsOrDeptIdsOrUserGroupIds(
		d.Staff, []int64{op.Ctx().User().UserId}, allDeptIds, nil) {
		return true
	}

	return false
}

func (op *StaffOperator) GetDepartmentManagerInfoByLevel(deptId int64, level int, all bool) (userInfos []*UserInfo, err error) {
	for curDeptId, currentLevel := deptId, level; currentLevel > 0; currentLevel-- {
		if curDeptId == 0 {
			op.Ctx().Log().Infof("not enough level, demand level %d, current level %d", level, currentLevel)
			break
		} else if deptInfo, e := op.GetDeptInfoByDeptId(curDeptId); e != nil {
			if e != common.ErrRecordNotFound {
				err = e
			}
			break
		} else {
			curDeptId = deptInfo.SuperId
			if all || currentLevel == 1 { //如果是全选或level只剩下1了
				if deptInfo.ManagerId == 0 {
					err = interCommon.ErrDeptManagerNotExist
					if !all {
						currentLevel++ //2020-07-17 非逐级审批的情况下，找不到就往上再推一个
					}
				} else if userInfo, e := op.GetUserInfoByUserId(deptInfo.ManagerId); e != nil {
					err = e
					if !all {
						currentLevel++ //2020-07-21 非逐级审批的情况下，找不到就往上再推一个
					}
				} else {
					userInfos = append(userInfos, userInfo)
				}
			}
		}
	}
	if len(userInfos) == 0 {
		if err == nil {
			err = common.ErrRecordNotFound
		}
	}
	return userInfos, err
}

func (op *StaffOperator) GetDepartmentManagerInfoByLevelFromTop(deptId int64, level int, all bool) (userInfos []*UserInfo, err error) {
	var allUserInfos []*UserInfo
	for curDeptId := deptId; curDeptId != 0; { //先一直找到所有人
		if deptInfo, e := op.GetDeptInfoByDeptId(curDeptId); e != nil {
			if e != common.ErrRecordNotFound {
				err = e
			}
			break
		} else {
			curDeptId = deptInfo.SuperId
			if deptInfo.ManagerId == 0 {
				err = interCommon.ErrDeptManagerNotExist
				allUserInfos = append(allUserInfos, nil)
			} else if userInfo, e := op.GetUserInfoByUserId(deptInfo.ManagerId); e != nil {
				err = e
				allUserInfos = append(allUserInfos, nil)
			} else {
				allUserInfos = append(allUserInfos, userInfo)
			}
		}
	}
	if len(allUserInfos) == 0 {
		if err == nil {
			err = common.ErrRecordNotFound
		}
		return
	}
	if !all { //只找一个人的情况
		if len(allUserInfos) < level {
			return nil, common.ErrRecordNotFound
		} else if info := allUserInfos[len(allUserInfos)-level]; info == nil {
			return nil, common.ErrRecordNotFound
		}
		userInfos = append(userInfos, allUserInfos[len(allUserInfos)-level])
		return
	}
	//否则从上往下找
	count := len(allUserInfos) - level + 1
	if count <= 0 { //如果层级不够，则至少有一个主管来审批
		count = 1
	}
	for _, info := range allUserInfos { //找够数量位置
		if count <= 0 {
			break
		}
		if info != nil {
			userInfos = append(userInfos, info)
		}
		count--
	}
	return userInfos, err
}

func (op *StaffOperator) GetUserManagerInfoByLevel(userId int64, level int, all bool) (userInfos []*UserInfo, err error) {
	currentUserId := op.ctx.User().UserId //如果传了userId，就是查某人的老板，否则查自己的老板
	if userId != 0 {
		currentUserId = userId
	}
	for currentLevel := level; currentLevel > 0; currentLevel-- {
		if userInfo, e := op.GetManagerInfoByUserId(currentUserId); e != nil {
			if e != common.ErrRecordNotFound {
				err = e
			}
			break
		} else {
			currentUserId = userInfo.UserId
			if all || currentLevel == 1 { //如果是全选或level只剩下1了
				userInfos = append(userInfos, userInfo)
			}
		}
	}
	if len(userInfos) == 0 {
		if err == nil {
			err = common.ErrRecordNotFound
		}
	}
	return userInfos, err
}

func (op *StaffOperator) ValidateStaffInfos(staffInfos []*StaffInfo) (err error) {
	for _, staffInfo := range staffInfos {
		switch staffInfo.Type {
		case interCommon.STAFF_TYPE_PERSON:
			if _, err = op.GetUserInfoByUserId(staffInfo.UserId); err != nil && err != common.ErrRecordNotFound {
				return err
			}
		case interCommon.STAFF_TYPE_DEPARTMENT:
			if _, err = op.GetUserInfosByDeptId(staffInfo.DepartmentId, true); err != nil {
				return err
			}
		case interCommon.STAFF_TYPE_GROUP:
			userGroupOperator := ugLogic.NewUserGroupOperator(op.ctx)
			_, err := userGroupOperator.QueryUserGroupInfoById(staffInfo.UserGroupId)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (op *StaffOperator) GetUserInfoByUserId(userId int64) (
	userInfo *UserInfo, err error) {
	res, err := staff.GetUserByUserID(op.ctx.GinCtx(), userId)
	if err != nil {
		return nil, err
	} else if res == nil || res.ID == 0 {
		return nil, common.ErrRecordNotFound
	}
	t, s := GetUserTypeAndStatus(res.EmployeeType, res.Status)
	userInfo = &UserInfo{
		UserId:      res.ID,
		Account:     res.UserName,
		NameZh:      res.LarkName,
		NameEn:      res.LarkEnName,
		NameDisplay: res.LarkName,
		Email:       res.Email,
		Phone:       res.Mobile,
		EmployeeId:  res.EmployeeNo,
		Type:        t,
		Status:      s,
	}
	primaryDept, err := staff.GetPrimaryDepartmentByLarkUserID(op.ctx.GinCtx(), res.LarkUserID)
	if err != nil {
		return nil, err
	}
	userInfo.DepartmentName = primaryDept.Name
	userInfo.DepartmentId = primaryDept.ID
	return userInfo, nil
}

func (op *StaffOperator) GetUserInfoByUserIdInAllStatus(userId int64) (
	userInfo *UserInfo, err error) {
	res, err := staff.GetUserByUserIDInAllStatus(op.ctx.GinCtx(), userId)
	if err != nil {
		return nil, err
	} else if res == nil || res.ID == 0 {
		return nil, common.ErrRecordNotFound
	}
	t, s := GetUserTypeAndStatus(res.EmployeeType, res.Status)
	userInfo = &UserInfo{
		UserId:      res.ID,
		Account:     res.UserName,
		NameZh:      res.LarkName,
		NameEn:      res.LarkEnName,
		NameDisplay: res.LarkName,
		Email:       res.Email,
		Phone:       res.Mobile,
		EmployeeId:  res.EmployeeNo,
		Type:        t,
		Status:      s,
	}
	primaryDept, err := staff.GetPrimaryDepartmentByLarkUserID(op.ctx.GinCtx(), res.LarkUserID)
	if err != nil {
		return nil, err
	}
	userInfo.DepartmentName = primaryDept.Name
	userInfo.DepartmentId = primaryDept.ID
	if userInfo.Status == UserStatusLeaved {
		userInfo.NameZh = userInfo.NameZh + "(离职)"
		userInfo.NameDisplay = userInfo.NameDisplay + "(离职)"
	}
	return userInfo, nil
}

func (op *StaffOperator) GetDeptManagerInfoByDeptId(deptId int64) (
	userInfo *UserInfo, err error) {
	deptTable, err := staff.GetDepartmentByID(op.ctx.GinCtx(), deptId)
	if err != nil {
		return nil, err
	} else if deptTable == nil || deptTable.ID == 0 || deptTable.LarkLeaderUserID == common.StringEmpty {
		return nil, common.ErrRecordNotFound
	}
	res, err := staff.GetUserByLarkUserID(op.ctx.GinCtx(), deptTable.LarkLeaderUserID)
	if err != nil {
		return nil, err
	}
	t, s := GetUserTypeAndStatus(res.EmployeeType, res.Status)
	userInfo = &UserInfo{
		UserId:         res.ID,
		Account:        res.UserName,
		NameZh:         res.LarkName,
		NameEn:         res.LarkEnName,
		NameDisplay:    res.LarkName,
		Email:          res.Email,
		Phone:          res.Mobile,
		EmployeeId:     res.EmployeeNo,
		DepartmentName: deptTable.Name,
		DepartmentId:   deptTable.ID,
		Type:           t,
		Status:         s,
	}
	return userInfo, nil
}

func (op *StaffOperator) GetManagerInfoByUserId(userId int64) (
	userInfo *UserInfo, err error) {
	userTable, err := staff.GetUserByUserID(op.ctx.GinCtx(), userId)
	if err != nil {
		return nil, err
	}
	if userTable.LarkLeaderUserID == common.StringEmpty {
		return nil, common.ErrRecordNotFound
	}
	res, err := staff.GetUserByLarkUserID(op.ctx.GinCtx(), userTable.LarkLeaderUserID)
	if err != nil {
		return nil, err
	}
	t, s := GetUserTypeAndStatus(res.EmployeeType, res.Status)
	userInfo = &UserInfo{
		UserId:      res.ID,
		Account:     res.UserName,
		NameZh:      res.LarkName,
		NameEn:      res.LarkEnName,
		NameDisplay: res.LarkName,
		Email:       res.Email,
		Phone:       res.Mobile,
		EmployeeId:  res.EmployeeNo,
		Type:        t,
		Status:      s,
	}
	primaryDept, err := staff.GetPrimaryDepartmentByLarkUserID(op.ctx.GinCtx(), res.LarkUserID)
	if err != nil {
		return nil, err
	}
	userInfo.DepartmentName = primaryDept.Name
	userInfo.DepartmentId = primaryDept.ID
	return userInfo, nil
}

func (op *StaffOperator) GetUserInfosByDeptId(deptId int64, recursive bool) (
	userInfos []*UserInfo, err error) {
	// 先查部门，转化成larkDeptID
	deptTable, err := staff.GetDepartmentByID(op.ctx.GinCtx(), deptId)
	if err != nil {
		return nil, err
	}
	return op.GetUserInfosByDeptTable(deptTable, recursive)
}

func (op *StaffOperator) GetUserInfosByDeptTable(deptTable *staff.DepartmentTable, recursive bool) (
	userInfos []*UserInfo, err error) {
	// 查人
	userTables, err := staff.GetUsersInDepartmentByLarkDepartmentID(op.ctx.GinCtx(), deptTable.LarkDepartmentID)
	if err != nil {
		return nil, err
	}
	for _, tb := range userTables {
		if tb == nil || tb.ID == 0 {
			continue
		}
		t, s := GetUserTypeAndStatus(tb.EmployeeType, tb.Status)
		userInfos = append(userInfos, &UserInfo{
			UserId:         tb.ID,
			Account:        tb.UserName,
			NameZh:         tb.LarkName,
			NameEn:         tb.LarkEnName,
			NameDisplay:    tb.LarkName,
			Email:          tb.Email,
			Phone:          tb.Mobile,
			EmployeeId:     tb.EmployeeNo,
			DepartmentName: deptTable.Name,
			DepartmentId:   deptTable.ID,
			Type:           t,
			Status:         s,
		})
	}
	if !recursive {
		return userInfos, nil
	} else { // 递归查询
		subDepts, err := staff.GetDepartmentsByLarkParentDepartmentID(op.ctx.GinCtx(), deptTable.LarkParentDepartmentID)
		if err != nil {
			return nil, err
		}
		for _, subDept := range subDepts {
			subUserInfos, err := op.GetUserInfosByDeptTable(subDept, recursive)
			if err != nil {
				return nil, err
			}
			userInfos = append(userInfos, subUserInfos...)
		}
	}
	return userInfos, nil
}

func (op *StaffOperator) GetUserIdsByUserNameZhVague(nameZh string) (
	userIds []int64, err error) {
	if len(nameZh) == 0 {
		return nil, nil
	}
	tbs, err := staff.GetUsersByLarkNameVague(op.ctx.GinCtx(), nameZh)
	if err != nil {
		return nil, err
	}
	for _, tb := range tbs {
		userIds = append(userIds, tb.ID)
	}
	return userIds, nil
}

func (op *StaffOperator) GetUserByUserNameZhVague(nameZh string) (
	userInfos []UserInfo, err error) {
	if len(nameZh) == 0 {
		return nil, nil
	}
	tbs, err := staff.GetUsersByLarkNameVague(op.ctx.GinCtx(), nameZh)
	if err != nil {
		return nil, err
	}
	for _, tb := range tbs {
		t, s := GetUserTypeAndStatus(tb.EmployeeType, tb.Status)
		primaryDept, err := staff.GetPrimaryDepartmentByLarkUserID(op.ctx.GinCtx(), tb.LarkUserID)
		if err != nil {
			return nil, err
		}
		userInfos = append(userInfos, UserInfo{
			UserId:         tb.ID,
			Account:        tb.UserName,
			NameZh:         tb.LarkName,
			NameEn:         tb.LarkEnName,
			NameDisplay:    tb.LarkName,
			Email:          tb.Email,
			Phone:          tb.Mobile,
			EmployeeId:     tb.EmployeeNo,
			DepartmentName: primaryDept.Name,
			DepartmentId:   primaryDept.ID,
			Type:           t,
			Status:         s,
		})
	}
	return userInfos, nil
}

func (op *StaffOperator) GetAllDeptIdsAndNamesByUserId(userId int64) (allDeptIds []int64, allDeptNames []string, err error) {
	if userInfo, err := op.GetUserInfoByUserId(userId); err != nil {
		return nil, nil, err
	} else {
		currentDeptId := userInfo.DepartmentId //查出用户所有的父级部门id
		allDeptIds = append(allDeptIds, userInfo.DepartmentId)
		allDeptNames = append(allDeptNames, userInfo.DepartmentName)
		for {
			if deptInfo, err := op.GetDeptInfoByDeptId(currentDeptId); err == nil {
				if deptInfo.SuperId > 0 {
					allDeptIds = append(allDeptIds, deptInfo.SuperId)
					allDeptNames = append(allDeptNames, deptInfo.SuperName)
					currentDeptId = deptInfo.SuperId
				} else {
					return allDeptIds, allDeptNames, nil //忽略错误
				}
			} else {
				return allDeptIds, allDeptNames, nil //忽略错误
			}
		}
	}
}

func (op *StaffOperator) GetParentDeptInfoByUserId(userId int64) (p *ParentDeptInfo, err error) {
	allDeptIds, allDeptNames, err := op.GetAllDeptIdsAndNamesByUserId(userId)
	if err != nil {
		return nil, err
	}
	var root, d *ParentDeptInfo
	for i := 1; i < len(allDeptIds); i++ {
		if d == nil {
			root = &ParentDeptInfo{
				DepartmentId: allDeptIds[i],
				Name:         allDeptNames[i],
			}
			d = root
		} else {
			d.ParentDept = &ParentDeptInfo{
				DepartmentId: allDeptIds[i],
				Name:         allDeptNames[i],
			}
			d = d.ParentDept
		}
	}
	return root, nil
}

func (op *StaffOperator) GetAllUserGroupIdsByUserId(userId int64) (allUserGroupIds []int64, err error) {
	userGroupOperator := ugLogic.NewUserGroupOperator(op.ctx)
	return userGroupOperator.QueryUserGroupIdsByUserId(userId)
}

func (op *StaffOperator) GetUserAllDeptIdsAndDeptNamesAndUserGroupIds() (allDeptIds []int64,
	allDeptNames []string, allUserGroupIds []int64, err error) {
	if op.ctx.User().DeptAndUserGroupIds != nil { //如果ctx.user里面有，就直接从ctx.user里面拿
		return op.ctx.User().DeptAndUserGroupIds.AllDeptIds,
			op.ctx.User().DeptAndUserGroupIds.AllDeptNames,
			op.ctx.User().DeptAndUserGroupIds.AllUserGroupIds, nil
	}
	if op.ctx.User().UserId == 0 {
		op.ctx.Log().Warningln("userId is 0 in user context")
		return nil, nil, nil, nil
	} else if allDeptIds, allDeptNames, allUserGroupIds, err :=
		op.getAllDeptIdsAndDeptNamesAndUserGroupIdsByUserId(op.ctx.User().UserId); err != nil {
		return nil, nil, nil, err
	} else {
		op.Ctx().User().DeptAndUserGroupIds = &common.AllDeptIdsAndUserGroupIds{
			AllDeptIds: allDeptIds, AllDeptNames: allDeptNames, AllUserGroupIds: allUserGroupIds,
		}
		return allDeptIds, allDeptNames, allUserGroupIds, nil
	}
}

func (op *StaffOperator) getAllDeptIdsAndDeptNamesAndUserGroupIdsByUserId(userId int64) (allDeptIds []int64,
	allDeptNames []string, allUserGroupIds []int64, err error) {
	var cache common.AllDeptIdsAndUserGroupIds
	//缓存
	redisCli, errCli := redis.DefaultClient(op.ctx.GinCtx())
	if errCli != nil {
		op.ctx.Log().Warningln("Get redis client err:", err)
	} else if retCmd, err := redisCli.Get(fmt.Sprintf(KEY_USER_ALL_DEPT_USERGROUP, userId)); err != nil {
		op.ctx.Log().Warningln("Get user all deptIds and userGroupIds from redis err:", err)
	} else if ret := retCmd.Val(); len(ret) > 0 && common.JsonStringDecode(ret, &cache) == nil {
		return cache.AllDeptIds, cache.AllDeptNames, cache.AllUserGroupIds, nil
	}
	//未命中缓存，请求sso
	if allDeptIds, allDeptNames, err = op.GetAllDeptIdsAndNamesByUserId(userId); err != nil {
		return nil, nil, nil, err
	} else if allUserGroupIds, err = op.GetAllUserGroupIdsByUserId(userId); err != nil {
		return nil, nil, nil, err
	} else {
		cache.AllDeptIds = allDeptIds
		cache.AllDeptNames = allDeptNames
		cache.AllUserGroupIds = allUserGroupIds
		data, err := common.JsonStringEncode(cache)
		if err == nil && errCli == nil { //set cache
			if err = redisCli.Set(fmt.Sprintf(KEY_USER_ALL_DEPT_USERGROUP, userId), data, time.Duration(common.GetConfig().StaffCacheTime)*time.Second); err != nil {
				op.ctx.Log().Warningln("Set user all deptIds and userGroupIds to redis err:", err)
			}
		}
		return allDeptIds, allDeptNames, allUserGroupIds, nil
	}
}

/* @note
 * 判断staffInfo数组中，是否存在一项匹配了传入的用户Ids和部门Ids和角色Ids
 */
func (op *StaffOperator) JudgeIfStaffInfosContainsAnyUserIdsOrDeptIdsOrUserGroupIds(
	staffInfos []StaffInfo, userIds, deptIds, userGroupIds []int64) bool {
	allUserIds := make(map[int64]bool)
	allDeptIds := make(map[int64]bool)
	allUserGroupIds := make(map[int64]bool)
	for _, userId := range userIds {
		allUserIds[userId] = true
	}
	for _, deptId := range deptIds {
		allDeptIds[deptId] = true
	}
	for _, userGroupId := range userGroupIds {
		allUserGroupIds[userGroupId] = true
	}
	for _, staffInfo := range staffInfos {
		switch staffInfo.Type {
		case interCommon.STAFF_TYPE_ALL:
			return true
		case interCommon.STAFF_TYPE_PERSON:
			if allUserIds[staffInfo.UserId] {
				return true
			}
		case interCommon.STAFF_TYPE_DEPARTMENT:
			if allDeptIds[staffInfo.DepartmentId] {
				return true
			}
		case interCommon.STAFF_TYPE_GROUP:
			if allUserGroupIds[staffInfo.UserGroupId] {
				return true
			}
		}
	}
	return false
}

func (op *StaffOperator) GetUserAvailableAppList() (
	availableApps []*AvailableApp, err error) {
	/*
		userId := op.Ctx().User().UserId
		//缓存
		redisCli, errCli := redis.DefaultClient(op.ctx.Log())
		if errCli != nil {
			op.ctx.Log().Warningln("Get redis client err:", err)
		} else if ret, err := redisCli.GET(fmt.Sprintf(KEY_USER_AVAILABLE_APPS_INFO, userId)); err != nil {
			op.ctx.Log().Warningln("Get user available apps info from redis err:", err)
		} else if len(ret) > 0 && common.JsonDecode(ret, &availableApps) == nil {
			return availableApps, nil
		}
		//未命中缓存，请求sso
		openClient, err := cas.NewCASOpenClient(op.ctx.Log())
		if err != nil {
			return nil, err
		}
		resSlice, err := openClient.UserAvailableAppList(userId)
		if err != nil {
			return nil, err
		}
		for _, res := range resSlice {
			availableApps = append(availableApps, &AvailableApp{
				AppId:   res.AppId,
				AppIcon: res.AppIcon,
				AppName: res.AppName,
				AppUrl:  res.AppUrl,
				Select:  res.Select,
			})
		}
		data, err := common.JsonEncode(availableApps)
		if err == nil && errCli == nil {
			if err = redisCli.SETEX(fmt.Sprintf(KEY_USER_AVAILABLE_APPS_INFO, userId), common.GetConfig().StaffCacheTime, data); err != nil {
				op.ctx.Log().Warningln("Set user available apps info to redis err:", err)
			}
		}
	*/
	availableApps = make([]*AvailableApp, 0) // mock一个
	return availableApps, nil
}

func FillCtxUserByUserId(ctx common.HContextIface, userId int64) error {
	staffOperator := NewStaffOperator(ctx)
	userInfo, err := staffOperator.GetUserInfoByUserId(userId)
	if err != nil {
		return err
	}
	ctx.User().UserId = userInfo.UserId
	ctx.User().AccountName = userInfo.Account
	ctx.User().DisplayName = userInfo.NameDisplay
	ctx.User().Email = userInfo.Email
	ctx.User().EmployeeId = userInfo.EmployeeId
	return nil
}
