/**
 * @note
 * lark_test
 *
 * <AUTHOR>
 * @date 	2025-07-24
 */
package staff

import (
	"context"
	"fmt"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/tmsong/hlog"
	conf "gitlab.docsl.com/security/bpm/pkg/config"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
	"os"
	"testing"
)

var (
	log *hlog.Logger
	ctx context.Context
)

func init() {
	masker.SetGetKeyFunc(func(arg string) []byte {
		return []byte(os.Getenv("BPM_CONFIG_CRYPT_KEY"))
	})
	log = hlog.NewLoggerWithConfig(logger.GetHlogConfig(), 0)
	ctx = context.WithValue(context.Background(), common.KeyLogger, log)
	// 尝试加载配置文件，先尝试相对路径，如果失败则尝试绝对路径
	_, err := conf.ReplaceAndLoad("../../../conf/dev.toml", true)
	if err != nil {
		panic(err)
	}
}

func Test_GetDepartmentChildren(t *testing.T) {
	PatchConvey("Test_GetDepartmentChildren", t, func() {
		PatchConvey("case1 GetDepartmentChildren", func() {
			cli := lark.GetLarkClient()
			departments, err := lark.GetDepartmentChildren(ctx, cli, "0", true, 50)
			So(err, ShouldBeNil)
			str, _ := common.JsonStringEncode(departments)
			fmt.Println(departments)
			So(str, ShouldNotBeEmpty)
		})
	})
}

func Test_GetDepartmentUser(t *testing.T) {
	PatchConvey("Test_GetDepartmentUser", t, func() {
		PatchConvey("case1 GetDepartmentUser", func() {
			cli := lark.GetLarkClient()
			users, err := lark.GetDepartmentUsers(ctx, cli, "258c49a2ba358162", 50)
			So(err, ShouldBeNil)
			str, _ := common.JsonStringEncode(users)
			fmt.Println(users)
			So(str, ShouldNotBeEmpty)
		})
	})
}

func Test_SyncLarkData(t *testing.T) {
	PatchConvey("Test_SyncLarkData", t, func() {
		PatchConvey("case1 SyncLarkData", func() {
			err := SyncLarkData(ctx)
			So(err, ShouldBeNil)
		})
	})
}
