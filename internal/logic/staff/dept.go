/**
 * @note
 * dept
 *
 * <AUTHOR>
 * @date 	2020-02-12
 */
package staff

import (
	set "github.com/deckarep/golang-set/v2"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/model/staff"
)

func (op *StaffOperator) GetDeptInfoByDeptId(deptId int64) (
	deptInfo *DepartmentInfo, err error) {
	res, err := staff.GetDepartmentByID(op.ctx.GinCtx(), deptId)
	if err != nil {
		return nil, err
	} else if res == nil || res.ID == 0 {
		return nil, bpmCom.ErrDeptNotExist
	}
	deptInfo = &DepartmentInfo{
		DepartmentId: res.ID,
		Name:         res.Name,
		Code:         res.LarkDepartmentID,
	}
	// 找parent
	if res.LarkParentDepartmentID != common.StringEmpty {
		parentDept, err := staff.GetDepartmentByLarkDepartmentID(op.ctx.GinCtx(), res.LarkParentDepartmentID)
		if err != nil {
			return nil, err
		}
		deptInfo.SuperId = parentDept.ID
		deptInfo.SuperName = parentDept.Name
	}
	// 找leader
	if res.LarkLeaderUserID != common.StringEmpty {
		leaderInfo, err := staff.GetUserByLarkUserID(op.ctx.GinCtx(), res.LarkLeaderUserID)
		if err != nil {
			return nil, err
		}
		deptInfo.ManagerId = leaderInfo.ID
		deptInfo.ManagerEmployeeId = leaderInfo.EmployeeNo
		deptInfo.ManagerAccount = leaderInfo.UserName
	}
	return deptInfo, nil
}

func (op *StaffOperator) GetAllDeptInfo() (
	deptInfos []*DepartmentInfo, err error) {
	tbs, err := staff.GetAllDepartments(op.ctx.GinCtx())
	if err != nil {
		return nil, err
	}
	leaderLarkIDs := set.NewSet[string]()                        // 用于补全leaderID
	deptLarkIDDeptMap := make(map[string]*staff.DepartmentTable) // 用于
	for _, tb := range tbs {
		if tb.LarkLeaderUserID != common.StringEmpty {
			leaderLarkIDs.Add(tb.LarkLeaderUserID)
		}
		deptLarkIDDeptMap[tb.LarkDepartmentID] = tb
	}
	// 查所有leader
	leaders, err := staff.GetUsersByLarkUserIDs(op.ctx.GinCtx(), leaderLarkIDs.ToSlice())
	if err != nil {
		return nil, err
	}
	larkIDToUserMap := make(map[string]*staff.UserTable)
	for _, leader := range leaders {
		larkIDToUserMap[leader.LarkUserID] = leader
	}

	for _, tb := range tbs {
		deptInfo := &DepartmentInfo{
			DepartmentId: tb.ID,
			Name:         tb.Name,
			Code:         tb.LarkDepartmentID,
		}
		if tb.LarkParentDepartmentID != common.StringEmpty {
			if superDept, ok := deptLarkIDDeptMap[tb.LarkParentDepartmentID]; ok {
				deptInfo.SuperId = superDept.ID
				deptInfo.SuperName = superDept.Name
			}
		}
		if tb.LarkLeaderUserID != common.StringEmpty {
			if leader, ok := larkIDToUserMap[tb.LarkLeaderUserID]; ok {
				deptInfo.ManagerId = leader.ID
				deptInfo.ManagerEmployeeId = leader.EmployeeNo
				deptInfo.ManagerAccount = leader.UserName
				deptInfo.ManagerEmail = leader.Email
			}
		}
		deptInfos = append(deptInfos, deptInfo)
	}
	return deptInfos, nil
}
