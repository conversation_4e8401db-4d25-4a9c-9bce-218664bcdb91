/**
 * @note
 * structs.go
 *
 * <AUTHOR>
 * @date 	2019-12-23
 */
package api

import (
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type ApiOperator struct {
	ctx common.HContextIface
}

func NewApiOperator(ctx common.HContextIface) *ApiOperator {
	return &ApiOperator{ctx: ctx}
}

func (op *ApiOperator) Ctx() common.HContextIface {
	return op.ctx
}

type ApiResult struct {
	Code    int         `json:"code"`
	Message interface{} `json:"message"`
	Data    interface{} `json:"data"`
}
