/**
 * @note
 * api
 *
 * <AUTHOR>
 * @date 	2019-12-23
 */
package api

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	appAuthLogic "gitlab.docsl.com/security/bpm/internal/logic/app_auth"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/httpclient"
	"net/http"
	"net/url"
	"strings"
	"time"
)

func (op *ApiOperator) SignAndDo(addr, method, appName string, param interface{}, headParam map[string]interface{},
	timeout time.Duration, retryTimes int) (resp string, code int, err error) {
	client := httpclient.NewHttpClient([]string{common.StringEmpty}, common.StringEmpty, op.Ctx().Log())
	authAppOperator := appAuthLogic.NewAuthAppOperator(op.ctx)
	m := strings.ToUpper(method)
	u, err := url.Parse(addr)
	if err != nil {
		return common.StringEmpty, common.ERR_OK, err
	}
	var getParam map[string]interface{}
	if m == http.MethodGet || m == http.MethodDelete {
		if p, ok := param.(map[string]interface{}); ok {
			getParam = p
		}
	}
	//加签
	if len(appName) > 0 {
		if authAppInfo, err := authAppOperator.QueryAuthInfoByAppName(appName); err == nil {
			if signMap, errSign := common.GetUrlSignMap(m, u.Host, u.Path, authAppInfo.AppId, authAppInfo.AppKey); errSign == nil {
				if getParam == nil {
					getParam = make(map[string]interface{})
				}
				for k, v := range signMap {
					getParam[k] = v
				}
			}
		}
	}
	if headParam == nil {
		headParam = make(map[string]interface{})
	}
	//加user-id的header
	headParam[common.HuobiSsoUserId] = op.ctx.User().UserId
	if m == http.MethodGet || m == http.MethodDelete {
		resp, code, err = client.DoWithParam(addr, m, getParam, nil, headParam, timeout, retryTimes)
		if err != nil {
			return resp, code, err
		} else if code != http.StatusOK {
			return resp, code, common.ErrHttpResponse
		}
		return resp, code, err
	} else if m == http.MethodPost || m == http.MethodPut {
		postByte, err := common.JsonEncode(param)
		if err != nil {
			return resp, code, err
		}
		resp, code, err = client.DoWithParamJSON(addr, m, getParam, postByte, headParam, timeout, retryTimes)
		if err != nil {
			return resp, code, err
		} else if code != http.StatusOK {
			return resp, code, common.ErrHttpResponse
		}
		return resp, code, err
	}
	return common.StringEmpty, http.StatusOK, bpmCom.ErrMethodNotSupported
}
