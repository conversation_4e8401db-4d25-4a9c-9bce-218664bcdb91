/**
 * @note
 * auth
 *
 * <AUTHOR>
 * @date 	2020-07-06
 */
package sdk_auth

import (
	"encoding/hex"
	"fmt"
	"github.com/google/uuid"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/common/redis"
	"strings"
	"time"
)

func (op *SdkAuthOperator) GenerateAuthToken() (token string, err error) {
	data := []byte(common.GetEnv() + uuid.New().String())
	first := strings.Replace(uuid.NewSHA1(TOKEN_UUID_NS, data).String(),
		common.StringDelimiter, common.StringEmpty, -1)
	second := strings.Replace(uuid.NewSHA1(TOKEN_UUID_NS, []byte(first)).String(),
		common.StringDelimiter, common.StringEmpty, -1)
	realToken := first + second
	fakeToken := op.encryptAuthToken(realToken)
	redisCli, errCli := redis.DefaultClient(op.ctx.GinCtx())
	if errCli != nil {
		op.ctx.Log().Errorln("Get redis client err:", err)
		return common.StringEmpty, errCli
	}
	if userInfoStr, err := common.JsonStringEncode(op.Ctx().User()); err != nil {
		op.ctx.Log().Errorln("Encode user info err:", err)
		return common.StringEmpty, err
	} else if err := redisCli.Set(fmt.Sprintf(KEY_AUTH_TOKEN, fakeToken), userInfoStr, time.Duration(common.GetConfig().AuthTokenTTL)*time.Second); err != nil {
		op.ctx.Log().Errorln("Set auth token to redis err:", err)
		return common.StringEmpty, err
	}
	return realToken, nil
}

func (op *SdkAuthOperator) encryptAuthToken(token string) string {
	return hex.EncodeToString(common.CBCEncrypt([]byte(AUTH_TOKEN_SECURITY_KEY),
		[]byte(AUTH_TOKEN_SECURITY_KEY), []byte(token)))
}

func (op *SdkAuthOperator) AuthToken(authToken string) (user *common.User, err error) {
	if authToken == common.StringEmpty {
		return nil, common.ErrMissingAMandatoryParameterf("authToken")
	}
	redisCli, errCli := redis.DefaultClient(op.ctx.GinCtx())
	if errCli != nil {
		op.ctx.Log().Errorln("Get redis client err:", err)
		return nil, errCli
	}
	fakeToken := op.encryptAuthToken(authToken)
	userInfoRet, err := redisCli.Get(fmt.Sprintf(KEY_AUTH_TOKEN, fakeToken))
	if err != nil {
		return nil, err
	}
	userInfoStr := userInfoRet.Val()
	if len(userInfoStr) == 0 {
		op.ctx.Log().Errorln("missing user info in redis value")
		return nil, common.ErrRecordNotFound
	}
	user = &common.User{}
	err = common.JsonStringDecode(userInfoStr, user)
	if err != nil {
		return nil, err
	} else if user.UserId == 0 {
		op.ctx.Log().Errorln("userId is 0 in user context")
		return nil, common.ErrRecordNotFound
	}
	return user, nil
}
