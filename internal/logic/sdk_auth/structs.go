/**
 * @note
 * structs.go
 *
 * <AUTHOR>
 * @date 	2020-07-06
 */
package sdk_auth

import (
	"github.com/google/uuid"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

var TOKEN_UUID_NS = uuid.Must(uuid.Parse("AE0AF50D-7E60-46FD-9236-7FC601E43896"))

const (
	AUTH_TOKEN_SECURITY_KEY = "19e92260a194b93c"
	KEY_AUTH_TOKEN          = "hbpm_auth_token_%s"
)

type SdkAuthOperator struct {
	ctx common.HContextIface
}

func (op *SdkAuthOperator) Ctx() common.HContextIface {
	return op.ctx
}

func NewSdkAuthOperator(ctx common.HContextIface) *SdkAuthOperator {
	return &SdkAuthOperator{ctx: ctx}
}
