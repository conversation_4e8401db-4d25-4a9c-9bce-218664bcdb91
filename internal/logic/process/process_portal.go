/**
 * @note
 *
 * <AUTHOR>
 * @date 	2020-05-10
 */

package process

import (
	"fmt"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/internal/logic/api"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"net/http"
	"os"
	"strings"
	"time"
)

// 添加三方app推送过来的任务
func (op *ProcessOperator) AddProcess(userId int64, processUuid, detail, data string, tag common2.TaskTagEnum, initiatorId int64, title, desc common2.I18nString) error {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return err
	}

	if _, err := pModel.InsertTaskForPortal(userId, processUuid, common.StringEmpty, common.StringEmpty, common.StringJsonEmpty, data,
		common2.PROCESS_TYPE_APPROVAL, common2.STATUS_NORMAL, common2.PROCESS_STATE_UNAPPROVED, title.Marshal(),
		desc.Marshal(), tag, initiatorId); err != nil {
		return err
	}
	return nil
}

/*func (op *ProcessOperator) SendNotifyAfterPortalProcessAdd(userId int64,
	notifyModes []bpmCom.NotifyTypeEnum, detailUrl string) error {
	defer func() {
		if err := recover(); err != nil {
			op.ctx.Log().Errorf("send notify error: %+v", err)
		}
	}()

	notifyOperator := notify.NewNotifyOperator(op.Ctx())
	staffOperator := staffLogic.NewStaffOperator(op.Ctx())
	userInfo, err := staffOperator.GetUserInfoByUserId(userId)
	var userName string
	if err != nil {
		userName = "某申请人"
	} else if len(userInfo.NameZh) > 0 {
		userName = userInfo.NameZh
	} else if len(userInfo.NameDisplay) > 0 {
		userName = userInfo.NameDisplay
	} else if len(userInfo.NameEn) > 0 {
		userName = userInfo.NameEn
	} else {
		userName = "某申请人"
	}
	emailContent, err := weLogic.GenerateEmailContent("", userName, weLogic.NEED_APPROVE_EMAIL_TMPL, detailUrl)
	if err != nil {
		emailContent = weLogic.GeneratePortalTaskEmailContent(detailUrl)
	}
	err = notifyOperator.SendNotify(userId,
		notify.NotifyInfo{
			DingTalkContent: weLogic.GenerateDingTalkNotifyContent("", userName, detailUrl),
			MailTitle:       "任务代办通知",
			MailContent:     emailContent,
		}, notify.NotifyConfig{Mode: notifyModes})
	if err != nil {
		op.ctx.Log().Errorf("failed to send dingtalk or email notify to user: %v", err)
		return err
	}
	return nil
}*/

func (op *ProcessOperator) QueryPortalProcessByConditions(conditions TaskListCondition,
	offset, limit int64) ([]TaskInfo, error) {
	whereMap := make(map[string]interface{})
	if len(conditions.Tag) > 0 {
		whereMap["tag"] = conditions.Tag
	}

	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}
	var processes []*processModel.ProcessTable
	processes, err = pModel.QueryPortalProcessByWhereMap(whereMap, conditions.Keyword,
		conditions.OrderBy, offset, limit)

	tasks := make([]TaskInfo, 0)
	staffOperator := staffLogic.NewStaffOperator(op.Ctx())
	for _, process := range processes {
		var initiatorName string
		if process.InitiatorId != 0 {
			userInfo, err := staffOperator.GetUserInfoByUserId(process.InitiatorId)
			if err != nil {
				return nil, err
			}
			initiatorName = userInfo.NameZh
		} else {
			initiatorName = "系统"
		}

		title, err := common.JsonStringToRaw(process.Title)
		if err != nil {
			return nil, err
		}
		desc, err := common.JsonStringToRaw(process.Desc)
		if err != nil {
			return nil, err
		}
		if len(title) == 0 || len(desc) == 0 {
			return nil, common2.ErrDirtyData
		}

		t := TaskInfo{
			Uuid:          process.Uuid,
			Title:         title,
			Desc:          desc,
			Tag:           process.Tag,
			Origin:        process.Origin,
			InitiatorName: initiatorName,
			CreateTime:    process.CreateTime,
			OpenTypes:     make([]int64, 0),
		}

		if process.Origin != common2.OriginBpm {
			data := ProcData{}
			err = common.JsonDecode([]byte(process.Data), &data)
			if err != nil {
				return nil, err
			}
			t.Url = data.Url
			t.OpenTypes = data.OpenTypes
		}
		tasks = append(tasks, t)
	}
	return tasks, nil
}

func (op *ProcessOperator) QueryPortalProcessCntByConditions(conditions TaskListCondition) (int64, error) {
	whereMap := make(map[string]interface{})
	if len(conditions.Tag) > 0 {
		whereMap["tag"] = conditions.Tag
	}
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return 0, err
	}
	return pModel.QueryPortalProcessCnt(whereMap, conditions.Keyword)
}

func (op *ProcessOperator) UpdatePortalProcess(process *processModel.ProcessTable,
	uuid string, action common2.ProcessActionEnum, comment string) error {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return err
	}
	var newState common2.ProcessStateEnum
	if action == common2.PROCESS_ACTION_REJECT {
		newState = common2.PROCESS_STATE_REJECTED
	} else if action == common2.PROCESS_ACTION_APPROVE {
		newState = common2.PROCESS_STATE_APPROVED
	}

	if process.State != common2.PROCESS_STATE_UNAPPROVED { //状态已经不对
		return common2.ErrProcessStateIncorrect
	}

	data := ProcData{}
	err = common.JsonDecode([]byte(process.Data), &data)
	if err != nil {
		return err
	}

	data.Comment = comment
	newData, err := common.JsonEncode(data)
	if err != nil {
		return err
	}
	updateMap := map[string]interface{}{
		"data":  newData,
		"state": newState,
	}
	whereMap := map[string]interface{}{
		"uuid": uuid,
	}
	_, err = pModel.UpdateProcessRuntimeByWhereMap(updateMap, whereMap)
	if err != nil {
		return err
	}

	// 如果是portal过来的更新请求，则需要回调三方APP地址,同步任务状态；
	// 如果是内部app过来的请求（同步任务状态）,则不需再回调
	if strings.Contains(op.Ctx().GinCtx().Request.URL.Path, "/p") {
		err = op.ProcessCallBack(data.CallBackUrl, process.Uuid, data.Comment, process.Origin, newState)
		if err != nil {
			op.SendDingNotify("Error", fmt.Sprintf("callback to internal app path[%s] failed after update task[%s]",
				data.CallBackUrl, process.Uuid), err)
		}
	}
	return nil
}

func (op *ProcessOperator) SendDingNotify(level, msg string, err error) {
	var errStr string
	if err != nil {
		errStr = err.Error()
	}
	content, _ := common.ToPrettyJsonString(map[string]interface{}{
		"env":   common.GetEnv(),
		"trace": op.ctx.Log().GetTrace(),
		"content": map[string]interface{}{
			"level":   level,
			"message": msg,
			"error":   errStr,
		},
		"host": func() string {
			host, _ := os.Hostname()
			return host
		}(),
	})
	errSend := lark.SendLarkRobotMessage(op.ctx.GinCtx(), content)
	if errSend != nil {
		op.ctx.Log().Errorln("send dingtalk message err:", errSend)
	}
}

func (op *ProcessOperator) ProcessCallBack(callBackUrl, uuid, comment, appName string, state common2.ProcessStateEnum) error {
	if len(callBackUrl) > 0 {
		apiOperator := api.NewApiOperator(op.Ctx())
		paramMap := map[string]interface{}{
			"processUuid": uuid,
			"state":       state,
			"comment":     comment,
		}
		resp, code, err := apiOperator.SignAndDo(callBackUrl, "POST", appName, paramMap, nil, 10*time.Second, 3)
		if err != nil {
			return err
		} else if code != http.StatusOK {
			return common.ErrHttpResponse
		}

		resMap := make(map[string]interface{})
		var res api.ApiResult
		if err := common.JsonStringDecode(resp, &resMap); err != nil {
			return err
		} else if _, ok := resMap["code"]; !ok {
			return common.ErrHttpResponse
		} else if err = common.JsonStringDecode(resp, &res); err != nil {
			return err
		} else if res.Code != common.ERR_OK && res.Code != http.StatusOK {
			return common.ErrHttpResponse
		}
	}
	return nil
}

func (op *ProcessOperator) DelPortalProcess(uuid string) error {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return err
	}
	_, err = pModel.DelPortalProcessByUuid(uuid)
	if err != nil {
		return err
	}
	return nil
}
