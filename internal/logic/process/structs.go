/**
 * @note
 * 审批等流程相关结构体
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */

package process

import (
	"encoding/json"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type ProcessOperator struct {
	ctx common.HContextIface
}

func (op *ProcessOperator) Ctx() common.HContextIface {
	return op.ctx
}

func NewProcessOperator(hctx common.HContextIface) *ProcessOperator {
	return &ProcessOperator{ctx: hctx}
}

type ListProcessCondition struct {
	WorkflowName             string                 `json:"workflowName"`
	WorkflowTemplateUuid     string                 `json:"workflowTemplateUuid"`
	WorkflowTemplateUuids    []string               `json:"workflowTemplateUuids"`
	WorkflowTemplateGroupId  int64                  `json:"workflowTemplateGroupId"`
	WorkflowTemplateGroupIds []int64                `json:"workflowTemplateGroupIds"`
	WorkflowUuids            []string               `json:"workflowUuids"`
	WorkflowSerialIds        []string               `json:"workflowSerialIds"`
	WorkflowState            string                 `json:"workflowState"`
	WorkflowStates           []string               `json:"workflowStates"`
	UserName                 string                 `json:"userName"`
	WorkflowOrUserName       string                 `json:"workflowOrUserName"`
	BothWorkflowAndUser      bool                   `json:"-"`
	Type                     bpmCom.ProcessTypeEnum `json:"type"`
	Types                    []string               `json:"types"`
	State                    string                 `json:"state"`
	States                   []string               `json:"states"`
	StartTime                int64                  `json:"startTime" validate:"gte=0"`
	EndTime                  int64                  `json:"endTime" validate:"gte=0"`
	UpdateStartTime          int64                  `json:"updateStartTime" validate:"gte=0"`
	UpdateEndTime            int64                  `json:"updateEndTime" validate:"gte=0"`
}

type TaskListCondition struct {
	Keyword string               `json:"keyword"`
	Tag     bpmCom.TaskTagEnum   `json:"tag"`
	OrderBy bpmCom.TimeOrderType `json:"orderBy"`
}

type TaskInfo struct {
	Uuid          string             `json:"uuid"`
	Title         json.RawMessage    `json:"title"`
	Desc          json.RawMessage    `json:"desc"`
	Tag           bpmCom.TaskTagEnum `json:"tag"`
	InitiatorName string             `json:"initiatorName"`
	CreateTime    int64              `json:"createTime"`
	Url           string             `json:"url,omitempty"`
	Origin        string             `json:"origin"`
	OpenTypes     []int64            `json:"openTypes,omitempty"`
}

type ProcData struct {
	Url         string                  `json:"url,omitempty" validate:"url"`
	Messaged    bool                    `json:"notified,omitempty"` //是否已经通知过了
	NotifyMode  []bpmCom.NotifyTypeEnum `json:"notifyMode,omitempty"`
	Comment     string                  `json:"comment,omitempty"`
	CallBackUrl string                  `json:"callBackUrl,omitempty"`
	OpenTypes   []int64                 `json:"openTypes,omitempty"`
}
