/**
 * @note
 * 审批等流程相关逻辑
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */

package process

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	formLogic "gitlab.docsl.com/security/bpm/internal/logic/form"
	notifyLogic "gitlab.docsl.com/security/bpm/internal/logic/notify"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wCommon "gitlab.docsl.com/security/bpm/internal/logic/workflow/common"
	"gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	"gitlab.docsl.com/security/bpm/internal/model/workflow"
	"time"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

func (op *ProcessOperator) QueryProcessRuntimeByNodeUuidAndType(
	nodeUuid string, processType bpmCom.ProcessTypeEnum) (process []*processModel.ProcessTable, err error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}
	return pModel.QueryProcessRuntimeByNodeUuidAndType(nodeUuid, processType)
}

func (op *ProcessOperator) QueryProcessRuntimeByWorkflowUuid(
	workflowUuid string) (process []*processModel.ProcessTable, err error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}
	return pModel.QueryProcessRuntimeByWorkflowUuid(workflowUuid)
}

func (op *ProcessOperator) QueryProcessRuntimeByWorkflowUuidAndUserIds(
	workflowUuid string, userIds []int64) (process []*processModel.ProcessTable, err error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}
	return pModel.QueryProcessRuntimeByWorkflowUuidAndUserIds(workflowUuid, userIds)
}

func (op *ProcessOperator) QueryUserProcessRuntimeExcludeSomeStatesBySeveralConditions(conditions ListProcessCondition, workflowUuids []string,
	offset, limit int64) ([]*processModel.ProcessTable, error) {
	whereMap := make(map[string]interface{})
	if len(conditions.Type) > 0 {
		whereMap["type"] = conditions.Type
	}

	whereMap["origin"] = bpmCom.OriginBpm
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}

	return pModel.QueryUserProcessRuntimeExcludeSomeStatesByWhereMapAndOffsetAndLimit(whereMap, workflowUuids, conditions.States, conditions.Types,
		offset, limit, conditions.StartTime, conditions.EndTime, conditions.UpdateStartTime, conditions.UpdateEndTime)
}

func (op *ProcessOperator) QueryUserDoneProcess(conditions ListProcessCondition, workflowUuids []string,
	offset, limit int64) ([]*processModel.ProcessTable, int64, error) {

	whereMap := make(map[string]interface{})

	if len(conditions.Type) > 0 {
		whereMap["type"] = conditions.Type
	}

	whereMap["origin"] = bpmCom.OriginBpm

	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, 0, err
	}

	return pModel.QueryUserDoneProcess(whereMap, workflowUuids, conditions.States, conditions.Types,
		offset, limit, conditions.StartTime, conditions.EndTime, conditions.UpdateStartTime, conditions.UpdateEndTime)
}

func (op *ProcessOperator) QueryUserProcessRuntimeCntBySeveralConditions(conditions ListProcessCondition, workflowUuids []string) (int64, error) {
	whereMap := make(map[string]interface{})
	if len(conditions.Type) > 0 {
		whereMap["type"] = conditions.Type
	}
	whereMap["origin"] = bpmCom.OriginBpm
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return 0, err
	}
	return pModel.QueryUserProcessRuntimeCntByWhereMap(whereMap, workflowUuids, conditions.States, conditions.Types,
		conditions.StartTime, conditions.EndTime, conditions.UpdateStartTime, conditions.UpdateEndTime)
}

func (op *ProcessOperator) UpdateTransactorProcess(processTable *processModel.ProcessTable,
	comment string, data interface{}, action bpmCom.ProcessActionEnum) (larkMsg notifyLogic.LarkMsg, err error) {

	if processTable.State != bpmCom.PROCESS_STATE_UNDONE {
		return larkMsg, bpmCom.ErrProcessStateIncorrect
	}

	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return larkMsg, err
	}

	wModel, err := workflow.NewWorkflowModel(op, false)
	if err != nil {
		return larkMsg, err
	}

	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(processTable.WorkflowUuid)
	if err != nil {
		return larkMsg, err
	}

	//查出对应node
	nodeTable, err := wModel.QueryNodeRuntimeByUuid(processTable.NodeUuid)
	if err != nil {
		return larkMsg, err
	} else if nodeTable.Type != bpmCom.NODE_TYPE_TRANSACTOR {
		return larkMsg, bpmCom.ErrInvalidNodeType
	} else if nodeTable.State != bpmCom.STATE_PENDING {
		return larkMsg, bpmCom.ErrInvalidNodeState
	}
	//查出对应node的所有审批任务
	allProcesses, err := pModel.QueryProcessRuntimeByNodeUuidAndType(processTable.NodeUuid, bpmCom.PROCESS_TYPE_TRANSACTOR)
	if err != nil {
		return larkMsg, err
	}
	//解析对应node的数据
	transactorNodeData := &engine.TransactorTaskData{}
	if err := common.JsonDecode([]byte(nodeTable.Data), &transactorNodeData); err != nil {
		return larkMsg, err
	}

	transactorNodeDetail := &engine.TransactorTaskDetail{}
	if err := common.JsonDecode([]byte(nodeTable.Detail), &transactorNodeDetail); err != nil {
		return larkMsg, err
	}

	//2020-5-24 不让审批人改关联审批单，只有发起人可以改
	formOperator := formLogic.NewFormOperator(op.ctx)
	transactorNodeFront := &formLogic.Front{} //校验此处的修改权限
	if err := common.JsonDecode([]byte(nodeTable.Front), &transactorNodeFront); err != nil {
		return larkMsg, err
	}
	data = formOperator.FilterUnWritableFields(data, transactorNodeFront)
	if transactorNodeDetail.Comment && len(comment) == 0 { //需要评论才可以审批
		return larkMsg, bpmCom.ErrProcessNeedComment
	}

	//处理需要同时更新其他人的process
	var otherUnApprovedProcessUuids, otherInitializedProcessUuids []string
	var newState, otherNewState bpmCom.ProcessStateEnum
	newState = bpmCom.PROCESS_STATE_DONE
	if transactorNodeDetail.Mode == bpmCom.APPROVAL_MODE_OR_SIGN {
		//否则需要看情况，看审批人的这次审批是否会直接影响审批结果
		var needDealWithOtherProcesses bool
		//由于bpm v2的需求，组装成树状的process再处理
		_, processInfoMap, err := engine.AssembleProcessTablesToSortedProcessInfos(allProcesses)
		if err != nil {
			return larkMsg, err
		}
		pInfo, exist := processInfoMap[processTable.Uuid]
		if !exist {
			return larkMsg, bpmCom.ErrProcessNotExist
		}
		if pInfo.SubProcess == nil { //无子process
			if pInfo.ParentProcess == nil { //也没有父process，表示是个根process
				needDealWithOtherProcesses = true
			} else if pInfo.Detail.SubType != bpmCom.PROCESS_SUBTYPE_COUNTERSIGN_BEFORE { //不是父process加签出来的先审批process
				needDealWithOtherProcesses = true
			}
		} else if hasCountersignAfterSubProcess := func(sp *engine.SortedProcessInfo) bool {
			for _, p := range sp.SubProcess {
				if p.Detail.SubType == bpmCom.PROCESS_SUBTYPE_COUNTERSIGN_AFTER {
					return true
				}
			}
			return false
		}; !hasCountersignAfterSubProcess(pInfo) { //子process中不存在一个在此后审批的加签process
			needDealWithOtherProcesses = true
		}
		if needDealWithOtherProcesses {
			otherNewState = bpmCom.PROCESS_STATE_DONE_BY_OTHERS
			for _, p := range allProcesses {
				if p.Uuid != processTable.Uuid {
					if p.State == bpmCom.PROCESS_STATE_UNDONE {
						otherUnApprovedProcessUuids = append(otherUnApprovedProcessUuids, p.Uuid)
					} else if p.State == bpmCom.PROCESS_STATE_INITIALIZED {
						otherInitializedProcessUuids = append(otherInitializedProcessUuids, p.Uuid)
					}
				}
			}
		}
	}
	//合并提交的数据，处理node的数据段
	//2020-05-09 增加dataHistory的配置
	if !common.IsNil(data) {
		mergeOperator := engine.NewMergeOperator(engine.MergeConfig{Mode: bpmCom.MERGE_MODE_OVERRIDE})
		dataHistory := wCommon.ExtractDataHistory(transactorNodeData.Input)
		prueData := wCommon.DeleteDataHistory(transactorNodeData.Input)
		staffOperator := staffLogic.NewStaffOperator(op.Ctx())
		userInfo, err := staffOperator.GetUserInfoByUserId(op.Ctx().User().UserId)
		if err != nil {
			op.Ctx().Log().Errorln("update process: query userInfo error:", err)
			userInfo = &staffLogic.UserInfo{
				UserId:      op.Ctx().User().UserId,
				Account:     op.Ctx().User().AccountName,
				NameZh:      op.Ctx().User().DisplayName,
				NameEn:      op.Ctx().User().DisplayName,
				NameDisplay: op.Ctx().User().DisplayName,
				EmployeeId:  op.Ctx().User().EmployeeId,
			}
		}
		userInfo.Email = common.StringEmpty //抹去用户敏感信息
		userInfo.Phone = common.StringEmpty
		dataHistory = append(dataHistory, &wCommon.DataHistoryInfo{
			ProcessUuid: processTable.Uuid,
			PrevData:    prueData,
			Data:        data,
			User:        userInfo,
			OpTime:      time.Now().Unix(),
		})
		transactorNodeData.Input = mergeOperator.Merge([]interface{}{prueData, wCommon.DeleteDataHistory(data)}) //用户的data也过滤一下，防止用户自己瞎传
		wCommon.SetDataHistory(transactorNodeData.Input, dataHistory)
	}

	nd, err := common.JsonEncode(transactorNodeData)
	if err != nil {
		return larkMsg, err
	}

	//处理对应process的数据段
	processData := &processModel.ApprovalProcessData{}
	if err := common.JsonDecode([]byte(processTable.Data), &processData); err != nil {
		return larkMsg, err
	}
	processData.Comment = comment
	pd, err := common.JsonEncode(processData)
	if err != nil {
		return larkMsg, err
	}

	//记录此节点审批人
	if transactorNodeDetail.Mode == bpmCom.APPROVAL_MODE_OR_SIGN {
		transactorNodeDetail.OtherTransactorUser = &staffLogic.UserInfo{
			UserId:      op.Ctx().User().UserId,
			Account:     op.Ctx().User().AccountName,
			NameZh:      op.Ctx().User().DisplayName,
			NameEn:      op.Ctx().User().DisplayName,
			NameDisplay: op.Ctx().User().DisplayName,
			EmployeeId:  op.Ctx().User().EmployeeId,
		}
	}

	nodeDetailData, err := common.JsonEncode(transactorNodeDetail)
	if err != nil {
		return larkMsg, err
	}

	//用事务保证一致性，更新process，node表两个表的数据
	err = wModel.BatchUpdateTransactorProcessAndNodeData(processTable.Uuid, string(pd),
		newState, processTable.NodeUuid, string(nd), string(nodeDetailData), otherUnApprovedProcessUuids, otherNewState, otherInitializedProcessUuids)

	if err == nil {
		workflowName, initiatorName := engine.GetWorkflowNameAndUserName(op.Ctx(), workflowTable)
		larkMsg = notifyLogic.GenerateLarkHandleDoneContent(workflowName, initiatorName, processTable.Uuid, op.Ctx().User().DisplayName, workflowTable.CreateTime, transactorNodeDetail.Notify.LarkCustomTemplate, nodeTable.Data)
		//异步通知未审批的人
		go func() {
			if transactorNodeDetail.Mode == bpmCom.APPROVAL_MODE_OR_SIGN {
				notifyOperator := notifyLogic.NewNotifyOperator(op.Ctx())
				for _, p := range allProcesses {
					if p.Uuid != processTable.Uuid {
						//通知给其它人
						if p.State == bpmCom.PROCESS_STATE_UNDONE {
							emailContent, err := engine.GenerateEmailContentByTmpl(workflowName, initiatorName, engine.DONE_BY_OTHERS_EMAIL_TMPL, engine.GetToCProcessUrl(p.Uuid), nil)
							if err != nil {
								emailContent = engine.GenerateEmailNotifyOtherApprovalContent(p.Uuid)
							}
							if errNotify := notifyOperator.SendNotify(p.UserId, notifyLogic.NotifyMsg{
								LarkContent: notifyLogic.GenerateLarkNotifyOtherApproverContent(workflowName, initiatorName, p.Uuid, workflowTable.CreateTime, transactorNodeDetail.Notify.LarkCustomTemplate, nodeTable.Data),
								MailTitle:   "流程被他人处理通知",
								MailContent: emailContent,
							}, transactorNodeDetail.Notify.Mode); errNotify != nil {
								op.Ctx().Log().Errorln(err)
							}
						}
					}
				}
			}
		}()
	}

	return larkMsg, err
}

func (op *ProcessOperator) UpdateApprovalProcess(processTable *processModel.ProcessTable,
	comment string, data interface{}, action bpmCom.ProcessActionEnum) (
	larkMsg notifyLogic.LarkMsg, err error) {

	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return larkMsg, err
	}

	wModel, err := workflow.NewWorkflowModel(op, false)
	if err != nil {
		return larkMsg, err
	}

	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(processTable.WorkflowUuid)
	if err != nil {
		return larkMsg, err
	}

	//检查process的状态
	if processTable.State != bpmCom.PROCESS_STATE_UNAPPROVED { //状态已经不对
		return larkMsg, bpmCom.ErrProcessStateIncorrect
	}

	//查出对应node
	nodeTable, err := wModel.QueryNodeRuntimeByUuid(processTable.NodeUuid)
	if err != nil {
		return larkMsg, err
	} else if nodeTable.Type != bpmCom.NODE_TYPE_APPROVAL {
		return larkMsg, bpmCom.ErrInvalidNodeType
	} else if nodeTable.State != bpmCom.STATE_PENDING {
		return larkMsg, bpmCom.ErrInvalidNodeState
	}

	//查出对应node的所有审批任务
	allProcesses, err := pModel.QueryProcessRuntimeByNodeUuidAndType(processTable.NodeUuid, bpmCom.PROCESS_TYPE_APPROVAL)
	if err != nil {
		return larkMsg, err
	}

	//组装成树形
	_, processInfoMap, err := engine.AssembleProcessTablesToSortedProcessInfos(allProcesses)
	if err != nil {
		return larkMsg, err
	}
	pInfo, exist := processInfoMap[processTable.Uuid]
	if !exist {
		return larkMsg, bpmCom.ErrProcessNotExist
	}

	//解析对应node的数据
	approvalNodeData := &engine.ApprovalTaskData{}
	if err := common.JsonDecode([]byte(nodeTable.Data), &approvalNodeData); err != nil {
		return larkMsg, err
	}

	approvalNodeDetail := &engine.ApprovalTaskDetail{}
	if err := common.JsonDecode([]byte(nodeTable.Detail), &approvalNodeDetail); err != nil {
		return larkMsg, err
	}

	//2020-5-24 不让审批人改关联审批单，只有发起人可以改
	formOperator := formLogic.NewFormOperator(op.ctx)
	approvalNodeFront := &formLogic.Front{} //校验此处的修改权限
	if err := common.JsonDecode([]byte(nodeTable.Front), &approvalNodeFront); err != nil {
		return larkMsg, err
	}
	data = formOperator.FilterUnWritableFields(data, approvalNodeFront)
	if approvalNodeDetail.Comment && len(comment) == 0 { //需要评论才可以审批
		return larkMsg, bpmCom.ErrProcessNeedComment
	}

	//处理需要同时更新其他人的process
	var otherNeedToDealProcessUuids []string
	var newState, otherNewState bpmCom.ProcessStateEnum
	if action == bpmCom.PROCESS_ACTION_REJECT {
		newState = bpmCom.PROCESS_STATE_REJECTED
	} else if action == bpmCom.PROCESS_ACTION_APPROVE {
		newState = bpmCom.PROCESS_STATE_APPROVED
	}

	//如果是拒绝，则一定会影响所有的process
	var needDealWithOtherProcesses bool
	if action == bpmCom.PROCESS_ACTION_REJECT {
		otherNewState = bpmCom.PROCESS_STATE_REJECTED_BY_OTHERS
		for _, p := range allProcesses {
			if p.Uuid != processTable.Uuid {
				needDealWithOtherProcesses = true
				if p.State == bpmCom.PROCESS_STATE_UNAPPROVED || p.State == bpmCom.PROCESS_STATE_INITIALIZED {
					otherNeedToDealProcessUuids = append(otherNeedToDealProcessUuids, p.Uuid)
				}
			}
		}
	} else if approvalNodeDetail.Mode == bpmCom.APPROVAL_MODE_OR_SIGN && action == bpmCom.PROCESS_ACTION_APPROVE {
		if pInfo.SubProcess == nil { //无子process
			if pInfo.ParentProcess == nil { //也没有父process，表示是个根process
				needDealWithOtherProcesses = true
			} else if pInfo.Detail.SubType != bpmCom.PROCESS_SUBTYPE_COUNTERSIGN_BEFORE { //不是父process加签出来的先审批process
				needDealWithOtherProcesses = true
			}
		} else if hasCountersignAfterSubProcess := func(sp *engine.SortedProcessInfo) bool {
			for _, p := range sp.SubProcess {
				if p.Detail.SubType == bpmCom.PROCESS_SUBTYPE_COUNTERSIGN_AFTER {
					return true
				}
			}
			return false
		}; !hasCountersignAfterSubProcess(pInfo) { //子process中不存在一个在此后审批的加签process
			needDealWithOtherProcesses = true
		}

		if needDealWithOtherProcesses {
			otherNewState = bpmCom.PROCESS_STATE_APPROVED_BY_OTHERS
			for _, p := range allProcesses {
				if p.Uuid != processTable.Uuid {
					if p.State == bpmCom.PROCESS_STATE_UNAPPROVED || p.State == bpmCom.PROCESS_STATE_INITIALIZED {
						otherNeedToDealProcessUuids = append(otherNeedToDealProcessUuids, p.Uuid)
					}
				}
			}
		}
	}

	//合并提交的数据，处理node的数据段
	//2020-05-09 增加dataHistory的配置
	if !common.IsNil(data) {
		mergeOperator := engine.NewMergeOperator(engine.MergeConfig{Mode: bpmCom.MERGE_MODE_OVERRIDE})
		dataHistory := wCommon.ExtractDataHistory(approvalNodeData.Input)
		prueData := wCommon.DeleteDataHistory(approvalNodeData.Input)
		staffOperator := staffLogic.NewStaffOperator(op.Ctx())
		userInfo, err := staffOperator.GetUserInfoByUserId(op.Ctx().User().UserId)
		if err != nil {
			op.Ctx().Log().Errorln("update process: query userInfo error:", err)
			userInfo = &staffLogic.UserInfo{
				UserId:      op.Ctx().User().UserId,
				Account:     op.Ctx().User().AccountName,
				NameZh:      op.Ctx().User().DisplayName,
				NameEn:      op.Ctx().User().DisplayName,
				NameDisplay: op.Ctx().User().DisplayName,
				EmployeeId:  op.Ctx().User().EmployeeId,
			}
		}
		userInfo.Email = common.StringEmpty //抹去用户敏感信息
		userInfo.Phone = common.StringEmpty
		dataHistory = append(dataHistory, &wCommon.DataHistoryInfo{
			ProcessUuid: processTable.Uuid,
			PrevData:    prueData,
			Data:        data,
			User:        userInfo,
			OpTime:      time.Now().Unix(),
		})
		approvalNodeData.Input = mergeOperator.Merge([]interface{}{prueData, wCommon.DeleteDataHistory(data)}) //用户的data也过滤一下，防止用户自己瞎传
		wCommon.SetDataHistory(approvalNodeData.Input, dataHistory)
	}

	nd, err := common.JsonEncode(approvalNodeData)
	if err != nil {
		return larkMsg, err
	}

	//处理对应process的数据段
	processData := &processModel.ApprovalProcessData{}
	if err := common.JsonDecode([]byte(processTable.Data), &processData); err != nil {
		return larkMsg, err
	}
	processData.Comment = comment
	pd, err := common.JsonEncode(processData)
	if err != nil {
		return larkMsg, err
	}

	//记录此节点审批人
	if needDealWithOtherProcesses {
		approvalNodeDetail.OtherApproverUser = &staffLogic.UserInfo{
			UserId:      op.Ctx().User().UserId,
			Account:     op.Ctx().User().AccountName,
			NameZh:      op.Ctx().User().DisplayName,
			NameEn:      op.Ctx().User().DisplayName,
			NameDisplay: op.Ctx().User().DisplayName,
			EmployeeId:  op.Ctx().User().EmployeeId,
		}
	}

	nodeDetailData, err := common.JsonEncode(approvalNodeDetail)
	if err != nil {
		return larkMsg, err
	}

	//用事务保证一致性，更新process，node表两个表的数据
	err = wModel.BatchUpdateApprovalProcessAndNodeData(processTable.Uuid, string(pd),
		newState, processTable.NodeUuid, string(nd), string(nodeDetailData), otherNeedToDealProcessUuids, otherNewState)

	//异步通知未审批的人
	if err == nil {
		workflowName, initiatorName := engine.GetWorkflowNameAndUserName(op.Ctx(), workflowTable)
		switch action {
		case bpmCom.PROCESS_ACTION_APPROVE:
			larkMsg = notifyLogic.GenerateLarkApprovalDoneContent(workflowName, initiatorName, processTable.Uuid, op.Ctx().User().DisplayName, "审批通过", workflowTable.CreateTime, approvalNodeDetail.Notify.LarkCustomTemplate, nodeTable.Data)
		case bpmCom.PROCESS_ACTION_REJECT:
			larkMsg = notifyLogic.GenerateLarkApprovalDoneContent(workflowName, initiatorName, processTable.Uuid, op.Ctx().User().DisplayName, "审批拒绝", workflowTable.CreateTime, approvalNodeDetail.Notify.LarkCustomTemplate, nodeTable.Data)
		}
		go func() {
			if needDealWithOtherProcesses {
				notifyOperator := notifyLogic.NewNotifyOperator(op.Ctx())
				var notifyUuid []string

				notifyUuid = append(notifyUuid, otherNeedToDealProcessUuids...)

				for _, uuid := range notifyUuid {
					if pInfo, exist := processInfoMap[uuid]; exist {
						//通知给其它人
						emailContent, err := engine.GenerateEmailContentByTmpl(workflowName, initiatorName, engine.DONE_BY_OTHERS_EMAIL_TMPL, engine.GetToCProcessUrl(pInfo.Table.Uuid), nil)
						if err != nil {
							emailContent = engine.GenerateEmailNotifyOtherApprovalContent(pInfo.Table.Uuid)
						}
						if errNotify := notifyOperator.SendNotify(pInfo.Table.UserId, notifyLogic.NotifyMsg{
							LarkContent: notifyLogic.GenerateLarkNotifyOtherApproverContent(workflowName, initiatorName, pInfo.Table.Uuid, workflowTable.CreateTime, approvalNodeDetail.Notify.LarkCustomTemplate, nodeTable.Data),
							MailTitle:   "流程被他人处理通知",
							MailContent: emailContent,
						}, approvalNodeDetail.Notify.Mode); errNotify != nil {
							op.Ctx().Log().Errorln(err)
						}
					}
				}
			}
		}()
	}
	return larkMsg, err
}

func (op *ProcessOperator) AddSubApprovalProcess(subAction bpmCom.ProcessActionEnum,
	subType bpmCom.ProcessSubTypeEnum, processTable *processModel.ProcessTable, comment, explain string,
	explainVisible bool, staffInfos []*staffLogic.StaffInfo) (string, error) {
	wModel, err := workflow.NewWorkflowModel(op, false)
	if err != nil {
		return common.StringEmpty, err
	}
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return common.StringEmpty, err
	}
	nodeTable, err := wModel.QueryNodeRuntimeByUuid(processTable.NodeUuid)
	if err != nil {
		return common.StringEmpty, err
	}
	//查node，校验权限
	var notify notifyLogic.NotifyConfig
	var allowedAction []bpmCom.ProcessActionEnum
	switch nodeTable.Type {
	case bpmCom.NODE_TYPE_APPROVAL:
		var nodeDetail engine.ApprovalTaskDetail
		if err = common.JsonStringDecode(nodeTable.Detail, &nodeDetail); err != nil {
			return common.StringEmpty, err
		}
		notify = nodeDetail.Notify
		allowedAction = nodeDetail.AllowedAction
	case bpmCom.NODE_TYPE_TRANSACTOR:
		var nodeDetail engine.TransactorTaskDetail
		if err = common.JsonStringDecode(nodeTable.Detail, &nodeDetail); err != nil {
			return common.StringEmpty, err
		}
		notify = nodeDetail.Notify
		allowedAction = nodeDetail.AllowedAction
	default:
		return common.StringEmpty, bpmCom.ErrInvalidNodeType
	}
	if !op.HasActionPermission(subAction, allowedAction) {
		return common.StringEmpty, common.ErrNoPermission
	}
	//查人
	staffOperator := staffLogic.NewStaffOperator(op.Ctx())
	userInfo, err := staffOperator.GetUserInfoByUserId(staffInfos[0].UserId)
	if err != nil {
		return common.StringEmpty, err
	}
	var approvalData processModel.ApprovalProcessData
	if err := common.JsonStringDecode(processTable.Data, &approvalData); err != nil {
		return common.StringEmpty, err
	}
	approvalData.Comment = comment
	switch subType {
	case bpmCom.PROCESS_SUBTYPE_HANDOVER:
		approvalData.HandoverExplain = explain
		approvalData.HandoverExplainVisible = explainVisible
	case bpmCom.PROCESS_SUBTYPE_COUNTERSIGN_BEFORE:
		approvalData.CountersignBeforeExplain = explain
		approvalData.CountersignBeforeExplainVisible = explainVisible
	case bpmCom.PROCESS_SUBTYPE_COUNTERSIGN_AFTER:
		approvalData.CountersignAfterExplain = explain
		approvalData.CountersignAfterExplainVisible = explainVisible
	}

	newProcessUuid := common.NewUuid(common.RESOURCE_TYPE_PROCESS)
	var newState bpmCom.ProcessStateEnum
	switch subAction {
	case bpmCom.PROCESS_ACTION_HANDOVER:
		newState = bpmCom.PROCESS_STATE_HANDOVERED
	case bpmCom.PROCESS_ACTION_COUNTERSIGN:
		if subType == bpmCom.PROCESS_SUBTYPE_COUNTERSIGN_AFTER {
			if processTable.Type == bpmCom.PROCESS_TYPE_TRANSACTOR { //2020-8-6 增加对办理人的支持
				newState = bpmCom.PROCESS_STATE_DONE
			} else {
				newState = bpmCom.PROCESS_STATE_APPROVED
			}
		} else if subType == bpmCom.PROCESS_SUBTYPE_COUNTERSIGN_BEFORE {
			newState = bpmCom.PROCESS_STATE_INITIALIZED
			approvalData.Messaged = false //加签后，需要重新审批，所以通知重新发
		} else {
			return common.StringEmpty, common.ErrInvalidParameterOptionf("process subType")
		}
	default:
		return common.StringEmpty, common.ErrInvalidParameterOptionf("process action")
	}
	_, err = pModel.UpdateAndInsertProcessRuntime(processTable.Uuid, approvalData,
		newState, userInfo.UserId, newProcessUuid, processTable.WorkflowUuid, processTable.NodeUuid, processModel.ApprovalProcessDetail{
			Mode:       bpmCom.APPROVAL_MODE_AND_SIGN,
			Notify:     notify,
			SubType:    subType,
			ParentUuid: processTable.Uuid,
		}, processModel.ApprovalProcessData{},
		bpmCom.STATUS_NORMAL, bpmCom.PROCESS_STATE_INITIALIZED, processTable.Type, subType,
		processTable.Title, processTable.Desc, processTable.InitiatorId)
	return newProcessUuid, err
}

func (op *ProcessOperator) RemarkProcess(processTable *processModel.ProcessTable,
	remark string,
	file []*processModel.RemarkFileInfo,
	atStaff []*staffLogic.StaffInfo,
	visibleStaff []*staffLogic.StaffInfo, notifyConfig notifyLogic.NotifyConfig) error {
	wModel, err := workflow.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	nodeTable, err := wModel.QueryNodeRuntimeByUuid(processTable.NodeUuid)
	if err != nil {
		return err
	}
	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(processTable.WorkflowUuid)
	if err != nil {
		return err
	}
	var userIds []int64
	m := make(map[int64]bool) //用于去重
	var atInitiator bool      //at发起人情况特殊处理
	for _, staff := range atStaff {
		if staff.Type == bpmCom.STAFF_TYPE_PERSON && staff.UserId > 0 && !m[staff.UserId] {
			userIds = append(userIds, staff.UserId)
			m[staff.UserId] = true
			if staff.UserId == processTable.InitiatorId { //存在发起人
				atInitiator = true
			}
		}
	}
	var data processModel.ApprovalProcessData
	var nodeDetail engine.ApprovalTaskDetail
	if err := common.JsonStringDecode(nodeTable.Detail, &nodeDetail); err != nil {
		return err
	} else if err := common.JsonStringDecode(processTable.Data, &data); err != nil {
		return err
	}
	if !op.HasActionPermission(bpmCom.PROCESS_ACTION_REMARK, nodeDetail.AllowedAction) {
		return common.ErrNoPermission
	}
	atProcessTables, err := op.QueryProcessRuntimeByWorkflowUuidAndUserIds(processTable.WorkflowUuid, userIds)
	if err != nil {
		return err
	}
	//查询@人的信息
	var atUserIds []int64
	atUserIdProcessUuidMap := make(map[int64]string)
	for _, atProcessTable := range atProcessTables {
		if _, ok := atUserIdProcessUuidMap[atProcessTable.UserId]; !ok { //需要去重
			atUserIds = append(atUserIds, atProcessTable.UserId)
			atUserIdProcessUuidMap[atProcessTable.UserId] = atProcessTable.Uuid
		}
	}
	if atInitiator { //@发起人特殊处理
		if _, ok := atUserIdProcessUuidMap[processTable.InitiatorId]; !ok {
			atUserIds = append(atUserIds, processTable.InitiatorId)
		} else {
			atInitiator = false //已有process也属于发起人的情况，就不需要额外处理了
		}
	}
	staffOperator := staffLogic.NewStaffOperator(op.Ctx())
	atUserInfos := make([]*staffLogic.UserInfo, 0)
	for _, atUserId := range atUserIds {
		userInfo, err := staffOperator.GetUserInfoByUserId(atUserId)
		if err != nil {
			op.Ctx().Log().Errorln("get userInfo error:", err)
		} else {
			atUserInfos = append(atUserInfos, userInfo)
		}
	}
	now := time.Now().Unix()
	data.Remark = append(data.Remark, &processModel.ProcessRemarkInfo{
		Remark:       remark,
		File:         file,
		AtUser:       atUserInfos,
		VisibleStaff: visibleStaff,
		CreateTime:   now,
	})
	dataStr, err := common.JsonStringEncode(data)
	if err != nil {
		return err
	}
	_, err = op.UpdateUserProcessRuntimeDataByProcessUuid(processTable.Uuid, dataStr)
	//异步通知被圈的人
	go func() {
		notifyOperator := notifyLogic.NewNotifyOperator(op.Ctx())
		workflowName, initiatorName := engine.GetWorkflowNameAndUserName(op.Ctx(), workflowTable)
		for userId, atProcessUuid := range atUserIdProcessUuidMap {
			emailContent, err := engine.GenerateEmailContentByTmpl(workflowName, initiatorName, engine.REMARK_EMAIL_TMPL,
				engine.GetToCProcessUrl(atProcessUuid), map[string]interface{}{
					"remark": remark,
				})
			if err != nil {
				emailContent = engine.GenerateEmailRemarkContent(atProcessUuid)
			}
			errNotify := notifyOperator.SendNotify(userId, notifyLogic.NotifyMsg{
				LarkContent: notifyLogic.GenerateLarkRemarkContent(remark, workflowName, op.Ctx().User().DisplayName, atProcessUuid, now, notifyConfig.LarkCustomTemplate, nodeTable.Data),
				MailTitle:   "流程评论通知",
				MailContent: emailContent,
			}, notifyConfig.Mode)
			if errNotify != nil {
				op.Ctx().Log().Errorln(err)
			}
		}
		if atInitiator {
			emailContent, err := engine.GenerateEmailContentByTmpl(workflowName, initiatorName, engine.REMARK_EMAIL_TMPL,
				engine.GetToCWorkflowUrl(processTable.WorkflowUuid), map[string]interface{}{
					"remark": remark,
				})
			if err != nil {
				emailContent = engine.GenerateEmailRemarkContentByWorkflowUuid(processTable.WorkflowUuid)
			}
			errNotify := notifyOperator.SendNotify(processTable.InitiatorId, notifyLogic.NotifyMsg{
				LarkContent: notifyLogic.GenerateLarkRemarkContentByWorkflowUuid(remark, workflowName, op.Ctx().User().DisplayName, processTable.WorkflowUuid, now, notifyConfig.LarkCustomTemplate, nodeTable.Data),
				MailTitle:   "流程评论通知",
				MailContent: emailContent,
			}, notifyConfig.Mode)
			if errNotify != nil {
				op.Ctx().Log().Errorln(err)
			}
		}
	}()

	return err
}

func (op *ProcessOperator) HasActionPermission(action bpmCom.ProcessActionEnum, allowedActions []bpmCom.ProcessActionEnum) bool {
	for _, allowedAction := range allowedActions {
		if action == allowedAction {
			return true
		}
	}
	return false
}

func (op *ProcessOperator) RollbackProcess(processTable *processModel.ProcessTable,
	comment string,
	nodeUuid string, rollbackNodeName string) error {
	op.Ctx().Log().Infoln("process %s rollback to node %s", processTable.Uuid, nodeUuid)
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return err
	}
	wModel, err := workflow.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	curNodeTable, err := wModel.QueryNodeRuntimeByUuid(processTable.NodeUuid)
	if err != nil {
		return err
	}
	if curNodeTable.Type != bpmCom.NODE_TYPE_APPROVAL &&
		curNodeTable.Type != bpmCom.NODE_TYPE_INTERACT &&
		curNodeTable.Type != bpmCom.NODE_TYPE_TRANSACTOR { //检查节点类型	2020-6-23 增加交互系统的回退功能	2020-8-6 增加办理人节点
		return common.ErrNoPermission
	} else if curNodeTable.Uuid == nodeUuid {
		return bpmCom.ErrRollbackToSelf
	} else if curNodeTable.State != bpmCom.STATE_PENDING {
		return bpmCom.ErrInvalidNodeState
	}
	tarNodeTable, err := wModel.QueryNodeRuntimeByUuid(nodeUuid)
	if err != nil {
		return err
	}
	var isReset bool
	if tarNodeTable.Type == bpmCom.NODE_TYPE_START {
		isReset = true
	} else if tarNodeTable.Type != bpmCom.NODE_TYPE_APPROVAL &&
		tarNodeTable.Type != bpmCom.NODE_TYPE_TRANSACTOR { //检查节点类型
		return common.ErrNoPermission
	}
	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(processTable.WorkflowUuid)
	if err != nil {
		return err
	}
	if workflowTable.State != bpmCom.STATE_PENDING { //检查工作流状态
		return bpmCom.ErrWorkflowState
	}
	//用于操作时加锁
	workflowBase, err := workflow.InitWorkflowBase(op, workflowTable.Uuid)
	if err != nil {
		return err
	}
	if err := workflowBase.Load(); err != nil {
		return err
	} else if err = workflowBase.Continue(); err != nil {
		return err
	}
	if isReset {
		defer func() {
			if err != nil {
				op.Ctx().Log().Errorln("reset workflow failed, pend again, error:", err)
				workflowBase.Pend()
			} else {
				workflowBase.Reset()
			}
		}()
	} else {
		defer workflowBase.Pend()
	}
	var resetTarNodeDataStr string //将当前节点的数据重置回要退回的节点
	var newInput interface{}
	if curNodeTable.Type == bpmCom.NODE_TYPE_APPROVAL {
		//当前节点是审批节点时，查node，校验权限
		var nodeDetail engine.ApprovalTaskDetail
		if err = common.JsonStringDecode(curNodeTable.Detail, &nodeDetail); err != nil {
			return err
		}
		if !op.HasActionPermission(bpmCom.PROCESS_ACTION_ROLLBACK, nodeDetail.AllowedAction) {
			return common.ErrNoPermission
		}
		var curNodeData engine.ApprovalTaskData
		if err = common.JsonStringDecode(curNodeTable.Data, &curNodeData); err != nil {
			return err
		}
		newInput = curNodeData.Input
	} else if curNodeTable.Type == bpmCom.NODE_TYPE_TRANSACTOR {
		//当前节点是办理人节点时，查node，校验权限		2020-8-6添加
		var nodeDetail engine.TransactorTaskDetail
		if err = common.JsonStringDecode(curNodeTable.Detail, &nodeDetail); err != nil {
			return err
		}
		if !op.HasActionPermission(bpmCom.PROCESS_ACTION_ROLLBACK, nodeDetail.AllowedAction) {
			return common.ErrNoPermission
		}
		var curNodeData engine.TransactorTaskData
		if err = common.JsonStringDecode(curNodeTable.Data, &curNodeData); err != nil {
			return err
		}
		newInput = curNodeData.Input
	} else if curNodeTable.Type == bpmCom.NODE_TYPE_INTERACT { //系统节点时的处理
		var curNodeData engine.InteractTaskData
		if err = common.JsonStringDecode(curNodeTable.Data, &curNodeData); err != nil {
			return err
		}
		newInput = curNodeData.Input
	}
	switch tarNodeTable.Type {
	case bpmCom.NODE_TYPE_APPROVAL:
		var tarNodeData engine.ApprovalTaskData
		tarNodeData.Input = newInput
		resetTarNodeDataStr, err = common.JsonStringEncode(tarNodeData)
	case bpmCom.NODE_TYPE_TRANSACTOR:
		var tarNodeData engine.TransactorTaskData
		tarNodeData.Input = newInput
		resetTarNodeDataStr, err = common.JsonStringEncode(tarNodeData)
	case bpmCom.NODE_TYPE_START:
		var tarNodeData engine.StartTaskData
		if err = common.JsonStringDecode(tarNodeTable.Data, &tarNodeData); err != nil {
			return err
		}
		tarNodeData.Input = newInput
		tarNodeData.Output = nil
		resetTarNodeDataStr, err = common.JsonStringEncode(tarNodeData)
	}
	if err != nil {
		return err
	}
	//读取workflow detail
	topo := &engine.WorkflowDetail{}
	err = common.JsonDecode([]byte(workflowTable.Detail), &topo)
	if err != nil {
		return err
	}
	nodesMap := make(map[string]*engine.TopologyDetail)
	//找目标节点
	var tarNode *engine.TopologyDetail
	for _, node := range topo.Nodes {
		nodesMap[node.Uuid] = node
		if node.Uuid == nodeUuid {
			tarNode = node
		}
	}
	if tarNode == nil {
		return common.ErrRecordNotFound
	}
	//往下找到所有需要重置的节点
	var needUpdateNodeUuids []string
	//如果是驳回到发起人，那么所有node都需要重置
	if isReset {
		for _, n := range topo.Nodes {
			needUpdateNodeUuids = append(needUpdateNodeUuids, n.Uuid)
		}
	} else {
		//否则要检察一下是否是一条线
		for n := tarNode; n.Uuid != curNodeTable.Uuid; n = nodesMap[n.Next[0].Uuid] {
			if n == nil || n.Type == bpmCom.NODE_TYPE_END || len(n.Next) == 0 {
				return common.ErrRecordNotFound
			} else if len(n.Next) > 1 {
				return bpmCom.ErrRollbackThroughMultiBranch
			}
			needUpdateNodeUuids = append(needUpdateNodeUuids, n.Uuid)
		}
		needUpdateNodeUuids = append(needUpdateNodeUuids, curNodeTable.Uuid)
	}
	//找到所有node和processTable
	needUpdateNodeTables, err := wModel.QueryNodeRuntimeByUuids(needUpdateNodeUuids)
	if err != nil {
		return err
	}
	needUpdateProcessTables, err := pModel.QueryNeedToResetProcessRuntimeByNodeUuids(needUpdateNodeUuids)
	if err != nil {
		return err
	}
	//统计需要同时通知的人
	needNotifyProcesses := engine.SortStakeholderProcessToNotify(needUpdateProcessTables, tarNodeTable.Uuid, func() []int64 {
		if isReset {
			return []int64{workflowTable.UserId, op.Ctx().User().UserId}
		} else {
			return []int64{op.Ctx().User().UserId}
		}
	}(), false)
	//记录所有节点的通知配置
	notifyConfigMap := make(map[string]notifyLogic.NotifyConfig)
	for _, nodeTable := range needUpdateNodeTables {
		switch nodeTable.Type {
		case bpmCom.NODE_TYPE_APPROVAL:
			d := &engine.ApprovalTaskDetail{}
			common.JsonStringDecode(nodeTable.Detail, &d)
			notifyConfigMap[nodeTable.Uuid] = d.Notify
		case bpmCom.NODE_TYPE_NOTIFY:
			d := &engine.NotifyTaskDetail{}
			common.JsonStringDecode(nodeTable.Detail, &d)
			notifyConfigMap[nodeTable.Uuid] = d.Notify
		case bpmCom.NODE_TYPE_TRANSACTOR:
			d := &engine.TransactorTaskDetail{}
			common.JsonStringDecode(nodeTable.Detail, &d)
			notifyConfigMap[nodeTable.Uuid] = d.Notify
		}
	}
	//插入一条新的process，标识退回状态
	newProcessTable := &processModel.ProcessTable{
		UserId:       processTable.UserId,
		Uuid:         common.NewUuid(common.RESOURCE_TYPE_PROCESS),
		WorkflowUuid: processTable.WorkflowUuid,
		NodeUuid:     processTable.NodeUuid,
		Detail:       processTable.Detail,
		Status:       processTable.Status,
		State:        bpmCom.PROCESS_STATE_ROLLBACKED,
		Type:         processTable.Type,
		Tag:          processTable.Tag,
		Origin:       processTable.Origin,
		InitiatorId:  processTable.InitiatorId,
		Title:        processTable.Title,
		Desc:         processTable.Desc,
		ApproveTime:  time.Now().UnixNano(),
	}
	newProcessTable.CreateTime = processTable.CreateTime
	newProcessTable.UpdateTime = processTable.UpdateTime
	//填入旧Data
	if processTable.Type == bpmCom.PROCESS_TYPE_APPROVAL ||
		processTable.Type == bpmCom.PROCESS_TYPE_TRANSACTOR {
		var d processModel.ApprovalProcessData
		if err = common.JsonStringDecode(processTable.Data, &d); err != nil {
			return err
		}
		d.Comment = comment
		d.RollbackNodeUuid = nodeUuid
		d.RollbackNodeName, _ = common.JsonStringToRaw(tarNodeTable.Name)
		if newProcessTable.Data, err = common.JsonStringEncode(d); err != nil {
			return err
		}
		//2020-06-23 前端告知在加签后的退回情况很难做，因此修改，将退回的process的parentUuid抹去，提到最外层。
		var detail processModel.ApprovalProcessDetail
		if err = common.JsonStringDecode(processTable.Detail, &d); err != nil {
			return err
		}
		detail.Order = 0
		detail.ParentUuid = common.StringEmpty
		detail.SubType = common.StringEmpty
		detail.PredecessorUuid = processTable.Uuid
		if newProcessTable.Detail, err = common.JsonStringEncode(detail); err != nil {
			return err
		}
	} else if processTable.Type == bpmCom.PROCESS_TYPE_INTERACT {
		var d processModel.InteractProcessData
		if err = common.JsonStringDecode(processTable.Data, &d); err != nil {
			return err
		}
		d.RollbackNodeUuid = nodeUuid
		d.RollbackNodeName, _ = common.JsonStringToRaw(tarNodeTable.Name)
		if newProcessTable.Data, err = common.JsonStringEncode(d); err != nil {
			return err
		}
	}
	for idx := range needUpdateNodeTables {
		needUpdateNodeTables[idx] = op.ResetNodeTable(needUpdateNodeTables[idx])
	}
	for idx := range needUpdateProcessTables {
		needUpdateProcessTables[idx] = op.ResetProcessTable(needUpdateProcessTables[idx])
		if needUpdateProcessTables[idx].Uuid == processTable.Uuid { //是本次退回的process，则清空data
			needUpdateProcessTables[idx].Data = common.StringJsonEmpty
		}
	}
	//把当前data重置回目标退回的节点data
	for _, n := range needUpdateNodeTables {
		if n.Uuid == nodeUuid {
			n.Data = resetTarNodeDataStr
		}
	}
	err = wModel.BatchRollbackProcessAndNodeAndWorkflow(needUpdateProcessTables,
		needUpdateNodeTables, newProcessTable)
	if err == nil {
		go func() {
			notifyOperator := notifyLogic.NewNotifyOperator(op.ctx)
			workflowName, initiatorName := engine.GetWorkflowNameAndUserName(op.Ctx(), workflowTable)
			if isReset { //如果退回到发起人，需要通知发起人
				emailContent, err := engine.GenerateEmailContentByTmpl(workflowName, initiatorName,
					engine.ROLLBACK_EMAIL_TMPL, engine.GetToCWorkflowUrl(workflowTable.Uuid), nil)
				if err != nil {
					emailContent = engine.GenerateEmailRollbackedContent(workflowTable.Uuid)
				}
				if errNotify := notifyOperator.SendNotify(workflowTable.UserId, notifyLogic.NotifyMsg{
					LarkContent: notifyLogic.GenerateLarkRollbackContent(workflowName, workflowTable.Uuid, workflowTable.CreateTime, notifyLogic.LarkInteractiveCustomTemplate{}, nil),
					MailTitle:   "流程退回通知",
					MailContent: emailContent,
				}, []bpmCom.NotifyTypeEnum{bpmCom.NotifyTypeLark, bpmCom.NotifyTypeEmail}); errNotify != nil {
					op.Ctx().Log().Errorln(errNotify)
				}
			}
			//给退回途径中的人发消息
			for _, proc := range needNotifyProcesses {
				emailContent, err := engine.GenerateEmailContentByTmpl(workflowName, initiatorName,
					engine.ROLLBACK_NOTIFY_OTHERS_EMAIL_TMPL, engine.GetToCProcessUrl(proc.Uuid), nil)
				if err != nil {
					emailContent = engine.GenerateEmailRollbackNotifyOthersContent(proc.Uuid)
				}
				if errNotify := notifyOperator.SendNotify(proc.UserId, notifyLogic.NotifyMsg{
					LarkContent: notifyLogic.GenerateLarkRollbackNotifyOthersContent(workflowName, initiatorName,
						proc.Uuid, workflowTable.CreateTime, notifyConfigMap[proc.NodeUuid].LarkCustomTemplate, nil),
					MailTitle:   "流程退回通知",
					MailContent: emailContent,
				}, notifyConfigMap[proc.NodeUuid].Mode); errNotify != nil {
					op.Ctx().Log().Errorln(errNotify)
				}
			}
		}()
	}
	return err
}

func (op *ProcessOperator) ResetNodeTable(nodeTable *workflow.NodeTable) *workflow.NodeTable {
	switch nodeTable.Type {
	case bpmCom.NODE_TYPE_INTERACT:
		var d engine.InteractTaskData
		common.JsonStringDecode(nodeTable.Data, &d)
		nodeTable.Data, _ = common.JsonStringEncode(engine.InteractTaskData{ //只留comment
			Comment: d.Comment,
		})
		nodeTable.State = bpmCom.STATE_READY
	default:
		nodeTable.Data = common.StringJsonEmpty
		nodeTable.State = bpmCom.STATE_READY
	}
	return nodeTable
}

func (op *ProcessOperator) ResetProcessTable(processTable *processModel.ProcessTable) *processModel.ProcessTable {
	switch processTable.Type {
	case bpmCom.PROCESS_TYPE_APPROVAL:
		fallthrough
	case bpmCom.PROCESS_TYPE_TRANSACTOR:
		if processTable.State == bpmCom.PROCESS_STATE_HANDOVERED ||
			processTable.State == bpmCom.PROCESS_STATE_ROLLBACKED ||
			processTable.State == bpmCom.PROCESS_STATE_NEED_COMPLETE {
			return processTable
		}
		var d processModel.ApprovalProcessData
		common.JsonStringDecode(processTable.Data, &d)
		d.LastUrgeTime = 0 //重置urge时间和已通知时间
		d.Messaged = false
		processTable.Data, _ = common.JsonStringEncode(d)
		processTable.State = bpmCom.PROCESS_STATE_INITIALIZED
	case bpmCom.PROCESS_TYPE_NOTIFY: //2010-6-11 这里改为不做操作，退回后不影响抄送人查看
		processTable.Data = common.StringJsonEmpty
		processTable.State = bpmCom.PROCESS_STATE_INITIALIZED
	case bpmCom.PROCESS_TYPE_INTERACT:
		processTable.Data = common.StringJsonEmpty
		processTable.State = bpmCom.PROCESS_STATE_INITIALIZED
	}
	return processTable
}

func (op *ProcessOperator) NeedCompleteProcess(processTable *processModel.ProcessTable,
	comment string) (err error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return err
	}
	wModel, err := workflow.NewWorkflowModel(op, false)
	if err != nil {
		return err
	}
	curNodeTable, err := wModel.QueryNodeRuntimeByUuid(processTable.NodeUuid)
	if err != nil {
		return err
	}
	if (curNodeTable.Type != bpmCom.NODE_TYPE_APPROVAL && curNodeTable.Type != bpmCom.NODE_TYPE_TRANSACTOR) ||
		curNodeTable.State != bpmCom.STATE_PENDING { //检查节点类型
		return common.ErrNoPermission
	}
	//查node，校验权限
	if curNodeTable.Type == bpmCom.NODE_TYPE_TRANSACTOR {
		var nodeDetail engine.TransactorTaskDetail
		if err = common.JsonStringDecode(curNodeTable.Detail, &nodeDetail); err != nil {
			return err
		}
		if !op.HasActionPermission(bpmCom.PROCESS_ACTION_NEEDCOMPLETE, nodeDetail.AllowedAction) {
			return common.ErrNoPermission
		}
	} else {
		var nodeDetail engine.ApprovalTaskDetail
		if err = common.JsonStringDecode(curNodeTable.Detail, &nodeDetail); err != nil {
			return err
		}
		if !op.HasActionPermission(bpmCom.PROCESS_ACTION_NEEDCOMPLETE, nodeDetail.AllowedAction) {
			return common.ErrNoPermission
		}
	}
	workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(processTable.WorkflowUuid)
	if err != nil {
		return err
	}
	if workflowTable.State != bpmCom.STATE_PENDING { //检查工作流状态
		return bpmCom.ErrWorkflowState
	}
	//用于操作时加锁
	workflowBase, err := workflow.InitWorkflowBase(op, workflowTable.Uuid)
	if err != nil {
		return err
	}
	if err := workflowBase.Load(); err != nil {
		return err
	} else if err = workflowBase.Continue(); err != nil {
		return err
	}
	defer func() {
		if err != nil {
			op.Ctx().Log().Errorln("need complete failed, pend again, error:", err)
			workflowBase.Pend()
		} else {
			workflowBase.NeedComplete()
		}
	}()
	var needUpdateNodeUuids []string
	//找到所有处于pending状态的approval node和对应的processTable
	needUpdateNodeTables, err := wModel.QueryNodeRuntimeByParentUuidsAndState([]string{workflowTable.Uuid}, bpmCom.STATE_PENDING)
	if err != nil {
		return err
	}
	for _, nodeTable := range needUpdateNodeTables {
		if nodeTable.Type == bpmCom.NODE_TYPE_APPROVAL || nodeTable.Type == bpmCom.NODE_TYPE_TRANSACTOR {
			needUpdateNodeUuids = append(needUpdateNodeUuids, nodeTable.Uuid)
		}
	}
	needUpdateProcessTables, err := pModel.QueryNeedToResetProcessRuntimeByNodeUuids(needUpdateNodeUuids)
	if err != nil {
		return err
	}
	//插入一条新的process，标识退回状态
	newProcessTable := &processModel.ProcessTable{
		UserId:       processTable.UserId,
		Uuid:         common.NewUuid(common.RESOURCE_TYPE_PROCESS),
		WorkflowUuid: processTable.WorkflowUuid,
		NodeUuid:     processTable.NodeUuid,
		Detail:       processTable.Detail,
		Status:       processTable.Status,
		State:        bpmCom.PROCESS_STATE_NEED_COMPLETE,
		Type:         processTable.Type,
		Tag:          processTable.Tag,
		Origin:       processTable.Origin,
		InitiatorId:  processTable.InitiatorId,
		Title:        processTable.Title,
		Desc:         processTable.Desc,
		ApproveTime:  time.Now().UnixNano(),
	}
	newProcessTable.CreateTime = processTable.CreateTime
	newProcessTable.UpdateTime = processTable.UpdateTime
	//填入旧Data
	var d processModel.ApprovalProcessData
	if err = common.JsonStringDecode(processTable.Data, &d); err != nil {
		return err
	}
	d.Comment = comment
	if newProcessTable.Data, err = common.JsonStringEncode(d); err != nil {
		return err
	}
	//与退回的情况一样，将补充资料的process的parentUuid抹去，提到最外层。
	var detail processModel.ApprovalProcessDetail
	if err = common.JsonStringDecode(processTable.Detail, &d); err != nil {
		return err
	}
	detail.Order = 0
	detail.ParentUuid = common.StringEmpty
	detail.SubType = common.StringEmpty
	detail.PredecessorUuid = processTable.Uuid
	if newProcessTable.Detail, err = common.JsonStringEncode(detail); err != nil {
		return err
	}
	for idx := range needUpdateProcessTables {
		needUpdateProcessTables[idx] = op.ResetProcessTable(needUpdateProcessTables[idx]) //复用rollback的逻辑
		if needUpdateProcessTables[idx].Uuid == processTable.Uuid {                       //是本次退回补充资料的process，则清空data
			needUpdateProcessTables[idx].Data = common.StringJsonEmpty
		}
	}
	err = wModel.BatchNeedCompleteProcess(needUpdateProcessTables,
		newProcessTable)
	if err == nil {
		go func() {
			notifyOperator := notifyLogic.NewNotifyOperator(op.ctx)
			workflowName, initiatorName := engine.GetWorkflowNameAndUserName(op.Ctx(), workflowTable)
			emailContent, err := engine.GenerateEmailContentByTmpl(workflowName, initiatorName,
				engine.NEED_COMPLETE_EMAIL_TMPL, engine.GetToCWorkflowUrl(workflowTable.Uuid), nil)
			if err != nil {
				emailContent = engine.GenerateEmailNeedCompleteContent(workflowTable.Uuid)
			}
			if errNotify := notifyOperator.SendNotify(workflowTable.UserId, notifyLogic.NotifyMsg{
				LarkContent: notifyLogic.GenerateLarkNeedCompleteContent(workflowName, workflowTable.Uuid, workflowTable.CreateTime, notifyLogic.LarkInteractiveCustomTemplate{}, nil),
				MailTitle:   "流程补充资料通知",
				MailContent: emailContent,
			}, []bpmCom.NotifyTypeEnum{bpmCom.NotifyTypeLark, bpmCom.NotifyTypeEmail}); errNotify != nil {
				op.Ctx().Log().Errorln(errNotify)
			}
		}()
	}
	return err
}

func (op *ProcessOperator) HandleInteractProcessCallback(processTable *processModel.ProcessTable,
	comment string, pass, rollback bool, data interface{}) (nodeUuid string, err error) {
	nodeUuid = processTable.NodeUuid
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nodeUuid, err
	}
	var pDetail processModel.InteractProcessDetail
	var pData processModel.InteractProcessData
	//先解析process
	if err = common.JsonStringDecode(processTable.Detail, &pDetail); err != nil {
		return nodeUuid, err
	} else if err = common.JsonStringDecode(processTable.Data, &pData); err != nil {
		return nodeUuid, err
	}
	//判断来源是否是同一个系统，以及是否真的需要回调
	if pDetail.AppName != op.Ctx().User().Origin || !pDetail.Callback {
		return nodeUuid, common.ErrNoPermission
	}
	pData.CallbackResult = &processModel.CallbackResult{
		Pass:     pass,
		Rollback: rollback,
		Comment:  comment,
		Data:     data,
	}
	newData, err := common.JsonStringEncode(pData)
	if err != nil {
		return nodeUuid, err
	}
	if rollback { //如果需要退回，则走退回逻辑
		processTable.Data = newData
		wModel, err := workflow.NewWorkflowModel(op, false)
		if err != nil {
			return nodeUuid, err
		}
		workflowTable, err := wModel.QueryWorkflowRuntimeByUuid(processTable.WorkflowUuid)
		if err != nil {
			return nodeUuid, err
		}
		//读取workflow detail
		topo := &engine.WorkflowDetail{}
		err = common.JsonDecode([]byte(workflowTable.Detail), &topo)
		if err != nil {
			return nodeUuid, err
		}
		tarNodeUuid := processTable.NodeUuid //找到需要回退的那个节点
		nodeMap := make(map[string]*engine.TopologyDetail)
		for _, n := range topo.Nodes {
			nodeMap[n.Uuid] = n
		}
		for nodeMap[tarNodeUuid] != nil {
			n := nodeMap[tarNodeUuid]
			if n.Type == bpmCom.NODE_TYPE_APPROVAL ||
				n.Type == bpmCom.NODE_TYPE_START ||
				n.Type == bpmCom.NODE_TYPE_TRANSACTOR { //找到了上一个审批节点 <2020-7-9 临时加了发起人节点
				break
			} else if len(n.Prev) > 1 { //有分叉，肯定不对
				return nodeUuid, bpmCom.ErrRollbackThroughMultiBranch
			} else if len(n.Prev) == 0 { //到头了，也有问题
				return nodeUuid, common.ErrRecordNotFound
			} else {
				tarNodeUuid = n.Prev[0].Uuid //往前倒腾
			}
		}
		if nodeMap[tarNodeUuid] == nil {
			return nodeUuid, common.ErrRecordNotFound
		}
		return tarNodeUuid, op.RollbackProcess(processTable, comment, tarNodeUuid, common.StringEmpty)
	}
	var rowsAffected int64
	if pass {
		rowsAffected, err = pModel.UpdateProcessRuntimeDataAndStateByProcessUuid(processTable.Uuid, newData, bpmCom.PROCESS_STATE_FINISHED)
	} else {
		rowsAffected, err = pModel.UpdateProcessRuntimeDataAndStateByProcessUuid(processTable.Uuid, newData, bpmCom.PROCESS_STATE_FAILED)
	}
	if err == nil && rowsAffected == 0 {
		err = bpmCom.ErrAlreadyDone
	}
	return nodeUuid, err
}

func (op *ProcessOperator) MarkAllNotifyAsRead() error {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return err
	}
	_, err = pModel.UpdateProcessRuntimeByWhereMap(map[string]interface{}{
		"state": bpmCom.PROCESS_STATE_NOTIFIED,
	}, map[string]interface{}{
		"user_id": op.Ctx().User().UserId,
		"type":    bpmCom.PROCESS_TYPE_NOTIFY,
		"state":   bpmCom.PROCESS_STATE_NOT_NOTIFIED,
	})
	return err
}

func (op *ProcessOperator) QueryUserProcessRuntimeByUuid(processUuid string) (*processModel.ProcessTable, error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}
	return pModel.QueryUserProcessRuntimeByUuid(processUuid)
}

func (op *ProcessOperator) QueryProcessRuntimeByUuid(processUuid string) (*processModel.ProcessTable, error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}
	return pModel.QueryProcessByUuid(processUuid)
}

func (op *ProcessOperator) QuerySubProcessRuntimeByParentUuid(nodeUuid, processUuid string) ([]*processModel.ProcessTable, error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return nil, err
	}
	return pModel.QuerySubProcessesRuntimeByParentUuid(nodeUuid, processUuid)
}

func (op *ProcessOperator) UpdateUserProcessRuntimeStateByProcessUuid(processUuid string, state bpmCom.ProcessStateEnum) (int64, error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return 0, err
	}
	return pModel.UpdateProcessRuntimeStateByProcessUuid(processUuid, state)
}

func (op *ProcessOperator) UpdateUserProcessRuntimeDataByProcessUuid(processUuid, dataStr string) (int64, error) {
	pModel, err := processModel.NewProcessModel(op, false)
	if err != nil {
		return 0, err
	}
	return pModel.UpdateProcessRuntimeDataByProcessUuid(processUuid, dataStr)
}
