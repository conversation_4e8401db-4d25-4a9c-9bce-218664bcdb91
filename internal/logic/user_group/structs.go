package user_group

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	baseOp "gitlab.docsl.com/security/bpm/internal/logic/base_operator"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type UserGroupOperator struct {
	*baseOp.BaseOperator
}

func NewUserGroupOperator(hctx common.HContextIface) *UserGroupOperator {
	return &UserGroupOperator{baseOp.NewBaseOperator(hctx)}
}

// fix cycle import
type StaffInfo = bpmCom.StaffInfo

type UserGroupRelationDetail struct {
	Staff []StaffInfo `json:"staff,omitempty"`
}

type UserGroupRelationInfo struct {
	UserId int64                   `json:"userId"`
	Detail UserGroupRelationDetail `json:"detail,omitempty"`
}
