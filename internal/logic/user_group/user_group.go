package user_group

import (
	"gitlab.docsl.com/security/bpm/internal/model/user_group"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

func (op *UserGroupOperator) InsertUserGroupInfo(name, description string, relationDetails []UserGroupRelationInfo) (int64, error) {
	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return 0, err
	}
	id, err := gModel.InsertUserGroupInfo(name, description)
	if err != nil {
		return id, err
	}
	err = gModel.InsertUserGroupRelation(id, assembleDetailStr(relationDetails))

	return id, err
}

func assembleDetailStr(relationDetails []UserGroupRelationInfo) (userIdDetailMap map[int64]string) {
	if relationDetails == nil {
		return
	}
	userIdDetailMap = make(map[int64]string)
	for _, d := range relationDetails {
		if s, err := common.JsonStringEncode(d.Detail); err == nil {
			userIdDetailMap[d.UserId] = s
		}
	}
	return
}

func (op *UserGroupOperator) DeleteUserGroupInfo(userGroupIds []int64) ([]int64, error) {
	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return nil, err
	}

	users, err := gModel.SoftDeleteUserGroupInfoById(userGroupIds)

	return users, err
}

func (op *UserGroupOperator) UpdateUserGroupInfo(userGroupId int64, name, desc *string, userRelationDetails []UserGroupRelationInfo) (int64, error) {
	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return 0, err
	}
	userIdDetailMap := assembleDetailStr(userRelationDetails)
	//更新用户组中的成员
	if userIdDetailMap != nil {
		existUserIds, err := gModel.QueryUsersByGroupId(userGroupId)
		if err != nil {
			return 0, err
		}
		err = gModel.SoftDeleteUserFromGroup(userGroupId, existUserIds)
		if err != nil {
			return 0, err
		}
		err = gModel.InsertUserGroupRelation(userGroupId, userIdDetailMap)
		if err != nil {
			return 0, err
		}
	}
	updateMap := make(map[string]interface{})
	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["description"] = *desc
	}

	rowsAffected, err := gModel.UpdateUserGroupInfoById(userGroupId, updateMap)

	return rowsAffected, err
}

func (op *UserGroupOperator) QueryUserGroupInfoByNameOrUserId(userGroupName string, userId int64, offset, limit int) (
	tbs []user_group.UserGroupInfoTable, cnt int64, err error) {

	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return nil, 0, err
	}

	var userGroupIds []int64
	if userId != 0 {
		userGroupIds, err = gModel.QueryUserGroupIdsByUserId(userId)
		if len(userGroupIds) == 0 || err != nil {
			return nil, 0, err
		}
	}

	userGroupInfos, count, err := gModel.QueryUserGroupInfoByNameAndUserGroupIds(userGroupName, nil, offset, limit)
	if err != nil {
		return nil, 0, err
	}

	return userGroupInfos, count, err
}

func (op *UserGroupOperator) QueryUserGroupInfoById(userGroupId int64) (*user_group.UserGroupInfoTable, error) {
	pModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return nil, err
	}
	return pModel.QueryUserGroupInfoById(userGroupId)
}

func (op *UserGroupOperator) AddUserToGroup(userGroupId int64, relations []UserGroupRelationInfo) error {
	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return err
	}
	return gModel.InsertUserGroupRelation(userGroupId, assembleDetailStr(relations))
}

func (op *UserGroupOperator) SoftDeleteUserFromGroup(userGroupId int64, userIds []int64) error {
	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return err
	}
	return gModel.SoftDeleteUserFromGroup(userGroupId, userIds)
}

// 2020-8-19 由于角色中增加了依据发起人所在部门来动态选择审批人的条件，这里校验角色中的条件来动态决定返回的用户
func (op *UserGroupOperator) QueryUserGroupByGroupId(groupId int64) ([]user_group.UserGroupRelationTable, error) {
	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return nil, err
	}
	relations, err := gModel.QueryUserGroupRelationsByGroupId(groupId)
	if err != nil {
		return nil, err
	}
	return relations, nil
}

func (op *UserGroupOperator) QueryUserGroupRelationsByGroupId(groupId int64) ([]user_group.UserGroupRelationTable, error) {
	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return nil, err
	}
	return gModel.QueryUserGroupRelationsByGroupId(groupId)
}

func (op *UserGroupOperator) QueryUserGroupIdsByUserId(userId int64) ([]int64, error) {
	gModel, err := user_group.NewUserGroupModel(op, false)
	if err != nil {
		return nil, err
	}
	return gModel.QueryUserGroupIdsByUserId(userId)
}
