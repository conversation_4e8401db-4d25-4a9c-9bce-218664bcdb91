/**
 * @note
 * 通知相关操作
 *
 * <AUTHOR>
 * @date 	2019-11-19
 */
package notify

import (
	"context"
	"fmt"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/email"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"gitlab.docsl.com/security/bpm/pkg/model/staff"
	"gitlab.docsl.com/security/common/redis"
)

func (op *NotifyOperator) SendNotify(userId int64, info NotifyMsg, modes []bpmCom.NotifyTypeEnum) error {
	user, err := staff.GetUserByUserID(op.ctx.GinCtx(), userId)
	if err != nil {
		return err
	}
	for _, mode := range modes {
		switch mode {
		// TODO CHANGE TO LARK
		case bpmCom.NotifyTypeLark:
			go func() {
				if info.LarkContent.UniqID == common.StringEmpty {
					info.LarkContent.UniqID = common.NewUuid(common.RESOURCE_TYPE_MESSAGE)
				}
				larkMsgID, err := op.SendLarkNotify(op.ctx.GinCtx(), user.LarkUserID, common.StringEmpty, info.LarkContent.UniqID, info.LarkContent)
				if err != nil {
					op.ctx.Log().Errorf("failed to send notify to user [%s], mode %s", user.LarkName, bpmCom.NotifyTypeLark)
				}
				if info.LarkContent.NeedRecord {
					redisCli, err := redis.DefaultClient(op.ctx.GinCtx()) // 用redis记录msgID以供后面更新
					redisCli.Printlog(true)
					err = redisCli.Set(fmt.Sprintf(larkMsgIDKeyFormat, info.LarkContent.UniqID), larkMsgID, larkMsgIDExpireTime)
					if err != nil {
						op.ctx.Log().Errorf("failed to record lark message id %s by unique id %s", larkMsgID, info.LarkContent.UniqID)
					}
				}
			}()
		case bpmCom.NotifyTypeEmail:
			go func() {
				_ = email.SendEmail(op.ctx, user.Email, info.MailTitle, info.MailContent)
			}()
		}
	}
	return nil
}

func (op *NotifyOperator) SendLarkNotify(ctx context.Context, larkUserID, larkChatID string, uniqID string, msg LarkMsg) (msgID string, err error) {
	cli := lark.GetLarkClient()
	if larkChatID != common.StringEmpty {
		switch msg.MsgType {
		case larkim.MsgTypeInteractive:
			return lark.SendLarkMessageByChatID(ctx, cli, larkChatID, uniqID, larkim.MsgTypeInteractive, msg.Interactive.GetContent())
		default:
			return lark.SendLarkMessageByChatID(ctx, cli, larkChatID, uniqID, larkim.MsgTypeText, fmt.Sprintf(`{"text":"%s"}`, msg.Text))
		}
	}
	switch msg.MsgType {
	case larkim.MsgTypeInteractive:
		return lark.SendLarkMessageByUserID(ctx, cli, larkUserID, uniqID, larkim.MsgTypeInteractive, msg.Interactive.GetContent())
	default:
		return lark.SendLarkMessageByUserID(ctx, cli, larkUserID, uniqID, larkim.MsgTypeText, fmt.Sprintf(`{"text":"%s"}`, msg.Text))
	}
}

func (op *NotifyOperator) UpdateLarkNotify(ctx context.Context, msgID string, msg LarkMsg) error {
	cli := lark.GetLarkClient()
	switch msg.MsgType {
	case larkim.MsgTypeInteractive:
		return lark.PatchLarkMessageByMsgID(ctx, cli, msgID, msg.Interactive.GetContent())
	default:
		_, err := lark.UpdateLarkMessageByMsgID(ctx, cli, msgID, larkim.MsgTypeText, fmt.Sprintf(`{"text":"%s"}`, msg.Text))
		return err
	}
}
