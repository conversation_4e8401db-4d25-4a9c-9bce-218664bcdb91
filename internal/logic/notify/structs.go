/**
 * @note
 * 通知相关结构体
 *
 * <AUTHOR>
 * @date 	2019-11-19
 */
package notify

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"time"
)

const (
	larkMsgIDKeyFormat  = "bpm_lark_msg_id_%s"
	larkMsgIDExpireTime = time.Hour * 14 * 24 // 14天更新时间

	ApprovalCardID = "ctp_AAzmJwoZEdhJ" // 审批卡片
	HandleCardID   = "ctp_AAzmFm7u1ltE" // 办理卡片
	NotifyCardID   = "ctp_AAzmJwoZEhOF"

	larkCardTitleKey   = "larkCardTitle"
	larkCardContentKey = "larkCardContent"
	processUuidKey     = "processUuid"
	workflowUuidKey    = "workflowUuid"
	bpmEnvKey          = "bpmEnv"
)

type NotifyOperator struct {
	ctx common.HContextIface
}

func NewNotifyOperator(ctx common.HContextIface) *NotifyOperator {
	return &NotifyOperator{ctx: ctx}
}

type LarkInteractiveCustomTemplate struct {
	TemplateID        string            `json:"templateID"`
	VariablesFromData map[string]string `json:"variablesFromData"` // 需要从表单中注入的变量,key为渲染后的path，v为原data中的path
	AfterTemplateID   string            `json:"afterTemplateID"`
}

type NotifyConfig struct {
	Mode               []bpmCom.NotifyTypeEnum       `json:"mode"`
	LarkCustomTemplate LarkInteractiveCustomTemplate `json:"larkCustomTemplate"` // 如果不为空，则使用自定义的卡片
}

type NotifyMsg struct {
	LarkContent LarkMsg `json:"larkContent"` // lark content
	SmsContent  SmsMsg  `json:"smsContent"`  // 短信content
	MailTitle   string  `json:"mailTitle"`   // 邮件title
	MailContent string  `json:"mailContent"` // 邮件content
}

type MailMsg struct { // TODO
	Title   string `json:"title"`
	Content string `json:"content"`
}

type SmsMsg struct { // TODO
}

type LarkMsg struct {
	MsgType     string              `json:"msgType"`
	Interactive LarkInteractiveData `json:"interactive"`
	Text        string              `json:"text"`
	NeedRecord  bool                `json:"needRecord"`
	UniqID      string              `json:"uniqID"`
}

// 目前只支持用templateID发送
type LarkInteractiveData struct {
	TemplateID       string                 `json:"template_id"`
	TemplateVariable map[string]interface{} `json:"template_variable"`
}

type LarkInteractiveContent struct {
	Type string              `json:"type"`
	Data LarkInteractiveData `json:"data"`
}

func (d *LarkInteractiveData) GetContent() string {
	content, _ := common.JsonStringEncode(LarkInteractiveContent{
		Type: "template",
		Data: *d,
	})
	return content
}
