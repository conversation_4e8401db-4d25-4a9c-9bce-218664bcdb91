/**
 * @note
 * notify_test
 *
 * <AUTHOR>
 * @date 	2025-08-01
 */
package notify

import (
	"context"
	"fmt"
	. "github.com/bytedance/mockey"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/tmsong/hlog"
	common2 "gitlab.docsl.com/security/bpm/pkg/common"
	conf "gitlab.docsl.com/security/bpm/pkg/config"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
	"os"
	"strconv"
	"testing"
)

var (
	log  *hlog.Logger
	ctx  context.Context
	hctx common2.HContextIface
)

func init() {
	masker.SetGetKeyFunc(func(arg string) []byte {
		return []byte(os.Getenv("BPM_CONFIG_CRYPT_KEY"))
	})
	log = hlog.NewLoggerWithConfig(logger.GetHlogConfig(), 0)
	hctx = common2.NewContext(log, nil)
	ctx = hctx.GinCtx()
	// 尝试加载配置文件，先尝试相对路径，如果失败则尝试绝对路径
	_, err := conf.ReplaceAndLoad("../../../conf/dev.toml", true)
	if err != nil {
		panic(err)
	}
}

func Test_SendLarkMessageByUserID(t *testing.T) {
	PatchConvey("Test_SendLarkMessageByUserID", t, func() {
		PatchConvey("case1 SendLarkMessageByUserID", func() {
			cli := lark.GetLarkClient()
			messageID, err := lark.SendLarkMessageByUserID(ctx, cli, "3f4cadfb", strconv.FormatInt(common.GenerateId(), 10), "text", `{"text":"你好"}`)
			So(err, ShouldBeNil)
			fmt.Println(messageID)
			So(messageID, ShouldNotBeEmpty)
		})
	})
}

func Test_SendLarkInteractiveMessageByUserID(t *testing.T) {
	PatchConvey("Test_SendLarkInteractiveMessageByUserID", t, func() {
		PatchConvey("case1 SendLarkInteractiveMessageByUserID", func() {
			op := NewNotifyOperator(hctx)
			msgID, err := op.SendLarkNotify(ctx, "", "oc_da77fb4adee0fe7a7e30b63fe5686311", fmt.Sprintf("%d", common.GenerateId()), LarkMsg{
				MsgType: larkim.MsgTypeInteractive,
				Interactive: LarkInteractiveData{
					TemplateID: "ctp_AAzmJwoZEdhJ",
					TemplateVariable: map[string]interface{}{
						"larkCardTitle":   "审批通知",
						"larkCardContent": "**您有一个流程需要审批**\n**流程名称**:  123\n**发起人**: 我\n**发起时间**:  123\n[查看详情](https://bpm.sec-test.yorkapp.com)",
						"processUuid":     "12345",
					},
				},
			})
			So(err, ShouldBeNil)
			fmt.Println(msgID)
			So(msgID, ShouldNotBeEmpty)
		})
	})
}

func Test_UpdateLarkInteractiveMessageByUserID(t *testing.T) {
	PatchConvey("Test_SendLarkInteractiveMessageByUserID", t, func() {
		PatchConvey("case1 SendLarkInteractiveMessageByUserID", func() {
			op := NewNotifyOperator(hctx)
			err := op.UpdateLarkNotify(ctx, "om_x100b463d3212b4a00d1912fd2c58f7b", LarkMsg{
				MsgType: larkim.MsgTypeInteractive,
				Interactive: LarkInteractiveData{
					TemplateID: "ctp_AAzmJwoZEhOF",
					TemplateVariable: map[string]interface{}{
						"larkCardTitle":   "您有一个流程已经审批完成",
						"larkCardContent": "**流程名称**:  123\n**发起人**: 我\n**发起时间**:  123\n[查看详情](https://bpm.sec-test.yorkapp.com)",
						"processUuid":     "12345",
					},
				},
			})
			So(err, ShouldBeNil)
		})
	})
}

func Test_UpdateLarkMessageByMsgID(t *testing.T) {
	PatchConvey("Test_UpdateLarkMessageByMsgID", t, func() {
		PatchConvey("case1 UpdateLarkMessageByMsgID", func() {
			cli := lark.GetLarkClient()
			messageID, err := lark.UpdateLarkMessageByMsgID(ctx, cli, "om_x100b4636ebe08ca00d20235af446b44", "text", `{"text":"不好"}`)
			So(err, ShouldBeNil)
			fmt.Println(messageID)
			So(messageID, ShouldNotBeEmpty)
		})
	})
}

func Test_WithdrawLarkMessageByMsgID(t *testing.T) {
	PatchConvey("Test_WithdrawLarkMessageByMsgID", t, func() {
		PatchConvey("case1 WithdrawLarkMessageByMsgID", func() {
			cli := lark.GetLarkClient()
			err := lark.WithdrawMessageByMsgID(ctx, cli, "om_x100b4636ebe08ca00d20235af446b44")
			So(err, ShouldBeNil)
		})
	})
}
