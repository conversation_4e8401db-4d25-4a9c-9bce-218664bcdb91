/**
 * @note
 * generate
 *
 * <AUTHOR>
 * @date 	2025-08-04
 */
package notify

import (
	"fmt"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	pv "github.com/tmsong/utils/pathvalue"
	interCommon "gitlab.docsl.com/security/bpm/internal/common"
	bpmCom "gitlab.docsl.com/security/bpm/pkg/common"

	"gitlab.docsl.com/security/common"
	"strconv"
	"time"
)

func getTimeFormatByStamp(timeStamp int64) string {
	return time.Unix(timeStamp, 0).In(common.LocalLocation).Format("2006-01-02 15:04:05")
}

func GetToCProcessUrl(processUuid string) string {
	return interCommon.GetConfig().ToCAddr + "business/approval-form/edit/" + processUuid +
		"?timestamp=" + strconv.FormatInt(time.Now().Unix(), 10)
}

func GetToCWorkflowUrl(workflowUuid string) string {
	return interCommon.GetConfig().ToCAddr + "business/approval-form/initiate/" + workflowUuid +
		"?timestamp=" + strconv.FormatInt(time.Now().Unix(), 10)
}

// after：代表是否取template的afterTemplateID
func generateLarkMsg(defaultCardID, defaultTitle, defaultContent string, defaultData map[string]interface{},
	customTemplate LarkInteractiveCustomTemplate, after bool, customData interface{}, needRecord bool, uniqID string) LarkMsg {
	content := LarkInteractiveData{
		TemplateID:       defaultCardID,
		TemplateVariable: make(map[string]interface{}),
	}
	if (customTemplate.TemplateID != common.StringEmpty && !after) || (customTemplate.AfterTemplateID != common.StringEmpty && after) {
		if after {
			content.TemplateID = customTemplate.AfterTemplateID
		} else {
			content.TemplateID = customTemplate.TemplateID // 使用自定义卡片
		}
		if !common.IsNil(customData) {
			m := make(map[string]interface{})
			if customDataStr, ok := customData.(string); ok {
				_ = common.JsonStringDecode(customDataStr, &m) // 把节点string数据塞进去
			} else {
				_ = common.InterfaceToStruct(customData, &m) // 把节点数据塞进去
			}
			for target, source := range customTemplate.VariablesFromData { // 将自定义的变量塞进去
				if v, ok := pv.GetPathValue(m, source); ok {
					_ = pv.SetPathValue(content.TemplateVariable, target, v)
				}
			}
		}
	} else { // 系统默认卡片的变量塞入
		content.TemplateVariable[larkCardTitleKey] = defaultTitle
		content.TemplateVariable[larkCardContentKey] = defaultContent
	}
	for k, v := range defaultData {
		content.TemplateVariable[k] = v
	}
	content.TemplateVariable[bpmEnvKey] = bpmCom.GetEnv()
	return LarkMsg{
		MsgType:     larkim.MsgTypeInteractive,
		Interactive: content,
		NeedRecord:  needRecord,
		UniqID:      uniqID,
	}
}

func GenerateLarkNotifyContent(workflowName, initiatorName, processUuid string, createTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "抄送通知", fmt.Sprintf("**您有一个待查看的流程**\n**流程名称**: %s\n**发起人**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		initiatorName,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, fmt.Sprintf("%s_notify", processUuid))
}

// GenerateLarkApprovalDoneContent 用于审批后的卡片更新
func GenerateLarkApprovalDoneContent(workflowName, initiatorName, processUuid, operator, action string, createTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "审批通知", fmt.Sprintf("**您有一个待审批的流程**\n**流程名称**: %s\n**发起人**: %s\n**发起时间**: %s\n[查看详情](%s)\n已被%s%s",
		workflowName,
		initiatorName,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid),
		operator,
		action),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

// GenerateLarkHandleDoneContent 用于办理后的卡片更新
func GenerateLarkHandleDoneContent(workflowName, initiatorName, processUuid, operator string, createTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "办理通知", fmt.Sprintf("**您有一个待办理的流程**\n**流程名称**: %s\n**发起人**: %s\n**发起时间**: %s\n[查看详情](%s)\n已被%s办理",
		workflowName,
		initiatorName,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid),
		operator),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkApprovalContent(workflowName, initiatorName, processUuid string, createTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(ApprovalCardID, "审批通知", fmt.Sprintf("**您有一个待审批的流程**\n**流程名称**: %s\n**发起人**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		initiatorName,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, true, fmt.Sprintf("%s_approval", processUuid))
}

func GenerateLarkHandleContent(workflowName, initiatorName, processUuid string, createTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(HandleCardID, "办理通知", fmt.Sprintf("**您有一个待办理的流程**\n**流程名称**: %s\n**发起人**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		initiatorName,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, true, fmt.Sprintf("%s_handle", processUuid))
}

func GenerateLarkRemarkContent(remark, workflowName, remarkerName, processUuid string, remarkTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "评论通知", fmt.Sprintf("**%s 审批流程有一条评论提醒到您**\n**评论人**: %s\n**评论内容**: %s\n**评论时间**: %s\n[查看详情](%s)",
		workflowName,
		remarkerName,
		remark,
		getTimeFormatByStamp(remarkTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkRemarkContentByWorkflowUuid(remark, workflowName, remarkerName, workflowUuid string, remarkTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "评论通知", fmt.Sprintf("**%s 审批流程有一条评论提醒到您**\n**评论人**: %s\n**评论内容**: %s\n**评论时间**: %s\n[查看详情](%s)",
		workflowName,
		remarkerName,
		remark,
		getTimeFormatByStamp(remarkTime),
		GetToCWorkflowUrl(workflowUuid)),
		map[string]interface{}{
			workflowUuidKey: workflowUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkUrgeContent(workflowName, initiatorName, processUuid, action string, urgeTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "任务催办通知", fmt.Sprintf("**您有一个待%s的流程**\n**流程名称**: %s\n**催办人**: %s\n**催办时间**: %s\n[查看详情](%s)",
		action,
		workflowName,
		initiatorName,
		getTimeFormatByStamp(urgeTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkFinishedContent(workflowName, workflowUuid string, createTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "流程完成通知", fmt.Sprintf("**您有一个流程已完成**\n**流程名称**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		getTimeFormatByStamp(createTime),
		GetToCWorkflowUrl(workflowUuid)),
		map[string]interface{}{
			workflowUuidKey: workflowUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkFailedContent(workflowName, workflowUuid string, createTime int64,
	customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "流程失败通知", fmt.Sprintf("**您有一个流程已失败**\n**流程名称**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		getTimeFormatByStamp(createTime),
		GetToCWorkflowUrl(workflowUuid)),
		map[string]interface{}{
			workflowUuidKey: workflowUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkResultNotifyOthersContent(workflowName, initiatorName, processUuid, action, result string, createTime int64, customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "流程结果通知", fmt.Sprintf("**您有一条%s的流程已结束**\n**流程名称**: %s\n**发起人**: %s\n**流程结果**: %s\n**发起时间**: %s\n[查看详情](%s)",
		action,
		workflowName,
		initiatorName,
		result,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkRollbackContent(workflowName, workflowUuid string, createTime int64, customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "流程退回通知", fmt.Sprintf("**您有一个流程已被退回**\n**流程名称**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		getTimeFormatByStamp(createTime),
		GetToCWorkflowUrl(workflowUuid)),
		map[string]interface{}{
			workflowUuidKey: workflowUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkNeedCompleteContent(workflowName, workflowUuid string, createTime int64, customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "流程补充资料通知", fmt.Sprintf("**您有一个流程需要补充资料**\n**流程名称**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		getTimeFormatByStamp(createTime),
		GetToCWorkflowUrl(workflowUuid)),
		map[string]interface{}{
			workflowUuidKey: workflowUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkNotifyOtherApproverContent(workflowName, initiatorName, processUuid string, createTime int64, customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "流程被他人处理通知", fmt.Sprintf("**您有一条审批流程已被他人处理**\n**流程名称**: %s\n**发起人**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		initiatorName,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkRollbackNotifyOthersContent(workflowName, initiatorName, processUuid string, createTime int64, customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "流程退回通知", fmt.Sprintf("**您有一条已处理的流程被退回**\n**流程名称**: %s\n**发起人**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		initiatorName,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}

func GenerateLarkWithdrawNotifyOthersContent(workflowName, initiatorName, processUuid string, createTime int64, customTemplate LarkInteractiveCustomTemplate, data interface{}) LarkMsg {
	return generateLarkMsg(NotifyCardID, "流程撤回通知", fmt.Sprintf("**您有一条已处理的流程被撤回**\n**流程名称**: %s\n**发起人**: %s\n**发起时间**: %s\n[查看详情](%s)",
		workflowName,
		initiatorName,
		getTimeFormatByStamp(createTime),
		GetToCProcessUrl(processUuid)),
		map[string]interface{}{
			processUuidKey: processUuid,
		}, customTemplate, false, data, false, common.StringEmpty)
}
