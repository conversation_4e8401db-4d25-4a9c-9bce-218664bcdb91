/**
 * @note
 * 应用appId和appKey相关操作
 *
 * <AUTHOR>
 * @date 	2019-12-23
 */
package app_auth

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	authAppModel "gitlab.docsl.com/security/bpm/internal/model/auth_app"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

func (op *AuthAppOperator) InsertAuthAppInfo(appName, desc string, ssoAppId int64) (string, string, error) {
	aModel, err := authAppModel.NewAuthAppModel(op, false)
	if err != nil {
		return common.StringEmpty, common.StringEmpty, err
	}
	appId := common.RandAuthStr(20)
	appKey := common.RandAuthStr(40)
	return aModel.InsertAuthAppRecord(op.Ctx().User().UserId, ssoAppId, appName, desc, appId, appKey)
}

func (op *AuthAppOperator) DeleteAuthAppInfo(appIds []string) (int64, error) {
	aModel, err := authAppModel.NewAuthAppModel(op, false)
	if err != nil {
		return 0, err
	}
	return aModel.SoftDeleteAuthAppRecordByAppId(appIds)
}

func (op *AuthAppOperator) UpdateAuthApp(appId, appName, desc string) (int64, error) {
	aModel, err := authAppModel.NewAuthAppModel(op, false)
	if err != nil {
		return 0, err
	}
	return aModel.UpdateAuthAppRecordByAppId(appId, appName, desc)
}

func (op *AuthAppOperator) QueryAppKeyByAppId(appId string) (authItem *authAppModel.AuthAppTable, err error) {
	aModel, err := authAppModel.NewAuthAppModel(op, false)
	if err != nil {
		return nil, err
	}
	authAppRecord, err := aModel.QueryAuthAppRecordByAppId(appId)
	if err != nil {
		return nil, err
	}
	return authAppRecord, nil
}

func (op *AuthAppOperator) QueryAuthInfoByAppName(appName string) (authItem *authAppModel.AuthAppTable, err error) {
	aModel, err := authAppModel.NewAuthAppModel(op, false)
	if err != nil {
		return nil, err
	}
	authAppRecord, err := aModel.QueryAuthAppRecordByAppName(appName)
	if err != nil {
		return nil, err
	}
	return authAppRecord, nil
}

func (op *AuthAppOperator) ListAuthAppInfo(condition ListAppAuthCondition) (items []*AppAuthInfo, err error) {
	aModel, err := authAppModel.NewAuthAppModel(op, false)
	if err != nil {
		return nil, err
	}
	whereMap := make(map[string]interface{})
	whereMap["status"] = bpmCom.STATUS_NORMAL
	if len(condition.AppName) > 0 {
		whereMap["app_name"] = condition.AppName
	}
	if len(condition.AppId) > 0 {
		whereMap["app_id"] = condition.AppId
	}

	authAppTables, err := aModel.ListAuthAppRecordByCondition(whereMap)
	if err != nil {
		return nil, err
	}
	for _, authAppTable := range authAppTables {
		item := &AppAuthInfo{
			AppName:     authAppTable.AppName,
			Description: authAppTable.Description,
			AppId:       authAppTable.AppId,
			AppKey:      authAppTable.AppKey,
			CreateTime:  authAppTable.CreateTime,
			UpdateTime:  authAppTable.UpdateTime,
		}
		items = append(items, item)
	}
	return items, nil
}
