/**
 * @note
 * app认证相关结构体
 *
 * <AUTHOR>
 * @date 	2019-12-23
 */
package app_auth

import (
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type AuthAppOperator struct {
	ctx common.HContextIface
}

func (op *AuthAppOperator) Ctx() common.HContextIface {
	return op.ctx
}

func NewAuthAppOperator(ctx common.HContextIface) *AuthAppOperator {
	return &AuthAppOperator{ctx: ctx}
}

type AppAuthInfo struct {
	AppName     string `json:"appName"`
	Description string `json:"description"`
	AppId       string `json:"appId"`
	AppKey      string `json:"appKey"`
	CreateTime  int64  `json:"createTime"`
	UpdateTime  int64  `json:"updateTime"`
}

type ListAppAuthCondition struct {
	AppName string `json:"appName"`
	AppId   string `json:"appId"`
}
