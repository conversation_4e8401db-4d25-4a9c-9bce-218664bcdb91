package workflow_tag

import (
	"github.com/jianfengye/collection"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/internal/model/tag"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
)

func (op *WorkflowTagOperator) InsertTag(tagName string) (int64, error) {
	m, err := workflow_tag.NewWorkflowTagModel(op, false)
	if err != nil {
		return 0, err
	}
	return m.InsertWorkflowTag(tagName, op.Ctx().User().DisplayName)
}

func (op *WorkflowTagOperator) DeleteTag(tagId int64) error {
	m, err := workflow_tag.NewWorkflowTagModel(op, false)
	if err != nil {
		return err
	}

	wModel, err := workflowModel.NewWorkflowModel(op, false)
	if err != nil {
		return nil
	}

	if err := m.SoftDeleteWorkflowTag(tagId); err == nil {
		templates, err := wModel.QueryTemplateByTags([]int64{tagId}, false)
		if err != nil {
			return err
		}

		for _, template := range templates {

			tags := collection.NewInt64Collection(template.Tags)

			newTags, _ := tags.Unique().Filter(func(item interface{}, key int) bool {
				return item.(int64) != tagId
			}).ToInt64s()

			if err := wModel.EditWorkflowTemplateTag(map[string]interface{}{
				"id": template.ID,
			}, newTags); err != nil {
				return err
			}
		}

		return nil
	} else {
		return err
	}
}

func (op *WorkflowTagOperator) UpdateTag(tagId int64, tagName *string) (int64, error) {
	m, err := workflow_tag.NewWorkflowTagModel(op, false)
	if err != nil {
		return 0, err
	}

	if _, err := m.QueryWorkflowTag(map[string]interface{}{
		"id":     tagId,
		"status": bpmCom.STATUS_NORMAL,
	}); err != nil {
		return 0, err
	}

	return m.UpdateWorkflowTagById(tagId, map[string]interface{}{
		"tag_name": *tagName,
	})
}

func (op *WorkflowTagOperator) QueryList() ([]workflow_tag.WorkflowTagTable, int64, error) {
	m, err := workflow_tag.NewWorkflowTagModel(op, false)
	if err != nil {
		return nil, 0, err
	}
	return m.QueryList(map[string]interface{}{
		"status": bpmCom.STATUS_NORMAL,
	})
}
