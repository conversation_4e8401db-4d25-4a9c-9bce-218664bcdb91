/**
 * @note
 * 表单相关解/读/写等操作
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */
package form

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

/* @note
 * 根据前端传过来的form配置，返回所配置的key(field_key) : map[workflowTemplateUuid]bool 的组合Map（关联审批单）
 */
func (op *FormOperator) ExtractRelatedTemplateUuidsFromForm(form interface{}) (
	relatedWorkflowKeys []string, keyRelatedTemplateUuidMap map[string]map[string]bool,
	relatedWorkflowFieldKeys []string, keyRelatedTemplateUuidFieldMap map[string]map[string]bool) {
	var content FormContent
	err := common.InterfaceToStruct(form, &content)
	if err != nil {
		return nil, nil, nil, nil
	}
	keyRelatedTemplateUuidMap, keyRelatedTemplateUuidFieldMap = make(map[string]map[string]bool), make(map[string]map[string]bool)
	for _, field := range content.Fields {
		if field.Type == FORM_FIELD_TYPE_RELATE_WORKFLOW {
			var relateWorkflowOptions RelateWorkflowTemplateOptions
			err = common.InterfaceToStruct(field.Options, &relateWorkflowOptions)
			if err != nil {
				continue
			}
			for _, data := range relateWorkflowOptions.Data {
				if data.WorkflowTemplateUuid != common.StringEmpty {
					if keyRelatedTemplateUuidMap[field.Code] == nil {
						keyRelatedTemplateUuidMap[field.Code] = make(map[string]bool)
					}
					keyRelatedTemplateUuidMap[field.Code][data.WorkflowTemplateUuid] = true
				}
			}
		} else if field.Type == FORM_FIELD_TYPE_RELATE_WORKFLOW_FIELD {
			var relateWorkflowFieldOptions RelateWorkflowTemplateFieldOptions
			err = common.InterfaceToStruct(field.Options, &relateWorkflowFieldOptions)
			if err != nil {
				continue
			}
			for _, data := range relateWorkflowFieldOptions.Data {
				if data.WorkflowTemplateUuid != common.StringEmpty {
					if keyRelatedTemplateUuidFieldMap[field.Code] == nil {
						keyRelatedTemplateUuidFieldMap[field.Code] = make(map[string]bool)
					}
					keyRelatedTemplateUuidFieldMap[field.Code][data.WorkflowTemplateUuid] = true
				}
			}
		}
	}
	for key := range keyRelatedTemplateUuidMap {
		relatedWorkflowKeys = append(relatedWorkflowKeys, key)
	}
	for key := range keyRelatedTemplateUuidFieldMap {
		relatedWorkflowFieldKeys = append(relatedWorkflowFieldKeys, key)
	}
	return
}

/* @note
 * 提取所有的子组件和子表单列表以及其配置
 * params
 * formListKeys 所有formList组件的key
 * formListKeyFormContentMap 每个formList组件的key : 每个formList组件的form配置
 * childListKeys 所有childlist组件的key
 * childListKeyFormContentMap 每个childList组件的key : 每个childList组件的form配置
 */
func (op *FormOperator) ExtractFormListAndChildListFromForm(form interface{}) (
	formListKeys []string, formListKeyFormContentMap map[string]interface{},
	childListKeys []string, childListKeyFormContentMap map[string]interface{}) {
	var content FormContent
	err := common.InterfaceToStruct(form, &content)
	if err != nil {
		return nil, nil, nil, nil
	}
	formListKeyFormContentMap, childListKeyFormContentMap = make(map[string]interface{}), make(map[string]interface{})
	for _, field := range content.Fields {
		if field.Type == FORM_FIELD_TYPE_FORM_LIST {
			var formListOptions FormListOptions
			err = common.InterfaceToStruct(field.Options, &formListOptions)
			if err != nil {
				continue
			}
			formListKeys = append(formListKeys, field.Code)
			formListKeyFormContentMap[field.Code] = formListOptions.Form
		} else if field.Type == FORM_FIELD_TYPE_CHILD_LIST {
			var childListOptions ChildListOptions
			err = common.InterfaceToStruct(field.Options, &childListOptions)
			if err != nil {
				continue
			}
			childListKeys = append(childListKeys, field.Code)
			childListKeyFormContentMap[field.Code] = childListOptions.Form
		}
	}
	return
}

/* @note
 * 提取表单中所有的字段名
 */
func (op *FormOperator) ExtractFieldsFromForm(form interface{}) (fields []string) {
	var content FormContent
	err := common.InterfaceToStruct(form, &content)
	if err != nil {
		return
	}
	for _, field := range content.Fields {
		fields = append(fields, field.Code)
	}
	return
}

/* @note
 * 提取表单中对应的联系人
 */
func (op *FormOperator) ExtractStaffsFromForm(confs []*StaffFromForm, data interface{}) (
	ret []*staffLogic.StaffInfo) {
	if common.IsNil(data) {
		return
	}
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return
	}
	for _, conf := range confs {
		if conf.Field != common.StringEmpty && dataMap[conf.Field] != nil {
			staffs := make([]*staffLogic.StaffInfo, 0)
			if err := common.InterfaceToStruct(dataMap[conf.Field], &staffs); err == nil {
				for _, staff := range staffs {
					if staff.UserId != 0 {
						staff.Type = bpmCom.STAFF_TYPE_PERSON
						ret = append(ret, staff)
					} else if staff.DepartmentId != 0 {
						staff.Type = bpmCom.STAFF_TYPE_DEPARTMENT
						ret = append(ret, staff)
					}
				}
			}
		}
	}
	return ret
}

/* @note
 * 与上一个函数差不多，提取data里的关联审批单的uuid
 */
func (op *FormOperator) ExtractWorkflowUuidsFromData(keys []string, data interface{}) (keyWorkflowUuidsMap map[string][]string, allWorkflowUuids []string) {
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return
	}
	var err error
	keyWorkflowUuidsMap = make(map[string][]string)
	for _, key := range keys {
		if relateWorkflowIface, ok := dataMap[key]; ok {
			if relateWorkflowISlice, ok := relateWorkflowIface.([]interface{}); ok {
				for _, i := range relateWorkflowISlice {
					var relateWorkflowData RelateWorkflowData
					err = common.InterfaceToStruct(i, &relateWorkflowData)
					if err != nil || relateWorkflowData.WorkflowUuid == common.StringEmpty {
						continue
					}
					keyWorkflowUuidsMap[key] = append(keyWorkflowUuidsMap[key], relateWorkflowData.WorkflowUuid)
					allWorkflowUuids = append(allWorkflowUuids, relateWorkflowData.WorkflowUuid)
				}
			}
		}
	}
	return
}

/* @note
 * 与上一个函数差不多，提取data里的关联审批单字段的uuid和field-value
 */
func (op *FormOperator) ExtractWorkflowUuidsFromDataAndRelateWorkflowData(keys []string, data interface{}) (keyWorkflowUuidsMap map[string][]string,
	allWorkflowUuids []string, relateWorkflowFieldDatas []RelateWorkflowFieldData) {
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return
	}
	var err error
	keyWorkflowUuidsMap = make(map[string][]string)
	for _, key := range keys {
		if relateWorkflowIface, ok := dataMap[key]; ok {
			if relateWorkflowISlice, ok := relateWorkflowIface.([]interface{}); ok {
				for _, i := range relateWorkflowISlice {
					var relateWorkflowFieldData RelateWorkflowFieldData
					err = common.InterfaceToStruct(i, &relateWorkflowFieldData)
					if err != nil || relateWorkflowFieldData.WorkflowUuid == common.StringEmpty {
						continue
					}
					keyWorkflowUuidsMap[key] = append(keyWorkflowUuidsMap[key], relateWorkflowFieldData.WorkflowUuid)
					allWorkflowUuids = append(allWorkflowUuids, relateWorkflowFieldData.WorkflowUuid)
					relateWorkflowFieldDatas = append(relateWorkflowFieldDatas, relateWorkflowFieldData)
				}
			}
		}
	}
	return
}
