/**
 * @note
 * 表单相关结构体
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */
package form

import (
	"encoding/json"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

type LayoutTypeEnum string
type ItemTypeEnum string
type ContainerTypeEnum string

const (
	LAYOUT_TYPE_INLINE     LayoutTypeEnum = "inline"
	LAYOUT_TYPE_HORIZONTAL LayoutTypeEnum = "horizontal"
)

const (
	CONTAINER_TYPE_BLOCK ContainerTypeEnum = "block"
)

const (
	ITEM_TYPE_HTML           ItemTypeEnum = "html"           //html块
	ITEM_TYPE_STATIC         ItemTypeEnum = "static"         //静态展示
	ITEM_TYPE_DIVIDER        ItemTypeEnum = "divider"        //分割线
	ITEM_TYPE_TEXT           ItemTypeEnum = "text"           //文本
	ITEM_TYPE_TEXTAREA       ItemTypeEnum = "textarea"       //多行文本
	ITEM_TYPE_NUMBER         ItemTypeEnum = "number"         //数字
	ITEM_TYPE_RADIO          ItemTypeEnum = "radio"          //单选框
	ITEM_TYPE_CHECKBOX       ItemTypeEnum = "checkbox"       //勾选框
	ITEM_TYPE_CHECKBOXES     ItemTypeEnum = "checkboxes"     //多选框
	ITEM_TYPE_SELECT         ItemTypeEnum = "select"         //下拉单选
	ITEM_TYPE_CHAINED_SELECT ItemTypeEnum = "chained-select" //级联下拉选择
	ITEM_TYPE_TREE           ItemTypeEnum = "tree"           //树
	ITEM_TYPE_DATE           ItemTypeEnum = "date"           //日期
	ITEM_TYPE_DATETIME       ItemTypeEnum = "datetime"       //日期+时间
	ITEM_TYPE_IMAGE          ItemTypeEnum = "image"          //图像
	ITEM_TYPE_FILE           ItemTypeEnum = "file"           //文件
	ITEM_TYPE_COMBO          ItemTypeEnum = "combo"          //组合
)

// 页面配置
type FormConfig struct {
	//Title           bpmCom.I18nString `json:"title"`
	//Body            []FormBlock       `json:"body"`
	FieldPermission *FormPermission `json:"formPermission"`
}

type FormPermission struct { //默认全部disable，即只读
	AllWritable    bool     `json:"allWritable"`    //此表单内配置项全部可写
	WritableFields []string `json:"writableFields"` //可写的字段
	HiddenFields   []string `json:"hiddenFields"`   //需要隐藏的字段
}

type Front struct {
	Form           interface{}     `json:"form"`
	FormPermission *FormPermission `json:"formPermission"`
}

type FormContent struct {
	Fields []FormField `json:"fields"`
}

type FormField struct {
	Id       int64           `json:"_id"`
	Code     string          `json:"code"` //key
	Disabled bool            `json:"disabled"`
	Options  interface{}     `json:"options"`
	Name     json.RawMessage `json:"name"`
	Required *bool           `json:"required"`
	Type     string          `json:"type"`
}

const (
	FORM_FIELD_TYPE_RELATE_WORKFLOW       = "relate_process"       //关联审批单
	FORM_FIELD_TYPE_FORM_LIST             = "form_list"            //子表单列表 包含子表单，子表单内嵌套组件，因此需要循环检查
	FORM_FIELD_TYPE_CHILD_LIST            = "child_list"           //组件列表	包含子组件，也会包含嵌套关系，所以需要循环检查
	FORM_FIELD_TYPE_RELATE_WORKFLOW_FIELD = "relate_process_field" //关联审批单中字段
)

type FormListOptions struct { //子表单列表
	Form interface{} `json:"model"`
}

type ChildListOptions struct { //子组件列表
	Form interface{} `json:"model"`
}

// 在Form里的关联审批单的模板范围
type RelateWorkflowTemplateOptions struct {
	Data []RelateWorkflowTemplateData `json:"data"`
}

type RelateWorkflowTemplateData struct {
	WorkflowTemplateUuid string          `json:"workflowTemplateUuid"`
	Name                 json.RawMessage `json:"name"`
}

type RelateWorkflowTemplateFieldOptions = RelateWorkflowTemplateOptions

// fix cycle import
// 在具体流程中提交上来的关联审批单数据
type RelateWorkflowData = bpmCom.RelateWorkflowData

// 关联审批单取字段控件
type RelateWorkflowFieldData = bpmCom.RelateWorkflowFieldData

type StaffFromForm struct {
	Field string          `json:"field"` //从哪个字段取联系人
	Name  json.RawMessage `json:"name,omitempty"`
}

type FormOperator struct {
	ctx common.HContextIface
}

func (op *FormOperator) Ctx() common.HContextIface {
	return op.ctx
}

func NewFormOperator(ctx common.HContextIface) *FormOperator {
	return &FormOperator{ctx: ctx}
}
