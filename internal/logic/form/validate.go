/**
 * @note
 * 必要的时候校验一下用户输入的表单
 *
 * <AUTHOR>
 * @date 	2020-05-24
 */
package form

import (
	"encoding/json"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"reflect"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

/* @note
 * 校验传入的workflowUuid是否在表单里的关联审批单内，用于审批人查看发起人提交的表单的场景。如果审批人试图查看发起人表单中没有的关联审批单，那么拦住并报错
 */
func (op *FormOperator) ValidateRelatedWorkflowUuidInData(front, targetWorkflowUuid string, data interface{}) error {
	var frontDetail Front
	err := common.JsonStringDecode(front, &frontDetail)
	if err != nil {
		return err
	}
	return op.validateRelatedWorkflowUuidInData(frontDetail.Form, targetWorkflowUuid, data)
}

func (op *FormOperator) validateRelatedWorkflowUuidInData(form interface{}, targetWorkflowUuid string, data interface{}) error {
	//校验所有关联审批单
	relatedWorkflowKeys, _,
		relatedWorkflowFieldKeys, _ := op.ExtractRelatedTemplateUuidsFromForm(form)
	_, allWorkflowUuids := op.ExtractWorkflowUuidsFromData(append(relatedWorkflowKeys, relatedWorkflowFieldKeys...), data)
	//校验是否所有的workflowUuid的模板uuid都是对的，且状态都是已完成
	for _, workflowUuid := range allWorkflowUuids {
		if targetWorkflowUuid == workflowUuid {
			return nil
		}
	}
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return common2.ErrNotRelateWorkflow
	}
	formListKeys, formListKeyFormContentMap,
		childListKeys, childListKeyFormContentMap := op.ExtractFormListAndChildListFromForm(form)
	if len(formListKeys) > 0 { //校验子表单列表里是否有这个关联审批流程
		for _, formListKey := range formListKeys {
			if v, ok := dataMap[formListKey]; ok {
				if subDataIfaces, ok := v.([]interface{}); ok {
					for _, subDataIface := range subDataIfaces {
						if op.validateRelatedWorkflowUuidInData(formListKeyFormContentMap[formListKey], targetWorkflowUuid, subDataIface) == nil {
							return nil
						}
					}
				}
			}
		}
	} else if len(childListKeys) > 0 { //校验子组件列表里是否有这个关联审批流程
		for _, childListKey := range childListKeys {
			if v, ok := dataMap[childListKey]; ok {
				if subDataIfaces, ok := v.([]interface{}); ok {
					for _, subDataIface := range subDataIfaces {
						if op.validateRelatedWorkflowUuidInData(childListKeyFormContentMap[childListKey], targetWorkflowUuid, subDataIface) == nil {
							return nil
						}
					}
				}
			}
		}
	}
	return common2.ErrNotRelateWorkflow
}

/* @note
 * 校验用户是否有非法传入，用于用户提交表单时的校验
 */
//TODO 这里之后需要解析所有前端表单配置，并校验用户是否有非法输入，否则会有bug风险，目前只做了关联审批单的校验

func (op *FormOperator) ValidateUserInputData(front string, data interface{}) error {
	var frontDetail Front
	err := common.JsonStringDecode(front, &frontDetail)
	if err != nil {
		return err
	}
	return op.validateUserInputData(frontDetail.Form, data)
}

func (op *FormOperator) validateUserInputData(form interface{}, data interface{}) error {
	//校验所有关联审批单
	relatedWorkflowKeys, keyRelatedTemplateUuidMap,
		relatedWorkflowFieldKeys, keyRelatedTemplateUuidFieldMap := op.ExtractRelatedTemplateUuidsFromForm(form)
	if len(relatedWorkflowKeys) > 0 { //先校验关联审批单
		keyWorkflowUuidsMap, allWorkflowUuids := op.ExtractWorkflowUuidsFromData(relatedWorkflowKeys, data)
		if err := op.validateWorkflowIsFinishedAndIsRelatedWithInitiator(keyRelatedTemplateUuidMap, keyWorkflowUuidsMap, allWorkflowUuids); err != nil {
			return err
		}
	}
	if len(relatedWorkflowFieldKeys) > 0 { //再校验关联审批单字段
		keyWorkflowUuidsMap, allWorkflowUuids, relateWorkflowFieldDatas := op.ExtractWorkflowUuidsFromDataAndRelateWorkflowData(relatedWorkflowFieldKeys, data)
		if err := op.validateWorkflowIsFinishedAndIsRelatedWithInitiator(keyRelatedTemplateUuidFieldMap, keyWorkflowUuidsMap, allWorkflowUuids); err != nil {
			return err
		}
		//TODO 检查关联审批单表单内的字段是否合法（用户提交时没有篡改）
		wModel, err := workflowModel.NewWorkflowModel(op, false)
		if err != nil {
			return err
		}
		endNodes, err := wModel.QueryNodeRuntimeByParentUuidsAndType(allWorkflowUuids, common2.NODE_TYPE_END)
		if err != nil {
			return err
		}
		workflowUuidDataMap := make(map[string]interface{})
		for _, n := range endNodes {
			m := make(map[string]interface{})
			err = common.JsonStringDecode(n.Data, &m)
			if err != nil {
				return err
			}
			workflowUuidDataMap[n.ParentUuid] = m["input"]
		}
		for _, relateWorkflowFieldData := range relateWorkflowFieldDatas {
			if data, ok := workflowUuidDataMap[relateWorkflowFieldData.WorkflowUuid]; ok && !common.IsNil(data) {
				op.Ctx().Log().Infoln("checking relate workflow field data, origin data:", data)
				op.Ctx().Log().Infoln("user input field:", relateWorkflowFieldData.Field, "value:", relateWorkflowFieldData.FieldValue)
				if dataMap, ok := data.(map[string]interface{}); ok {
					if originFieldValue, ok := dataMap[relateWorkflowFieldData.Field]; ok {
						op.Ctx().Log().Infoln("origin field value:", originFieldValue)
						switch originFieldValue.(type) {
						case string, json.Number:
							if relateWorkflowFieldData.FieldValue == originFieldValue {
								continue
							} else {
								return common2.ErrRelateWorkflowInputError
							}
						case []interface{}:
							if reflect.DeepEqual(originFieldValue, relateWorkflowFieldData.FieldValue) {
								continue
							} else {
								return common2.ErrRelateWorkflowInputError
							}
						default:
							continue
						}
					}
				}
			}
		}
	}
	//TODO 校验整数、文本、数字是否合法等等等
	return nil
}

/* @note
 * 校验用户输入的关联审批单workflow是否与用户有关（用户是此流程的发起人，审批人，抄送人）
 * params keyRelatedTemplateUuidMap 模板中所配置的key(field_key) : map[workflowTemplateUuid]bool 的组合Map（关联审批单）
 */
func (op *FormOperator) validateWorkflowIsFinishedAndIsRelatedWithInitiator(keyRelatedTemplateUuidMap map[string]map[string]bool,
	keyWorkflowUuidsMap map[string][]string, allWorkflowUuids []string) error {
	if len(allWorkflowUuids) > 0 {
		wModel, err := workflowModel.NewWorkflowModel(op, false)
		if err != nil {
			return err
		}
		workflowTables, err := wModel.QueryWorkflowRuntimeByUuids(allWorkflowUuids)
		workflowUuidTableMap := make(map[string]*workflowModel.WorkflowTable)
		for idx := range workflowTables {
			workflowUuidTableMap[workflowTables[idx].Uuid] = workflowTables[idx]
		}
		var workflowUuidNeedCheckProcess []string
		for key, workflowUuids := range keyWorkflowUuidsMap {
			templateUuidMap, ok := keyRelatedTemplateUuidMap[key] //拿出此key对应的templateUuid集合
			if !ok {
				return common2.ErrRelateWorkflowInputError
			}
			for _, workflowUuid := range workflowUuids {
				if workflowUuidTableMap[workflowUuid] == nil || //库中找不到此workflow
					workflowUuidTableMap[workflowUuid].State != common2.STATE_FINISHED || //状态不对
					!templateUuidMap[workflowUuidTableMap[workflowUuid].TemplateUuid] { //找不到此workflow对应的模板
					return common2.ErrRelateWorkflowInputError
				}
				if workflowUuidTableMap[workflowUuid].UserId != op.ctx.User().UserId { //这个workflow不是发起人发起的，那么要检查是否有权限
					workflowUuidNeedCheckProcess = append(workflowUuidNeedCheckProcess, workflowUuid)
				}
			}
		}
		if len(workflowUuidNeedCheckProcess) > 0 {
			//检查这些workflow是不是都跟发起人有关系（即发起人是这些workflow的审批人/抄送人）
			pModel, err := processModel.NewProcessModel(op, false)
			if err != nil {
				return err
			}
			processTables, err := pModel.QueryUserProcessRuntimeByWorkflowUuids(workflowUuidNeedCheckProcess)
			if err != nil {
				return err
			}
			hasPermissionWorkflow := make(map[string]bool)
			for _, p := range processTables {
				hasPermissionWorkflow[p.WorkflowUuid] = true
			}
			for _, needToCheckWorkflowUuid := range workflowUuidNeedCheckProcess {
				if !hasPermissionWorkflow[needToCheckWorkflowUuid] {
					return common2.ErrRelateWorkflowInputError
				}
			}
		}
	}
	return nil
}

/* @note
 * 根据前端front，过滤掉data中所有无写权限的field
 */
func (op *FormOperator) FilterUnWritableFields(data interface{}, front *Front) interface{} {
	data = op.FilterAllRelateWorkflowKey(data, front.Form)
	if front.FormPermission != nil {
		if !front.FormPermission.AllWritable {
			dataMap, ok := data.(map[string]interface{})
			if !ok {
				data = nil
			}
		NEXT_KEY:
			for key := range dataMap { //校验用户输入的每个key是否有权限
				for _, writableKey := range front.FormPermission.WritableFields {
					if key == writableKey {
						continue NEXT_KEY
					}
				}
				delete(dataMap, key)
			}
			data = dataMap
			if len(dataMap) == 0 {
				data = nil
			}
		}
	} else { //没有可写权限，直接把传入的data置为nil
		data = nil
	}
	return data
}

func (op *FormOperator) FilterAllRelateWorkflowKey(data interface{}, form interface{}) interface{} {
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return data
	}
	relateWorkflowKeys, _, relateWorkflowFieldKeys, _ := op.ExtractRelatedTemplateUuidsFromForm(form)
	for _, relateWorkflowKey := range relateWorkflowKeys { //删掉所有关联审批单的key，以防审批人修改
		delete(dataMap, relateWorkflowKey)
	}
	for _, relateWorkflowFieldKey := range relateWorkflowFieldKeys { //删掉所有关联审批单中字段的key，以防审批人修改
		delete(dataMap, relateWorkflowFieldKey)
	}
	return dataMap
}
