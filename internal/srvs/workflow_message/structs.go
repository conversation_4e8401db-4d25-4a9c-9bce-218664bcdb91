/**
 * @note
 * 信使，往chan中推一条消息
 *
 * <AUTHOR>
 * @date 	2019-11-28
 */
package workflow_message

import (
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type WorkflowMessenger struct {
	ctx      common.HContextIface
	wg       *common.WaitGroupWrapper
	workerId int64
}

func NewWorkflowMessenger(ctx common.HContextIface) *WorkflowMessenger {
	wp := &WorkflowMessenger{
		ctx: ctx,
		wg:  new(common.WaitGroupWrapper),
	}
	return wp
}

func (wp *WorkflowMessenger) Ctx() common.HContextIface {
	return wp.ctx
}
