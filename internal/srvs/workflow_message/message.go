/**
 * @note
 * workflow_messenger
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */
package workflow_message

import (
	"gitlab.docsl.com/security/bpm/internal/common"
	"time"
)

func (wm *WorkflowMessenger) NonBlockingSend(msg *common.WorkflowProcessMsg) error {
	select {
	case common.WorkflowProcessChan <- msg:
		return nil
	default:
		return common.ErrSendWorkflowProcessMsg
	}
}

func (wm *WorkflowMessenger) NonBlockingSendWithRetry(msg *common.WorkflowProcessMsg) error {
	ticker := time.NewTicker(500 * time.Millisecond)
	for i := 0; i < 3; i++ { //重试三次
		select {
		case common.WorkflowProcessChan <- msg:
			return nil
		case <-ticker.C:
			continue
		}
	}
	return common.ErrSendWorkflowProcessMsg
}

func (wm *WorkflowMessenger) BlockingSend(msg *common.WorkflowProcessMsg) {
	common.WorkflowProcessChan <- msg
}

func (wm *WorkflowMessenger) Wait(msg *common.WorkflowProcessMsg) error {
	return <-msg.Err<PERSON>han
}
