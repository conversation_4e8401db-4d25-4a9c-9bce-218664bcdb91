/**
 * @note
 * workflow服务的结构体定义
 *
 * <AUTHOR>
 * @date 	2019-10-31
 */
package workflow_service

import (
	"gitlab.docsl.com/security/bpm/pkg/common"
	"sync/atomic"
)

type WorkflowProcessor struct {
	ctx      common.HContextIface
	wg       *common.WaitGroupWrapper
	workerId int64
}

func NewWorkflowProcesser(ctx common.HContextIface) *WorkflowProcessor {
	wp := &WorkflowProcessor{
		ctx: ctx,
		wg:  new(common.WaitGroupWrapper),
	}
	return wp
}

func (wp *WorkflowProcessor) Ctx() common.HContextIface {
	return wp.ctx
}

func (wp *WorkflowProcessor) Wg() *common.WaitGroupWrapper {
	return wp.wg
}

type WorkflowWorker struct {
	ctx      common.HContextIface
	parent   *WorkflowProcessor
	workerId int64
}

func NewWorkflowWorker(parent *WorkflowProcessor) *WorkflowWorker {
	workerId := atomic.AddInt64(&parent.workerId, 1)
	l := parent.ctx.Log().<PERSON><PERSON>(workerId)
	ctx := common.NewContext(l, parent.ctx.GinCtx())
	ww := &WorkflowWorker{
		ctx:      ctx,
		parent:   parent,
		workerId: workerId,
	}
	return ww
}

func (ww *WorkflowWorker) Ctx() common.HContextIface {
	return ww.ctx
}
