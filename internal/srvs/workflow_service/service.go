/**
 * @note
 * workflow_service
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */
package workflow_service

import (
	"errors"
	"fmt"
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	weLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"os"
	"time"
)

func (wp *WorkflowProcessor) Run() {
	for idx := 0; idx < common2.GetConfig().ProcessorNum; idx++ {
		ww := NewWorkflowWorker(wp)
		wp.wg.Wrap(ww.handleWorkflowProcessMsgLoop)
	}
	wp.wg.Wait()
}

func (ww *WorkflowWorker) handleWorkflowProcessMsgLoop() {
	defer func() {
		ww.ctx.Log().Infof("worker[%d] break loop", ww.workerId)
	}()
	ticker := time.NewTicker(time.Minute * 30)
	for {
		select {
		case <-common.QuitChan:
			ww.ctx.Log().Infof("worker[%d] get <PERSON>uit<PERSON>han, exit", ww.workerId)
			return
		case <-ticker.C:
			ww.ctx.Log().Infof("worker[%d] msg=still alive", ww.workerId)
		case msg := <-common2.WorkflowProcessChan: //用户只要提了工单，就会执行该workflow
			msg.InitErrChanIfNilOrClosed()
			if len(msg.WorkflowUuid) == 0 {
				ww.alarm(msg.WorkflowUuid, "Warning", "receive msg workflowUuid is empty, drop", nil)
				continue
			}
			if len(msg.TraceId) > 0 {
				ww.ctx.Log().SetTraceId(msg.TraceId)
			}
			ww.ctx.Log().Infof("worker[%d] receive msg workflow[%s] node[%s], start processing",
				ww.workerId, msg.WorkflowUuid, msg.NodeUuid)
			workflow, err := weLogic.NewWorkflow(ww, msg.WorkflowUuid)
			if err != nil {
				ww.alarm(workflow.GetUuid(), "Error", "init workflow error", err)
				continue
			}
			for tryTimes := 0; tryTimes < common2.GetConfig().ProcessorRetryTimes; tryTimes++ {
				ww.Ctx().Log().Infof("worker[%d] try process workflow[%s] times [%d]", ww.workerId, msg.WorkflowUuid, tryTimes)
				//1.1 从表中加载数据到内存,变成workflow和若干tasks(nodes)
				if err = workflow.Load(); err != nil {
					ww.alarm(workflow.GetUuid(), "Error", "load workflow error", err)
					continue
					//1.2 真正处理workflow
				} else if err = ww.processWorkflow(ww.workerId, msg, workflow); err != nil {
					ww.alarm(workflow.GetUuid(), "Error", "process workflow error", err)
					continue
				}
				break //成功了，跳出
			}
			ww.ctx.Log().Infof("worker[%d] finish processing workflow[%s]", ww.workerId, msg.WorkflowUuid)
			msg.ErrChan <- err
			msg.CloseChanIgnorePanic()
		}
	}
}

func (ww *WorkflowWorker) processWorkflow(workerId int64, msg *common2.WorkflowProcessMsg, workflow *weLogic.Workflow) (err error) {
	defer func() {
		if err := recover(); err != nil {
			trace := common.IdentifyPanic()
			ww.ctx.Log().Errorf("workflow processor error: %+v, trace: %+v\n", err, trace)
			sendLarkAlarm(ww.ctx, err, trace)
			return
		}
	}()
	switch workflow.State() {
	case common2.STATE_READY:
		err = workflow.Begin()
		if err != nil {
			return fmt.Errorf("begin workflow error: %v", err)
		}
		//执行成功
	case common2.STATE_PENDING:
		err = workflow.Continue(msg.NodeUuid)
		if err != nil {
			return fmt.Errorf("continue workflow error: %v", err)
		}

	case common2.STATE_EXECUTING: //正在执行
		err = workflow.Wait()
		if err != nil {
			return fmt.Errorf("wait workflow error: %v", err)
		}
		return ww.processWorkflow(workerId, msg, workflow) //等待完成，重新处理
	case common2.STATE_FINISHED: //已经成功
		ww.ctx.Log().Infof("workflow[%s] finished, no need to process", workflow.GetUuid())
	case common2.STATE_FAILED, common2.STATE_DISCARD: //已经失败
		ww.ctx.Log().Infof("workflow[%s] already failed, no need to process", workflow.GetUuid())
	case common2.STATE_RESET:
		ww.ctx.Log().Infof("workflow[%s] is reset, waiting initiator to recreate", workflow.GetUuid())
	case common2.STATE_NEED_COMPLETE:
		ww.ctx.Log().Infof("workflow[%s] needs complete, waiting initiator to complete", workflow.GetUuid())
	default:
		return errors.New("unknown state, give up processing")
	}
	return nil
}

func (ww *WorkflowWorker) alarm(workflowUuid, level, msg string, err error) {
	var errStr string
	if err != nil {
		errStr = err.Error()
	}
	content, _ := common.ToPrettyJsonString(map[string]interface{}{
		"env":   common.GetEnv(),
		"trace": ww.ctx.Log().GetTrace(),
		"content": map[string]interface{}{
			"workflowUuid": workflowUuid,
			"level":        level,
			"message":      msg,
			"error":        errStr,
		},
		"host": func() string {
			host, _ := os.Hostname()
			return host
		}(),
	})
	errSend := lark.SendLarkRobotAlarmMessage(ww.ctx.GinCtx(), content)
	if level == "Warning" {
		ww.ctx.Log().Warningf("worker[%d] process workflow[%s] msg [%s] error [%v]", ww.workerId, workflowUuid, msg, err)
	} else {
		ww.ctx.Log().Errorf("worker[%d] process workflow[%s] msg [%s] error [%v]", ww.workerId, workflowUuid, msg, err)
	}
	if err != nil {
		ww.ctx.Log().Errorln("send dingtalk message err:", errSend)
	}
}

func sendLarkAlarm(ctx common.HContextIface, err interface{}, stack string) {
	content, _ := common.ToPrettyJsonString(map[string]interface{}{
		"env":   common.GetEnv(),
		"trace": ctx.Log().GetTrace(),
		"content": map[string]interface{}{
			"panic": err,
			"stack": stack,
		},
		"host": func() string {
			host, _ := os.Hostname()
			return host
		}(),
	})
	_ = lark.SendLarkRobotAlarmMessage(ctx.GinCtx(), content)
	return
}
