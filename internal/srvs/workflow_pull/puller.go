/**
 * @note
 * 进程重启时用以恢复未完成的服务
 *
 * <AUTHOR>
 * @date 	2019-12-26
 */
package workflow_pull

import (
	interCommon "gitlab.docsl.com/security/bpm/internal/common"
	workflowModel "gitlab.docsl.com/security/bpm/internal/model/workflow"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/common/structures/sets"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"os"
	"time"
)

func (wp *WorkflowPuller) Pull() {
	ticker := time.NewTicker(time.Second * time.Duration(interCommon.GetConfig().WaitInterval))
	endTicker := time.After(time.Second * time.Duration(interCommon.GetConfig().WaitTimeout))
	wModel, err := workflowModel.NewWorkflowModel(wp, false)
	if err != nil {
		wp.sendDingNotify("Error", "workflow puller new workflowModel err", err)
		return
	}
	wp.ctx.Log().Infoln("workflow puller start to scan orphan workflow")
	pulledWorkflows := sets.HashSet{}
	for {
		select {
		case <-common.QuitChan:
			wp.ctx.Log().Infoln("workflow puller get QuitChan, exit")
			return
		case <-endTicker:
			wp.ctx.Log().Infof("after %d seconds, workflow puller finished, quit", interCommon.GetConfig().WaitTimeout)
			return
		case <-ticker.C:
			//先查创建出来很久的仍然还是Ready的workflow，尝试拉起
			readyWorkflows, err := wModel.QueryReadyWorkflowByCreateTimeout(interCommon.GetConfig().PulseTimeout)
			if err != nil {
				wp.sendDingNotify("Error", "workflow puller query workflow err", err)
			} else {
				for _, readyWorkflow := range readyWorkflows {
					if !pulledWorkflows.Contains(readyWorkflow.Uuid) {
						interCommon.WorkflowProcessChan <- &interCommon.WorkflowProcessMsg{
							WorkflowUuid: readyWorkflow.Uuid,
							TraceId:      wp.ctx.Log().GetTraceId(),
						}
						wp.sendDingNotify("Info", "workflow puller pulling ready workflow: "+readyWorkflow.Uuid, nil)
						pulledWorkflows.Add(readyWorkflow.Uuid)
					}
				}
			}
			//再查心跳过期的Executing的workflow，尝试拉起
			executingWorkflows, err := wModel.QueryExecutingWorkflowByPulseTimeout(interCommon.GetConfig().PulseTimeout)
			if err != nil {
				wp.sendDingNotify("Error", "workflow puller query workflow err", err)
			} else {
				for _, executingWorkflow := range executingWorkflows {
					if !pulledWorkflows.Contains(executingWorkflow.Uuid) {
						interCommon.WorkflowProcessChan <- &interCommon.WorkflowProcessMsg{
							WorkflowUuid: executingWorkflow.Uuid,
							TraceId:      wp.ctx.Log().GetTraceId(),
						}
						wp.sendDingNotify("Info", "workflow puller pulling timeout executing workflow: "+executingWorkflow.Uuid, nil)
						pulledWorkflows.Add(executingWorkflow.Uuid)
					}
				}
			}
		}
	}
}

func (wp *WorkflowPuller) sendDingNotify(level, msg string, err error) {
	var errStr string
	if err != nil {
		errStr = err.Error()
	}
	content, _ := common.ToPrettyJsonString(map[string]interface{}{
		"env":   common.GetEnv(),
		"trace": wp.ctx.Log().GetTrace(),
		"content": map[string]interface{}{
			"level":   level,
			"message": msg,
			"error":   errStr,
		},
		"host": func() string {
			host, _ := os.Hostname()
			return host
		}(),
	})
	errSend := lark.SendLarkRobotMessage(wp.ctx.GinCtx(), content)
	if errSend != nil {
		wp.ctx.Log().Errorln("send dingtalk message err:", errSend)
	}
}
