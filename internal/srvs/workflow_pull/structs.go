/**
 * @note
 * structs.go
 *
 * <AUTHOR>
 * @date 	2019-12-26
 */
package workflow_pull

import (
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type WorkflowPuller struct {
	ctx      common.HContextIface
	wg       *common.WaitGroupWrapper
	workerId int64
}

func NewWorkflowPuller(ctx common.HContextIface) *WorkflowPuller {
	wp := &WorkflowPuller{
		ctx: ctx,
		wg:  new(common.WaitGroupWrapper),
	}
	return wp
}

func (wp *WorkflowPuller) Ctx() common.HContextIface {
	return wp.ctx
}
