/**
 * @note
 * validate
 *
 * <AUTHOR>
 * @date 	2019-12-23
 */
package middleware

import (
	"github.com/gin-gonic/gin"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	appAuthLogic "gitlab.docsl.com/security/bpm/internal/logic/app_auth"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	"gitlab.docsl.com/security/bpm/internal/model/auth_app"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/middleware"
	"strconv"
)

// ValidateSignature 外部调用bpm系统时,对请求解签
func ValidateSignature() gin.HandlerFunc {
	return func(gctx *gin.Context) {
		ctx := common.GetHCtx(gctx)
		ctx.Log().Debugf("Resolve request signature for %s:%v", gctx.Request.Method, gctx.Request.URL)
		authInfo, canPass := AuthUrlSign(gctx)
		if !canPass {
			middleware.RenderErrorAndAbort(gctx, common.NewError(common.ERR_INTER_SIGNATURE))
		} else if userIdStr := gctx.GetHeader(common.HuobiSsoUserId); len(userIdStr) == 0 {
			middleware.RenderErrorAndAbort(gctx, common.NewError(common.ERR_INTER_NO_USER_ID))
		} else if userId, err := strconv.ParseInt(userIdStr, 10, 64); err != nil {
			middleware.RenderErrorAndAbort(gctx, common.NewError(common.ERR_INTER_ERR_USER_ID))
		} else {
			staffOperator := staffLogic.NewStaffOperator(ctx)
			if userInfo, err := staffOperator.GetUserInfoByUserId(userId); err != nil {
				middleware.RenderErrorAndAbort(gctx, common.NewError(common.ERR_INTER_ERR_USER_ID))
			} else {
				ctx.User().UserId = userInfo.UserId
				ctx.User().AccountName = userInfo.Account
				ctx.User().DisplayName = userInfo.NameDisplay
				ctx.User().Email = userInfo.Email
				ctx.User().EmployeeId = userInfo.EmployeeId
				if authInfo != nil {
					ctx.User().Origin = authInfo.AppName
					ctx.User().OriginAppId = authInfo.SsoAppId
				} else {
					ctx.User().Origin = bpmCom.OriginInternalSystem
				}
			}
		}
	}
}

/**
 * 对请求进行解签操作
 *
 *@param http.Request http请求
 *@return bool 请求解签是否成功
 */
func AuthUrlSign(gctx *gin.Context) (authItem *auth_app.AuthAppTable, canPass bool) {
	r := gctx.Request
	values := r.URL.Query()
	appId := values.Get("AWSAccessKeyId")
	signatureVersion := values.Get("SignatureVersion")
	signatureMethod := values.Get("SignatureMethod")
	timestamp := values.Get("Timestamp")
	cliSign := values.Get("Signature")
	ctx := common.GetHCtx(gctx)
	if appId == common.StringEmpty || signatureVersion == common.StringEmpty ||
		signatureMethod == common.StringEmpty || timestamp == common.StringEmpty ||
		cliSign == common.StringEmpty {
		return nil, false
	}
	appAuthOperator := appAuthLogic.NewAuthAppOperator(ctx)
	if authInfo, err := appAuthOperator.QueryAppKeyByAppId(appId); err != nil {
		return nil, false
	} else if serverSignature, err := common.GetSignature(
		r.Method,
		r.Host,
		r.URL.Path,
		appId,
		signatureVersion,
		signatureMethod,
		timestamp,
		authInfo.AppKey); err != nil {
		return nil, false
	} else {
		return authInfo, serverSignature == cliSign
	}
}
