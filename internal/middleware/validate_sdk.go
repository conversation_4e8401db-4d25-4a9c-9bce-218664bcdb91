/**
 * @note
 * validate_sdk.go
 *
 * <AUTHOR>
 * @date 	2020-07-06
 */
package middleware

import (
	"github.com/tmsong/cas-go-client"
	sdkAuth "gitlab.docsl.com/security/bpm/internal/logic/sdk_auth"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

func validateSdk(ctx common.HContextIface) bool {
	gctx := ctx.GinCtx()
	authToken := gctx.GetHeader(common.HuobiHeaderAuthToken)
	if len(authToken) == 0 {
		return false
	}
	ctx.Log().Infoln("validate sdk auth token")
	//先看有没有code进来，有就直接走登录逻辑
	authOperator := sdkAuth.NewSdkAuthOperator(ctx)
	user, err := authOperator.AuthToken(authToken)
	if err != nil {
		ctx.Log().Errorln("auth token err:", err)
		gctx.Request.Header.Del(common.HuobiHeaderAuthToken)
		return false
	}
	ctx.User().UserId = user.UserId
	ctx.User().AccountName = user.AccountName
	ctx.User().DisplayName = user.DisplayName
	ctx.User().Email = user.Email
	ctx.User().EmployeeId = user.EmployeeId
	ctx.User().Origin = user.Origin
	ctx.User().OriginAppId = user.OriginAppId
	cas.SetCurrentUserId(gctx.Request, user.UserId)
	return true
}
