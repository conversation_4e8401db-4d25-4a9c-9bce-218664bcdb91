/**
 * @note
 * 用户校验逻辑
 *
 * <AUTHOR>
 * @date 	2019-12-03
 */
package middleware

import (
	"crypto/rand"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/tmsong/hlog"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
	pkgMiddle "gitlab.docsl.com/security/bpm/pkg/middleware"
	uModel "gitlab.docsl.com/security/bpm/pkg/model/staff"
	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/cas"
	"gitlab.docsl.com/security/common/redis"
	"gorm.io/gorm"
	"net/url"
	"time"
)

var noValidatePath = map[string]bool{
	"/info":            true,
	"/cas/csrf":        true,
	"/actuator/health": true,
}

func Validate(l *hlog.Logger) gin.HandlerFunc {
	ssoConfig := cas.GetConfig()
	ssoClient := cas.GetCasClient()
	return func(gctx *gin.Context) {
		if noValidatePath[gctx.Request.URL.Path] {
			return
		}
		ctx := common.GetHCtx(gctx)
		ctx.Log().Debugf("cas: handling validate for %s:%v", gctx.Request.Method, gctx.Request.URL)
		redisCli, err := redis.DefaultClient(gctx)
		if err != nil {
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		}

		sessionID, _ := gctx.Cookie(ssoConfig.CookieKey)
		sessionKey := fmt.Sprintf(ssoConfig.SessionKey, MD5Hex(sessionID))
		if gctx.Request.URL.Path == ssoConfig.LogoutPath { //如果是登出接口，清除用户cookie
			gctx.SetCookie(ssoConfig.CookieKey, url.QueryEscape(common.StringEmpty), -1, "/", common.StringEmpty, false, true)
			_, _ = redisCli.Del(sessionKey)
			gctx.Next()
			return
		}
		//尝试sdk的auth token登录，如果成功，跳过下面步骤
		if validateSdk(ctx) {
			ctx.Log().Debugf("sdkAuth: sdk auth success, skip cas validate for %s:%v", gctx.Request.Method, gctx.Request.URL)
			return
		}
		if len(sessionID) > 0 { //取到了sessionId
			//尝试从redis中读session
			if session, err := redisCli.Get(sessionKey); err == nil {
				if err = common.JsonStringDecode(session.Val(), ctx.User()); err == nil { //认证成功
					_, _ = redisCli.Expire(sessionKey, time.Duration(ssoConfig.SessionTTL)*time.Second) //刷新延长时间
					gctx.SetCookie(ssoConfig.CookieKey, sessionID, ssoConfig.SessionTTL, "/", common.StringEmpty, false, true)
					gctx.Next()
					return
				}
			}
		}
		//尝试从url中读取sso认证信息
		params := &cas.SSOQuery{}
		err = gctx.BindQuery(params)
		if err != nil {
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		}

		if len(params.Code) == 0 || len(params.State) == 0 {
			jumpTo := GetJumpToFromHeaderAndUrl(gctx)
			redirect := fmt.Sprintf("%s%s", ssoConfig.AppBackDomain, ssoConfig.CallbackPath)
			if jumpTo == "" {
				jumpTo = fmt.Sprintf("%s%s", ssoConfig.AppDomain, gctx.Request.URL.String())
			}
			common.SetRet(gctx, common.NewError(common.ERR_REDIRECT_TO_LOGIN).SetDesc(cas.GetSSOLoginUrl(redirect, jumpTo)))
			gctx.Abort()
			return
		}

		token, err := ssoClient.GetOAuthToken(params.Code, params.State)
		if err != nil {
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		}

		claims, err := ssoClient.ParseJwtToken(token.AccessToken)
		if err != nil {
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		}

		// 填充用户信息
		// 查一下本地表看看有没有这个用户，然后顺便赋值
		userTable, err := uModel.GetUserByUserName(gctx, claims.User.Name)
		if errors.Is(err, gorm.ErrRecordNotFound) { // 没找到，再尝试用Email找
			userTable, err = uModel.GetUserByEmail(gctx, claims.User.Email)
		}
		if err != nil { // 如果Email还是没找到，歇逼了
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		} else if userTable.Status != bpmCom.StatusActivated {
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_NO_PERMISSION))
			return
		} else if userTable.UserName != claims.User.Name {
			// 这里第一次登录需要更新下表里的userName
			err = uModel.UpdateUserNameByID(gctx, userTable.ID, claims.User.Name)
			if err != nil {
				pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
				return
			}
		}

		ctx.User().AccountName = userTable.UserName
		ctx.User().UserId = userTable.ID
		ctx.User().DisplayName = userTable.LarkName
		ctx.User().Email = userTable.Email
		ctx.User().EmployeeId = userTable.EmployeeNo

		encodedUser, err := common.JsonStringEncode(ctx.User())
		if err != nil {
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		}
		sessionID, err = newSessionID()
		if err != nil {
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		}
		sessionKey = fmt.Sprintf(ssoConfig.SessionKey, MD5Hex(sessionID))
		err = redisCli.Set(sessionKey, encodedUser, time.Duration(ssoConfig.SessionTTL)*time.Second)
		if err != nil {
			pkgMiddle.RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		}
		gctx.SetCookie(ssoConfig.CookieKey, sessionID, ssoConfig.SessionTTL, "/", common.StringEmpty, false, true)
	}
}

func GetJumpToFromHeaderAndUrl(ctx *gin.Context) string {
	value := ctx.GetHeader("Jump-To")
	if value == "" {
		value, _ = ctx.GetQuery("jump_to")
	}
	return value
}

// newSessionId generates a new opaque session identifier for use in the cookie.
func newSessionID() (sessionID string, err error) {
	const alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	// generate 64 character string
	bytes := make([]byte, 64)
	_, err = rand.Read(bytes)
	if err != nil {
		return common.StringEmpty, nil
	}

	for k, v := range bytes {
		bytes[k] = alphabet[v%byte(len(alphabet))]
	}

	return string(bytes), nil
}
