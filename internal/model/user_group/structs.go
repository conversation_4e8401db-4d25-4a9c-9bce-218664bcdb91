/**
 * @note
 * 表结构定义
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */
package user_group

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/common/mysql"
	"gorm.io/gorm"
)

// user_group相关表名
const (
	USER_GROUP_INFO_TABLE_NAME     = "user_group_info"
	USER_GROUP_RELATION_TABLE_NAME = "user_group_relation"
)

type UserGroupInfoTable struct {
	common.GormModel
	UserId      int64             `json:"userId"`      //用户组创建者
	Name        string            `json:"name"`        //用户组名称
	Description string            `json:"description"` //用户组描述
	Status      bpmCom.StatusEnum `json:"status"`      //状态，正常/删除
}

type UserGroupRelationTable struct {
	common.GormModel
	UserGroupId int64             `json:"user_group_id"` //用户组Id
	UserId      int64             `json:"user_id"`       //用户Id
	Detail      string            `json:"detail"`        //生效条件
	Status      bpmCom.StatusEnum `json:"status"`        //状态，正常/删除
}

type UserGroupModel struct {
	parent   common.Operator
	gormDB   *gorm.DB
	readOnly bool
}

func (this *UserGroupModel) ShardingUserGroupInfoTable() (db *gorm.DB) {
	db = this.gormDB.Table(USER_GROUP_INFO_TABLE_NAME).Model(&UserGroupInfoTable{})
	return db
}

func (this *UserGroupModel) ShardingUserGroupRelationTable() (db *gorm.DB) {
	db = this.gormDB.Table(USER_GROUP_RELATION_TABLE_NAME).Model(&UserGroupRelationTable{})
	return db
}

func NewUserGroupModel(op common.Operator, readOnly bool) (model *UserGroupModel, err error) {
	model = &UserGroupModel{
		parent:   op,
		readOnly: readOnly,
	}
	model.gormDB, err = mysql.GetDB(common.ModuleName, readOnly, op.Ctx().Log())
	return
}
