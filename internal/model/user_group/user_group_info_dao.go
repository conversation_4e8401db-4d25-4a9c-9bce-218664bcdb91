/**
 * @note
 * 用户组信息
 *
 * <AUTHOR>
 * @date 	2019-11-24
 */
package user_group

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
)

// 在user_group_info表中插入一条记录
func (this *UserGroupModel) InsertUserGroupInfo(name, description string) (int64, error) {
	db := this.ShardingUserGroupInfoTable()
	newRow := &UserGroupInfoTable{
		UserId:      this.parent.Ctx().User().UserId,
		Name:        name,
		Description: description,
		Status:      bpmCom.STATUS_NORMAL,
	}
	db = db.Create(newRow)
	return newRow.ID, db.Error
}

// 软删除user_group_info中的记录
func (this *UserGroupModel) SoftDeleteUserGroupInfoById(userGroupIds []int64) ([]int64, error) {
	db := this.ShardingUserGroupInfoTable()
	db = db.Where("id in (?) AND status = ?", userGroupIds, bpmCom.STATUS_NORMAL).Updates(map[string]interface{}{
		"status": bpmCom.STATUS_DELETED,
	})
	db = this.ShardingUserGroupRelationTable()
	db = db.Where("user_group_id in (?) AND status = ?", userGroupIds, bpmCom.STATUS_NORMAL).Updates(map[string]interface{}{
		"status": bpmCom.STATUS_DELETED,
	})
	return userGroupIds, db.Error
}

// 更新用户组
func (this *UserGroupModel) UpdateUserGroupInfoById(userGroupId int64, updateMap map[string]interface{}) (int64, error) {
	db := this.ShardingUserGroupInfoTable()
	db = db.Where(map[string]interface{}{
		"id": userGroupId,
	}).Updates(updateMap)
	return db.RowsAffected, db.Error
}

// 根据用户组名称查询用户组
func (this *UserGroupModel) QueryUserGroupInfoByNameAndUserGroupIds(name string, userGroupIds []int64, offset, limit int) ([]UserGroupInfoTable, int64, error) {
	db := this.ShardingUserGroupInfoTable()
	var userGroupInfos []UserGroupInfoTable
	db = db.Where("status = ?", bpmCom.STATUS_NORMAL)
	if len(name) > 0 {
		db = db.Where("binary name like ?", "%"+name+"%")
	}
	if len(userGroupIds) > 0 {
		db = db.Where("id in (?)", userGroupIds)
	}

	var totalCount int64
	if err := db.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	db = db.Order("create_time DESC").Offset(offset).Limit(limit).Find(&userGroupInfos)
	return userGroupInfos, totalCount, db.Error
}

func (this *UserGroupModel) QueryUserGroupCntForPaging(name string, userGroupIds []int64) (int64, error) {
	var cnt int64
	db := this.ShardingUserGroupInfoTable()
	if len(name) > 0 {
		db = db.Where("binary name like ?", "%"+name+"%")
	}
	if len(userGroupIds) > 0 {
		db = db.Where("id in (?)", userGroupIds)
	}
	db = db.Where("status = ?", bpmCom.STATUS_NORMAL).Count(&cnt)
	return cnt, db.Error
}

// 根据userGroupId查询用户组
func (this *UserGroupModel) QueryUserGroupInfoById(userGroupId int64) (*UserGroupInfoTable, error) {
	db := this.ShardingUserGroupInfoTable()
	userGroupInfo := &UserGroupInfoTable{}
	db = db.Where(map[string]interface{}{
		"id":     userGroupId,
		"status": bpmCom.STATUS_NORMAL,
	}).First(&userGroupInfo)
	return userGroupInfo, db.Error
}
