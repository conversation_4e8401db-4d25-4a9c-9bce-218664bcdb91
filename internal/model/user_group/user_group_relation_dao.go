/**
 * @note
 * 用户与用户组关系
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */
package user_group

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
)

// 在用户组中添加用户
func (this *UserGroupModel) InsertUserGroupRelation(userGroupId int64, userIdDetailMap map[int64]string) error {
	db := this.ShardingUserGroupRelationTable()
	for userId, detail := range userIdDetailMap {
		newRow := &UserGroupRelationTable{}
		assignMap := map[string]interface{}{
			"user_group_id": userGroupId,
			"user_id":       userId,
			"detail":        detail,
			"status":        bpmCom.STATUS_NORMAL,
		}
		db = db.Where(
			map[string]interface{}{
				"user_group_id": userGroupId,
				"user_id":       userId,
			}).Assign(assignMap).FirstOrCreate(newRow)
		if db.Error != nil {
			return db.Error
		}
	}
	return nil
}

// 从用户组中删除用户
func (this *UserGroupModel) SoftDeleteUserFromGroup(userGroupId int64, userIds []int64) error {
	db := this.ShardingUserGroupRelationTable()
	db = db.Where("user_group_id = ? and user_id in (?) AND status = ?", userGroupId, userIds, bpmCom.STATUS_NORMAL).
		Updates(map[string]interface{}{
			"status": bpmCom.STATUS_DELETED,
		})
	return db.Error
}

// 根据用户组Uuid查询相关联的所有用户
func (this *UserGroupModel) QueryUsersByGroupId(groupId int64) (userIds []int64, err error) {
	relations, err := this.QueryUserGroupRelationsByGroupId(groupId)
	if err != nil {
		return nil, err
	}
	for _, relation := range relations {
		userIds = append(userIds, relation.UserId)
	}
	return userIds, nil
}

// 根据用户组Id查询相关联的所有关系表
func (this *UserGroupModel) QueryUserGroupRelationsByGroupId(groupId int64) (relations []UserGroupRelationTable, err error) {
	db := this.ShardingUserGroupRelationTable()
	relations = []UserGroupRelationTable{}
	db = db.Where(map[string]interface{}{
		"user_group_id": groupId,
		"status":        bpmCom.STATUS_NORMAL,
	}).Find(&relations)
	if db.Error != nil {
		return nil, db.Error
	}
	return relations, nil
}

// 根据用户Id查询相关联的所有用户组Id
func (this *UserGroupModel) QueryUserGroupIdsByUserId(userId int64) (userGroupIds []int64, err error) {
	db := this.ShardingUserGroupRelationTable()
	relations := []UserGroupRelationTable{}
	db = db.Select("user_group_id").Where(map[string]interface{}{
		"user_id": userId,
		"status":  bpmCom.STATUS_NORMAL,
	}).Find(&relations)
	if db.Error != nil {
		return nil, db.Error
	}
	for _, relation := range relations {
		userGroupIds = append(userGroupIds, relation.UserGroupId)
	}
	return userGroupIds, nil
}
