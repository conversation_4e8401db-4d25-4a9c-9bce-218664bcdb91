/**
 * @note
 * workflow，node宣贯表，model结构体定义
 *
 * <AUTHOR>
 * @date 	2019-10-30
 */
package workflow

import (
	"database/sql/driver"
	"encoding/json"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"

	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/common/mysql"
	"gorm.io/gorm"
)

// workflow相关表名
const (
	WORKFLOW_PERSISTENCE_TABLE_NAME    = "workflow_persistence"
	WORKFLOW_RUNTIME_TABLE_NAME        = "workflow_runtime"
	WORKFLOW_TEMPLATE_TABLE_NAME       = "workflow_template"
	WORKFLOW_TEMPLATE_GROUP_TABLE_NAME = "workflow_template_group"
)

// node相关表名
const (
	NODE_PERSISTENCE_TABLE_NAME = "node_persistence"
	NODE_RUNTIME_TABLE_NAME     = "node_runtime"
	NODE_TEMPLATE_TABLE_NAME    = "node_template"
)

type WorkflowModel struct {
	parent   common.Operator
	gormDB   *gorm.DB
	readOnly bool
}

type NodeTable struct {
	common.GormModel
	UserId       int64                    `json:"userId"`       //此Node所归属于的用户
	InitiatorId  int64                    `json:"initiatorId"`  //此Node的发起者（非代发情况下与用户相同）
	TemplateUuid string                   `json:"templateUuid"` //此Node所属模板的Uuid
	Uuid         string                   `json:"uuid"`         //此Node的Uuid
	ParentUuid   string                   `json:"parentUuid"`   //此Node所属workflow的Uuid
	Name         string                   `json:"name"`         //节点名称
	Type         bpmCom.NodeTypeEnum      `json:"type"`         //节点类型
	Detail       string                   `json:"detail"`       //节点细节，具体做什么
	Front        string                   `json:"front"`        //前端渲染相关
	Data         string                   `json:"data"`         //节点运行时数据
	PulseTime    int64                    `json:"pulseTime"`    //上次心跳时间
	State        bpmCom.WorkflowStateEnum `json:"state"`        //string形式的运行状态表示
	Status       bpmCom.StatusEnum        `json:"status"`       //node的状态，如未启用/启用/删除等
	Version      int64                    `json:"version"`      //版本号
}

type NodeTemplateTable struct {
	common.GormModel
	Uuid       string              `json:"uuid"`        //节点uuid
	ParentUuid string              `json:"parent_uuid"` //所属workflow模板uuid
	Name       string              `json:"name"`        //节点名称
	Type       bpmCom.NodeTypeEnum `json:"type"`        //节点类型
	Detail     string              `json:"detail"`      //节点细节，具体做什么
	Front      string              `json:"front"`       //前端渲染相关
	Status     bpmCom.StatusEnum   `json:"status"`      //node的状态，如未启用/启用/删除等
}

type WorkflowTable struct {
	common.GormModel
	UserId       int64                    `json:"userId"`       //此workflow所归属于的用户
	InitiatorId  int64                    `json:"initiatorId"`  //此workflow的发起者（非代发情况下与用户相同）
	TemplateUuid string                   `json:"templateUuid"` //此workflow所属模板的Uuid
	GroupId      int64                    `json:"groupId"`      //此workflow的模板所属的模板组id
	Uuid         string                   `json:"uuid"`         //此workflow的Uuid
	SerialId     int64                    `json:"serialId"`     //此workflow的流水号id
	Name         string                   `json:"name"`         //workflow名称
	Description  string                   `json:"description"`  //workflow描述
	Detail       string                   `json:"detail"`       //workflow的拓扑结构
	Front        string                   `json:"front"`        //workflow前端渲染相关
	Data         string                   `json:"data"`         //workflow运行时数据
	PulseTime    int64                    `json:"pulseTime"`    //上次心跳时间
	State        bpmCom.WorkflowStateEnum `json:"state"`        //string形式的运行状态表示
	Status       bpmCom.StatusEnum        `json:"status"`       //workflow的状态，如未启用/启用/删除等
	Version      int64                    `json:"version"`      //版本号
	Edition      string                   `json:"edition"`      //workflow历代的版本号
	Tags         WorkflowTemplateTags     `json:"tags,omitempty" gorm:"-"`
}

type WorkflowTemplateTable struct {
	common.GormModel
	UserId      int64                            `json:"userId"`      //模板创建者的uid
	Uuid        string                           `json:"uuid"`        //模板的Uuid
	Name        string                           `json:"name"`        //模板名称
	Description string                           `json:"description"` //模板描述
	Staff       string                           `json:"staff"`       //哪些人有权限使用该模板
	GroupId     int64                            `json:"groupId"`     //模板所属模板组
	Tags        WorkflowTemplateTags             `json:"tags"`        //tags
	Detail      string                           `json:"detail"`      //模板的拓扑结构
	Origin      bpmCom.UserOriginEnum            `json:"origin"`      //模板允许发起的来源，Hbpm/InternalSystem/All
	State       bpmCom.WorkflowTemplateStateEnum `json:"state"`       //模板的逻辑状态，Enabled/Disabled/Editing
	Status      bpmCom.StatusEnum                `json:"status"`      //模板数据状态，Normal/Deleted等
	Edition     string                           `json:"edition"`     //模板历代的版本号
}

type WorkflowTemplateTags []int64

func (t *WorkflowTemplateTags) Scan(src interface{}) error {
	if src == nil {
		return nil
	}
	b, _ := src.([]byte)
	return json.Unmarshal(b, t)
}

func (t WorkflowTemplateTags) Value() (driver.Value, error) {
	if common.IsNil(t) {
		return common.StringArrayEmpty, nil
	}
	b, err := json.Marshal(t)
	return string(b), err
}

type TemplateGroupTable struct {
	common.GormModel
	UserId      int64                             `json:"userId"`      //workflow模板组的创建者id
	Name        string                            `json:"name"`        //workflow模板组名称
	Description string                            `json:"description"` //workflow模板组描述信息
	State       bpmCom.WorkflowTmplGroupStateEnum `json:"state"`       //启用/停用
	Status      bpmCom.StatusEnum                 `json:"status"`      //Normal/Deleted
}

func NewWorkflowModel(op common.Operator, readOnly bool) (model *WorkflowModel, err error) {
	model = &WorkflowModel{
		parent:   op,
		readOnly: readOnly,
	}
	model.gormDB, err = mysql.GetDB(common.ModuleName, readOnly, op.Ctx().Log())
	return
}

func (this *WorkflowModel) GetParent() common.Operator {
	return this.parent
}

func (this *WorkflowModel) ShardingWorkflowPersistenceTable() (db *gorm.DB) {
	db = this.gormDB.Table(WORKFLOW_PERSISTENCE_TABLE_NAME).Model(&WorkflowTable{})
	return db
}

func (this *WorkflowModel) ShardingWorkflowRuntimeTable() (db *gorm.DB) {
	db = this.gormDB.Table(WORKFLOW_RUNTIME_TABLE_NAME).Model(&WorkflowTable{})
	return db
}

func (this *WorkflowModel) ShardingWorkflowTemplateTable() (db *gorm.DB) {
	db = this.gormDB.Table(WORKFLOW_TEMPLATE_TABLE_NAME).Model(&WorkflowTemplateTable{})
	return db
}

func (this *WorkflowModel) ShardingTemplateGroupTable() (db *gorm.DB) {
	db = this.gormDB.Table(WORKFLOW_TEMPLATE_GROUP_TABLE_NAME).Model(&TemplateGroupTable{})
	return db
}

func (this *WorkflowModel) ShardingNodePersistenceTable() (db *gorm.DB) {
	db = this.gormDB.Table(NODE_PERSISTENCE_TABLE_NAME).Model(&NodeTable{})
	return db
}

func (this *WorkflowModel) ShardingNodeRuntimeTable() (db *gorm.DB) {
	db = this.gormDB.Table(NODE_RUNTIME_TABLE_NAME).Model(&NodeTable{})
	return db
}

func (this *WorkflowModel) ShardingNodeTemplateTable() (db *gorm.DB) {
	db = this.gormDB.Table(NODE_TEMPLATE_TABLE_NAME).Model(&NodeTemplateTable{})
	return db
}

func (this *WorkflowModel) ShardingProcessRuntimeTable() (db *gorm.DB) {
	db = this.gormDB.Table(processModel.PROCESS_RUNTIME_TABLE_NAME).Model(&processModel.ProcessTable{})
	return db
}
