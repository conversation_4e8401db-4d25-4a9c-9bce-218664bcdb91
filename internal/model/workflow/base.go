/**
 * @note
 * base是node和workflow的model中的共用部分
 *
 * <AUTHOR>
 * @date 	2019-11-08
 */
package workflow

import (
	"sync"
	"sync/atomic"
)

type Base struct {
	*WorkflowModel
	Uuid           string
	mu             sync.RWMutex
	currentVersion int64
	occupied       int32         //是否获得了控制权
	finishChan     chan struct{} //此任务已结束
}

//处理关闭一个已关闭的channel导致的panic
func (b *Base) CloseChanIgnorePanic() {
	defer func() {
		if recover() != nil {
			b.parent.Ctx().Log().Errorln("panic by closing a closed channel, please review code.")
		}
	}()
	close(b.finishChan)
}

//如果channel已经关闭，则init一个新的出来
func (b *Base) InitFinishChanIfClosed() {
	select {
	case _, ok := <-b.finishChan:
		if !ok {
			b.finishChan = make(chan struct{})
		}
	default:
	}
}

//等待结束
func (b *Base) WaitFinishChan() {
	<-b.finish<PERSON>han
}

//获得Uuid
func (b *Base) GetUuid() string {
	return b.Uuid
}

func (b *Base) SetOccupied() {
	atomic.StoreInt32(&b.occupied, 1)
}

func (b *Base) SetUnOccupied() {
	atomic.StoreInt32(&b.occupied, 0)
}

func (b *Base) IsOccupied() bool {
	return atomic.LoadInt32(&b.occupied) == 1
}
