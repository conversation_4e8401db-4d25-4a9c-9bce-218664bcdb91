/**
 * @note
 * base.go
 *
 * <AUTHOR>
 * @date 	2019-11-07
 */
package workflow

import (
	"gitlab.docsl.com/security/bpm/internal/common"
	"time"

	. "gitlab.docsl.com/security/bpm/pkg/common"
)

type NodeBase struct {
	Base
	Table *NodeTable
}

func InitNodeBase(op Operator, uuid string) (nb NodeBase, err error) {
	nb = NodeBase{
		Base: Base{
			Uuid:       uuid,
			finishChan: make(chan struct{}),
		},
	}
	nb.WorkflowModel, err = NewWorkflowModel(op, false)
	return nb, err
}

// 成功获得锁后开始心跳
func (nb *NodeBase) Pulse() {
	nb.mu.Lock()
	defer nb.mu.Unlock()
	rowsAffected, err := nb.UpdateNodePulseTimeByUuid(nb.Uuid, time.Now(), nb.currentVersion)
	if err != nil || rowsAffected == 0 {
		nb.GetParent().Ctx().Log().Errorf("node [%s] pulse failed, rows affected [%d], error [%v]", nb.Uuid, rowsAffected, err)
	} else {
		nb.currentVersion += 1
	}
}

func (nb *NodeBase) HeartBeat() {
	ticker := time.NewTicker(time.Second * time.Duration(common.GetConfig().PulseInterval))
	for {
		select {
		case <-ticker.C:
			nb.Pulse()
		case <-nb.finishChan:
			return
		}
	}
}

// Load or reload
func (nb *NodeBase) Load() error {
	var err error
	nb.mu.Lock()
	defer nb.mu.Unlock()
	nb.Table, err = nb.QueryNodeRuntimeByUuid(nb.Uuid)
	if err != nil {
		return err
	} else {
		nb.currentVersion = nb.Table.Version
		return nil
	}
}

// when acquire lock failed，wait for node finish
func (nb *NodeBase) Wait() error {
	ticker := time.NewTicker(time.Second * time.Duration(common.GetConfig().WaitInterval))
	timeoutTicker := time.NewTicker(time.Second * time.Duration(common.GetConfig().WaitTimeout))
	var err error
	lastRetryTimes := common.GetConfig().WaitRetryTimes
	for {
		select {
		case <-ticker.C:
			nb.parent.Ctx().Log().Infof("waiting other goroutine processing node [%s]...", nb.Uuid)
			if nb.IsOccupied() { //if current node has occupied the lock, just wait finish chan
				continue
			}
			err = nb.Load()
			if err != nil {
				if lastRetryTimes <= 0 {
					nb.parent.Ctx().Log().Errorf("load node [%s] error [%v]", nb.Uuid, err)
					return err
				}
				lastRetryTimes--
				continue
			}
			lastRetryTimes = common.GetConfig().WaitRetryTimes //reset retry times
			if nb.State() != common.STATE_EXECUTING {
				nb.parent.Ctx().Log().Infof("finish waiting node [%s], result [%s]", nb.Uuid, nb.State())
				return nil
			}
			//if timeout, make it ready
			if time.Now().Unix()-nb.Table.PulseTime > common.GetConfig().PulseTimeout {
				nb.parent.Ctx().Log().Infof("waiting node [%s], find it pulse timeout, try to fix it", nb.Uuid)
				nb.UpdateTimeoutExecutingNodeToReady(nb.Uuid, common.GetConfig().PulseTimeout, nb.currentVersion)
			}
		case <-nb.finishChan:
			return nil
		case <-timeoutTicker.C:
			return common.ErrWaitTimeout
		}
	}
}

// Ready->Executing, acquire lock
func (nb *NodeBase) Begin() error {
	nb.InitFinishChanIfClosed()
	err := nb.transferState(common.STATE_READY, common.STATE_EXECUTING)
	defer func() {
		if err != nil {
			nb.CloseChanIgnorePanic()
		} else { //begin heartbeat
			nb.SetOccupied()
			go func() {
				nb.HeartBeat()
			}()
		}
	}()
	return err
}

// Executing->Pending
func (nb *NodeBase) Pend() error {
	defer nb.CloseChanIgnorePanic()
	defer nb.SetUnOccupied()
	return nb.transferState(common.STATE_EXECUTING, common.STATE_PENDING)
}

// Pending->Executing, acquire lock
func (nb *NodeBase) Continue() error {
	nb.InitFinishChanIfClosed()
	err := nb.transferState(common.STATE_PENDING, common.STATE_EXECUTING)
	defer func() {
		if err != nil {
			nb.CloseChanIgnorePanic()
		} else { //begin heartbeat
			nb.SetOccupied()
			go func() {
				nb.HeartBeat()
			}()
		}
	}()
	return err
}

// Executing->Finish
func (nb *NodeBase) Finish() error {
	defer nb.CloseChanIgnorePanic()
	defer nb.SetUnOccupied()
	return nb.transferState(common.STATE_EXECUTING, common.STATE_FINISHED)
}

// Executing->Failed
func (nb *NodeBase) Fail() error {
	defer nb.CloseChanIgnorePanic()
	defer nb.SetUnOccupied()
	return nb.transferState(common.STATE_EXECUTING, common.STATE_FAILED)
}

// Ready->Discard
func (nb *NodeBase) Discard() error {
	if state := nb.State(); state == common.STATE_DISCARD ||
		state == common.STATE_FAILED ||
		state == common.STATE_EXECUTING {
		return common.ErrInvalidNodeState
	} else {
		defer nb.CloseChanIgnorePanic()
		defer nb.SetUnOccupied()
		return nb.transferState(nb.Table.State, common.STATE_DISCARD)
	}
}

// Persist data, use data in Table.Data
func (nb *NodeBase) Persist() error {
	nb.mu.Lock()
	defer nb.mu.Unlock()
	now := time.Now().Unix()
	rowsAffected, err := nb.UpdateNodeRuntimeByWhereMap(map[string]interface{}{
		"data":       nb.Table.Data,
		"pulse_time": now,
	}, map[string]interface{}{
		"uuid": nb.Uuid,
	}, nb.currentVersion)
	if err != nil {
		return err
	} else if rowsAffected == 0 {
		return common.ErrPersistFailed
	} else {
		nb.Table.PulseTime = now
		nb.currentVersion += 1
		return nil
	}
}

func (nb *NodeBase) State() common.WorkflowStateEnum {
	nb.mu.RLock()
	defer nb.mu.RUnlock()
	return nb.Table.State
}

// stateTransfer
func (nb *NodeBase) transferState(oldState, newState common.WorkflowStateEnum) error {
	nb.mu.Lock()
	defer nb.mu.Unlock()
	now := time.Now().Unix()
	rowsAffected, err := nb.UpdateNodeRuntimeByWhereMap(map[string]interface{}{
		"state":      newState,
		"pulse_time": now,
	}, map[string]interface{}{
		"uuid":  nb.Uuid,
		"state": oldState,
	}, nb.currentVersion)
	if err != nil {
		return err
	} else if rowsAffected == 0 {
		return common.ErrTransferFailed
	} else {
		nb.Table.State = newState
		nb.Table.PulseTime = now
		nb.currentVersion += 1
		return nil
	}
}
