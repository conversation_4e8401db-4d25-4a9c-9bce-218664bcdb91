/**
 * @note
 * 模板组表
 *
 * <AUTHOR>
 * @date 	2019-12-04
 */
package workflow

import (
	"gitlab.docsl.com/security/bpm/internal/common"
)

// 在workflow_template_group表中插入一条模板组
func (this *WorkflowModel) InsertTemplateGroup(name, description common.I18nString,
	state common.WorkflowTmplGroupStateEnum, status common.StatusEnum) (int64, error) {
	db := this.ShardingTemplateGroupTable()
	newRow := &TemplateGroupTable{
		UserId:      this.GetParent().Ctx().User().UserId,
		Name:        name.Marshal(),
		Description: description.Marshal(),
		State:       state,
		Status:      status,
	}
	db = db.Create(newRow)
	return newRow.ID, db.Error
}

func (this *WorkflowModel) DeleteTemplateGroupById(templateGroupIds []int64) error {
	this.gormDB = this.gormDB.Begin() //开事务
	tx := this.ShardingWorkflowTemplateTable()
	txTmp := tx.Where("group_id in (?) ", templateGroupIds).
		Updates(map[string]interface{}{
			"group_id": 0,
		})
	if txTmp.Error != nil {
		tx.Rollback()
		return txTmp.Error
	}

	tx = this.ShardingTemplateGroupTable()
	tx = tx.Where("id in (?) ", templateGroupIds).
		Updates(map[string]interface{}{
			"status": common.STATUS_DELETED,
		})
	if tx.Error != nil {
		tx.Rollback()
		return tx.Error
	}
	tx.Commit()
	return nil
}

// 启用模板组
func (this *WorkflowModel) EnableTemplateGroup(templateGroupIds []int64) error {
	db := this.ShardingTemplateGroupTable()
	db = db.Where("id in (?)", templateGroupIds).
		Updates(map[string]interface{}{
			"state": common.GROUP_STATE_ENABLED,
		})
	return db.Error
}

// 禁用模板组
func (this *WorkflowModel) DisableTemplateGroup(templateGroupIds []int64) error {
	db := this.ShardingTemplateGroupTable()
	db = db.Where("id in (?)", templateGroupIds).
		Updates(map[string]interface{}{
			"state": common.GROUP_STATE_DISABLED,
		})
	return db.Error
}

func (this *WorkflowModel) UpdateTemplateGroupNameAndDesc(groupId int64, name, description common.I18nString) error {
	db := this.ShardingTemplateGroupTable()
	m := make(map[string]interface{})
	if name != nil {
		m["name"] = name.Marshal()
	}
	if description != nil {
		m["description"] = description.Marshal()
	}
	db = db.Where("id = ?", groupId).Updates(m)
	return db.Error
}

func (this *WorkflowModel) UpdateTemplateGroupTemplates(groupId int64, workflowTmplUuids []string) error {
	db := this.ShardingWorkflowTemplateTable()
	//删掉原来的模板
	db = db.Where("group_id = ?", groupId).Updates(map[string]interface{}{
		"group_id": 0,
	})
	//加上传入的模板
	db = db.Where("uuid in (?)", workflowTmplUuids).Updates(map[string]interface{}{
		"group_id": groupId,
	})
	return db.Error
}

func (this *WorkflowModel) QueryTemplateGroupByConditions(templateGroupName string, groupIds []int64, offset, limit int64) (
	[]*TemplateGroupTable, int64, error) {
	db := this.ShardingTemplateGroupTable()
	templateGroupInfos := []*TemplateGroupTable{}
	if len(templateGroupName) > 0 {
		db = db.Where(`name->'$."zh-cn"' like ?`, "%"+templateGroupName+"%")
	}
	if len(groupIds) > 0 {
		db = db.Where("id in (?)", groupIds)
	}
	db = db.Where("status = ? ", common.STATUS_NORMAL)

	var totalCount int64
	if err := db.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	db.Order("create_time DESC").Offset(int(offset)).Limit(int(limit)).Find(&templateGroupInfos)
	return templateGroupInfos, totalCount, db.Error
}

func (this *WorkflowModel) QueryTemplateGroupByGroupId(groupId int64) (*TemplateGroupTable, error) {
	templateGroupInfo := &TemplateGroupTable{}
	db := this.ShardingTemplateGroupTable()
	db = db.Where("id = ? and status = ? ", groupId, common.STATUS_NORMAL).First(&templateGroupInfo)
	return templateGroupInfo, db.Error
}

func (this *WorkflowModel) QueryTemplateGroupByGroupIds(groupIds []int64) ([]*TemplateGroupTable, error) {
	if len(groupIds) == 0 {
		return nil, nil
	}
	templateGroupInfos := []*TemplateGroupTable{}
	db := this.ShardingTemplateGroupTable()
	db = db.Where("id in (?) and status = ? ", groupIds, common.STATUS_NORMAL).Find(&templateGroupInfos)
	return templateGroupInfos, db.Error
}

func (this *WorkflowModel) QueryTemplateGroupCntForPaging(templateGroupName string, groupIds []int64) (int64, error) {
	var cnt int64
	db := this.ShardingTemplateGroupTable()
	if len(templateGroupName) > 0 {
		db = db.Where(`name->'$."zh-cn"' like ?`, "%"+templateGroupName+"%")
	}
	if len(groupIds) > 0 {
		db = db.Where("id in (?)", groupIds)
	}
	db.Where("status = ? ", common.STATUS_NORMAL).Count(&cnt)
	return cnt, db.Error
}

func (this *WorkflowModel) GetTemplateGroupByIdAndName(templateGroupID int64, templateGroupName string) (
	*TemplateGroupTable, error) {
	db := this.ShardingTemplateGroupTable()
	var templateGroupInfo *TemplateGroupTable
	db = db.Where("status = ? and id = ? ", common.STATUS_NORMAL, templateGroupID).
		Where(`name->'$."zh-cn"' like ?`, "%"+templateGroupName+"%").
		Find(&templateGroupInfo)
	return templateGroupInfo, db.Error
}
