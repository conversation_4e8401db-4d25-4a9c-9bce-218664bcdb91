/**
 * @note
 * node的DAO
 *
 * <AUTHOR>
 * @date 	2019-10-30
 */
package workflow

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	"time"

	"gorm.io/gorm"
)

// 插入node记录
func (this *WorkflowModel) insertNode(db *gorm.DB, templateUuid,
	uuid, parentUuid, name string, nodeType bpmCom.NodeTypeEnum,
	detail, front, data string, state bpmCom.WorkflowStateEnum, status bpmCom.StatusEnum) (id int64, err error) {
	newRow := &NodeTable{
		TemplateUuid: templateUuid,
		Uuid:         uuid,
		ParentUuid:   parentUuid,
		Name:         name,
		Type:         nodeType,
		Detail:       detail,
		Front:        front,
		Data:         data,
		State:        state,
		Status:       status,
	}
	db = db.Create(newRow)
	return newRow.ID, db.Error
}

func (this *WorkflowModel) BatchUpdateApprovalProcessAndNodeData(processUuid, processData string, processState bpmCom.ProcessStateEnum,
	nodeUuid, nodeData, nodeDetail string, otherUnApprovedProcessUuids []string, otherProcessState bpmCom.ProcessStateEnum) (err error) {

	this.gormDB = this.gormDB.Begin() //开事务
	tx := this.ShardingProcessRuntimeTable()
	//更新当前流程
	txTmp := tx.Where(map[string]interface{}{
		"user_id": this.parent.Ctx().User().UserId,
		"uuid":    processUuid,
		"state":   bpmCom.PROCESS_STATE_UNAPPROVED,
	}).Updates(map[string]interface{}{
		"data":         processData,
		"state":        processState,
		"approve_time": time.Now().UnixNano(),
	})

	if txTmp.Error != nil {
		tx.Rollback()
		return tx.Error
	}
	//更新其他的流程
	if len(otherUnApprovedProcessUuids) > 0 && len(otherProcessState) > 0 {
		for _, otherUuid := range otherUnApprovedProcessUuids {
			txTmp := tx.Where(map[string]interface{}{
				"uuid": otherUuid,
				//"state": bpmCom.PROCESS_STATE_UNAPPROVED,
				//"state": []bpmCom.ProcessStateEnum{bpmCom.PROCESS_STATE_UNAPPROVED, bpmCom.PROCESS_STATE_INITIALIZED},
			}).Updates(map[string]interface{}{
				"state":        otherProcessState,
				"approve_time": time.Now().UnixNano(),
			})
			if txTmp.Error != nil {
				tx.Rollback()
				return tx.Error
			}
		}
	}
	//更新节点的数据
	tx = this.ShardingNodeRuntimeTable()
	txTmp = tx.Where(map[string]interface{}{
		"uuid":  nodeUuid,
		"state": bpmCom.STATE_PENDING,
	}).Updates(map[string]interface{}{
		"data":   nodeData,
		"detail": nodeDetail,
	})
	if txTmp.Error != nil {
		tx.Rollback()
		return txTmp.Error
	}
	tx.Commit()
	return nil
}

func (this *WorkflowModel) BatchUpdateTransactorProcessAndNodeData(processUuid, processData string, processState bpmCom.ProcessStateEnum,
	nodeUuid, nodeData, nodeDetail string, otherUnApprovedProcessUuids []string, otherProcessState bpmCom.ProcessStateEnum,
	otherInitializedProcessUuids []string) (err error) {

	this.gormDB = this.gormDB.Begin() //开事务
	tx := this.ShardingProcessRuntimeTable()
	//更新当前流程
	txTmp := tx.Where(map[string]interface{}{
		"user_id": this.parent.Ctx().User().UserId,
		"uuid":    processUuid,
		"state":   bpmCom.PROCESS_STATE_UNDONE,
	}).Updates(map[string]interface{}{
		"data":         processData,
		"state":        processState,
		"approve_time": time.Now().UnixNano(),
	})
	if txTmp.Error != nil {
		tx.Rollback()
		return tx.Error
	}
	//更新其他的流程
	if len(otherUnApprovedProcessUuids) > 0 && len(otherProcessState) > 0 {
		for _, otherUuid := range otherUnApprovedProcessUuids {
			txTmp := tx.Where(map[string]interface{}{
				"uuid":  otherUuid,
				"state": bpmCom.PROCESS_STATE_UNDONE,
			}).Updates(map[string]interface{}{
				"state":        otherProcessState,
				"approve_time": time.Now().UnixNano(),
			})
			if txTmp.Error != nil {
				tx.Rollback()
				return tx.Error
			}
		}
	}
	if len(otherInitializedProcessUuids) > 0 {
		for _, otherUuid := range otherInitializedProcessUuids {
			txTmp := tx.Where(map[string]interface{}{
				"uuid":  otherUuid,
				"state": bpmCom.PROCESS_STATE_INITIALIZED,
			}).UpdateColumn("state", bpmCom.PROCESS_STATE_EXPIRED)
			if txTmp.Error != nil {
				tx.Rollback()
				return tx.Error
			}
		}
	}

	//更新节点的数据
	tx = this.ShardingNodeRuntimeTable()
	txTmp = tx.Where(map[string]interface{}{
		"uuid":  nodeUuid,
		"state": bpmCom.STATE_PENDING,
	}).Updates(map[string]interface{}{
		"data":   nodeData,
		"detail": nodeDetail,
	})
	if txTmp.Error != nil {
		tx.Rollback()
		return txTmp.Error
	}
	tx.Commit()
	return nil
}

func (this *WorkflowModel) BatchRollbackProcessAndNodeAndWorkflow(needUpdateProcesses []*processModel.ProcessTable,
	needUpdateNodes []*NodeTable, needCreateProcess *processModel.ProcessTable) (err error) {
	this.gormDB = this.gormDB.Begin() //开事务
	tx := this.ShardingNodeRuntimeTable()
	for _, node := range needUpdateNodes {
		txTmp := tx.Where("uuid = ?", node.Uuid).Updates(map[string]interface{}{
			"state": node.State,
			"data":  node.Data,
		})
		if txTmp.Error != nil {
			tx.Rollback()
			return txTmp.Error
		}
	}
	//再插入process
	tx = this.ShardingProcessRuntimeTable()
	for _, proc := range needUpdateProcesses {
		txTmp := tx.Where("uuid = ?", proc.Uuid).Updates(map[string]interface{}{
			"state":        proc.State,
			"data":         proc.Data,
			"approve_time": time.Now().UnixNano(),
		})
		if txTmp.Error != nil {
			tx.Rollback()
			return txTmp.Error
		}
	}
	tx = tx.Create(needCreateProcess)
	if tx.Error != nil {
		tx.Rollback()
		return tx.Error
	}
	tx.Commit()
	return tx.Error
}

func (this *WorkflowModel) BatchNeedCompleteProcess(needUpdateProcesses []*processModel.ProcessTable,
	needCreateProcess *processModel.ProcessTable) (err error) {
	this.gormDB = this.gormDB.Begin() //开事务
	//插入新process
	tx := this.ShardingProcessRuntimeTable()
	for _, proc := range needUpdateProcesses {
		txTmp := tx.Where("uuid = ?", proc.Uuid).Updates(map[string]interface{}{
			"state":        proc.State,
			"data":         proc.Data,
			"approve_time": time.Now().UnixNano(),
		})
		if txTmp.Error != nil {
			tx.Rollback()
			return txTmp.Error
		}
	}
	tx = tx.Create(needCreateProcess)
	if tx.Error != nil {
		tx.Rollback()
		return tx.Error
	}
	tx.Commit()
	return tx.Error
}

func (this *WorkflowModel) UpdateNodeRuntimeData(nodeUuid, data string) (err error) {
	db := this.ShardingNodeRuntimeTable()
	db = db.Where("uuid = ?", nodeUuid).Updates(map[string]interface{}{
		"data": data,
	})
	return db.Error
}

func (this *WorkflowModel) UpdateNodeRuntimeDetail(nodeUuid, detail string) (err error) {
	db := this.ShardingNodeRuntimeTable()
	db = db.Where("uuid = ?", nodeUuid).Updates(map[string]interface{}{
		"detail": detail,
	})
	return db.Error
}

// 在运行时node表中插入node
func (this *WorkflowModel) InsertNodeRuntime(templateUuid,
	uuid, parentUuid, name string, nodeType bpmCom.NodeTypeEnum,
	detail, front, data string, state bpmCom.WorkflowStateEnum, status bpmCom.StatusEnum) (id int64, err error) {
	db := this.ShardingNodeRuntimeTable()
	return this.insertNode(db, templateUuid, uuid, parentUuid, name, nodeType, detail, front, data, state, status)
}

// 在持久化node表中插入node
func (this *WorkflowModel) InsertNodePersistence(templateUuid,
	uuid, parentUuid, name string, nodeType bpmCom.NodeTypeEnum,
	detail, front, data string, state bpmCom.WorkflowStateEnum, status bpmCom.StatusEnum) (id int64, err error) {
	db := this.ShardingNodePersistenceTable()
	return this.insertNode(db, templateUuid, uuid, parentUuid, name, nodeType, detail, front, data, state, status)
}

// 软删除node表
func (this *WorkflowModel) softDeleteNodeByUuid(db *gorm.DB, uuids ...string) (rowsAffected int64, err error) {
	db = db.Where("uuid in (?) AND state != ?", uuids, bpmCom.STATE_EXECUTING).Updates(map[string]interface{}{
		"status": bpmCom.STATUS_DELETED,
	})
	return db.RowsAffected, db.Error
}

// 在运行时node表中将状态置为Discard
func (this *WorkflowModel) WithdrawNodeRuntimeByWorkflowUuids(workflowUuids ...string) error {
	db := this.ShardingNodeRuntimeTable()
	db = db.Where("parent_uuid in (?) AND state != ?", workflowUuids, bpmCom.STATE_EXECUTING).Updates(map[string]interface{}{
		"state": bpmCom.STATE_DISCARD,
	})
	return db.Error
}

// 在持久化node表中软删除node
func (this *WorkflowModel) SoftDeleteNodePersistenceByUuid(uuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingNodePersistenceTable()
	return this.softDeleteWorkflowByUuid(db, uuids...)
}

// 在运行时node表中软删除node
func (this *WorkflowModel) SoftDeleteNodeRuntimeByUuid(uuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingNodeRuntimeTable()
	return this.softDeleteNodeByUuid(db, uuids...)
}

// 硬删除运行时node，（仅用于运行时node表）
func (this *WorkflowModel) HardDeleteNodeRuntimeByUuid(uuids string) (rowsAffected int64, err error) {
	db := this.ShardingNodeRuntimeTable()
	db = db.Exec("DELETE FROM "+NODE_RUNTIME_TABLE_NAME+" WHERE uuid in (?) AND state != ?", uuids, bpmCom.STATE_EXECUTING)
	return db.RowsAffected, db.Error
}

// 根据父uuid软删除node表
func (this *WorkflowModel) softDeleteNodeByParentUuid(db *gorm.DB, parentUuids ...string) (rowsAffected int64, err error) {
	db = db.Where("parent_uuid in (?) AND state != ?", parentUuids, bpmCom.STATE_EXECUTING).
		Updates(map[string]interface{}{
			"status": bpmCom.STATUS_DELETED,
		})
	return db.RowsAffected, db.Error
}

// 根据父uuid在持久化node表中软删除node
func (this *WorkflowModel) SoftDeleteNodePersistenceByParentUuid(parentUuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingNodePersistenceTable()
	return this.softDeleteWorkflowByUuid(db, parentUuids...)
}

// 根据父uuid在运行时node表中软删除node
func (this *WorkflowModel) SoftDeleteNodeRuntimeByParentUuid(parentUuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingNodeRuntimeTable()
	return this.softDeleteWorkflowByUuid(db, parentUuids...)
}

// 根据父uuid硬删除运行时node，（仅用于运行时node表）
func (this *WorkflowModel) HardDeleteNodeRuntimeByParentUuid(parentUuids string) (rowsAffected int64, err error) {
	db := this.ShardingNodeRuntimeTable()
	db = db.Exec("DELETE FROM "+NODE_RUNTIME_TABLE_NAME+" WHERE parent_uuid in (?) AND state != ?", parentUuids, bpmCom.STATE_EXECUTING)
	return db.RowsAffected, db.Error
}

func (this *WorkflowModel) updateNodeByWhereMap(db *gorm.DB, updateMap, whereMap map[string]interface{}, version int64) (rowsAffected int64, err error) {
	whereMap["version"] = version
	updateMap["version"] = version + 1
	db = db.Where(whereMap).Updates(updateMap)
	return db.RowsAffected, db.Error
}

func (this *WorkflowModel) UpdateNodeRuntimeByWhereMap(updateMap, whereMap map[string]interface{}, version int64) (rowsAffected int64, err error) {
	db := this.ShardingNodeRuntimeTable()
	return this.updateNodeByWhereMap(db, updateMap, whereMap, version)
}

func (this *WorkflowModel) UpdateNodePersistenceByWhereMap(updateMap, whereMap map[string]interface{}, version int64) (rowsAffected int64, err error) {
	db := this.ShardingNodePersistenceTable()
	return this.updateNodeByWhereMap(db, updateMap, whereMap, version)
}

func (this *WorkflowModel) queryNodeByParentUuid(db *gorm.DB, parentUuid string) (nodes []*NodeTable, err error) {
	nodes = []*NodeTable{}
	db = db.Where(map[string]interface{}{
		"parent_uuid": parentUuid,
		"status":      bpmCom.STATUS_NORMAL,
	}).Find(&nodes)
	return nodes, db.Error
}

func (this *WorkflowModel) QueryNodeRuntimeByParentUuid(parentUuid string) (nodes []*NodeTable, err error) {
	db := this.ShardingNodeRuntimeTable()
	return this.queryNodeByParentUuid(db, parentUuid)
}

func (this *WorkflowModel) QueryNodePersistenceByParentUuid(parentUuid string) (nodes []*NodeTable, err error) {
	db := this.ShardingNodePersistenceTable()
	return this.queryNodeByParentUuid(db, parentUuid)
}

func (this *WorkflowModel) QueryNodeRuntimeByParentUuidsAndState(parentUuids []string,
	state bpmCom.WorkflowStateEnum) (nodes []*NodeTable, err error) {
	db := this.ShardingNodeRuntimeTable()
	nodes = []*NodeTable{}
	db = db.Where(map[string]interface{}{
		"status": bpmCom.STATUS_NORMAL,
		"state":  state,
	}).Where("parent_uuid in (?)", parentUuids).Find(&nodes)
	return nodes, db.Error
}

func (this *WorkflowModel) QueryNodeRuntimeByParentUuidsAndType(parentUuids []string,
	nodeType bpmCom.NodeTypeEnum) (nodes []*NodeTable, err error) {
	db := this.ShardingNodeRuntimeTable()
	nodes = []*NodeTable{}
	db = db.Where(map[string]interface{}{
		"status": bpmCom.STATUS_NORMAL,
		"type":   nodeType,
	}).Where("parent_uuid in (?)", parentUuids).Find(&nodes)
	return nodes, db.Error
}

func (this *WorkflowModel) queryNodeByUuids(db *gorm.DB, nodeUuids []string) (nodes []*NodeTable, err error) {
	nodes = []*NodeTable{}
	db = db.Where("uuid in (?)", nodeUuids).Where(map[string]interface{}{
		"status": bpmCom.STATUS_NORMAL,
	}).Find(&nodes)
	return nodes, db.Error
}

func (this *WorkflowModel) QueryNodeRuntimeByUuids(nodeUuids []string) (nodes []*NodeTable, err error) {
	db := this.ShardingNodeRuntimeTable()
	return this.queryNodeByUuids(db, nodeUuids)
}

func (this *WorkflowModel) queryNodeByUuid(db *gorm.DB, uuid string) (node *NodeTable, err error) {
	node = &NodeTable{}
	db = db.Where(map[string]interface{}{
		"uuid":   uuid,
		"status": bpmCom.STATUS_NORMAL,
	}).First(&node)
	return node, db.Error
}

func (this *WorkflowModel) QueryNodeRuntimeByUuid(uuid string) (node *NodeTable, err error) {
	db := this.ShardingNodeRuntimeTable()
	return this.queryNodeByUuid(db, uuid)
}

func (this *WorkflowModel) QueryNodePersistenceByUuid(uuid string) (node *NodeTable, err error) {
	db := this.ShardingNodePersistenceTable()
	return this.queryNodeByUuid(db, uuid)
}

func (this *WorkflowModel) QueryExecutingNodeByPulseTimeout(pulseTimeout int) (nodes []*NodeTable, err error) {
	db := this.ShardingNodeRuntimeTable()
	nodes = []*NodeTable{}
	db = db.Where(map[string]interface{}{
		"state":  bpmCom.STATE_EXECUTING,
		"status": bpmCom.STATUS_NORMAL,
	}).Where("unix_timestamp(now()) - pulse_time > ?", pulseTimeout).Find(&nodes)
	return nodes, db.Error
}

func (this *WorkflowModel) UpdateTimeoutExecutingNodeToReady(uuid string,
	pulseTimeout int64, version int64) (rowsAffected int64, err error) {
	db := this.ShardingNodeRuntimeTable()
	db = db.Where(
		map[string]interface{}{
			"uuid":    uuid,
			"version": version,
			"state":   bpmCom.STATE_EXECUTING,
		}).Where("unix_timestamp(now()) - pulse_time > ?", pulseTimeout).
		Updates(map[string]interface{}{
			"pulse_time": time.Now().Unix(),
			"version":    version + 1,
			"state":      bpmCom.STATE_READY,
		})
	return db.RowsAffected, db.Error
}

// Node心跳
func (this *WorkflowModel) UpdateNodePulseTimeByUuid(uuid string, pulseTime time.Time,
	version int64) (rowsAffected int64, err error) {
	db := this.ShardingNodeRuntimeTable()
	db = db.Where(
		map[string]interface{}{
			"uuid":    uuid,
			"version": version,
			"state":   bpmCom.STATE_EXECUTING,
		}).Updates(map[string]interface{}{
		"pulse_time": pulseTime.Unix(),
		"version":    version + 1,
	})
	return db.RowsAffected, db.Error
}
