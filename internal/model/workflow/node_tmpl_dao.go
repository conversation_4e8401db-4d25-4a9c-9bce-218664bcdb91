/**
 * @note
 * node_tmpl_dao
 *
 * <AUTHOR>
 * @date 	2019-11-26
 */
package workflow

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

// 在node_template表中插入一条node模板
func (this *WorkflowModel) InsertNodeTemplate(name, parentUuid string, nodeType bpmCom.NodeTypeEnum,
	detail, front string, status bpmCom.StatusEnum) (string, error) {
	db := this.ShardingNodeTemplateTable()
	uuid := common.NewUuid(common.RESOURCE_TYPE_NODE_TEMPLATE)
	newRow := &NodeTemplateTable{
		Uuid:       uuid,
		ParentUuid: parentUuid,
		Name:       name,
		Type:       nodeType,
		Detail:     detail,
		Front:      front,
		Status:     status,
	}
	db = db.Create(newRow)
	return uuid, db.Error
}

func (this *WorkflowModel) QueryNodeTemplatesByUuids(uuids []string) ([]*NodeTemplateTable, error) {
	nodeTables := []*NodeTemplateTable{}
	db := this.ShardingNodeTemplateTable()
	db = db.Where("uuid in (?) ", uuids).Where("status = ?", bpmCom.STATUS_NORMAL).Find(&nodeTables)
	return nodeTables, db.Error
}
