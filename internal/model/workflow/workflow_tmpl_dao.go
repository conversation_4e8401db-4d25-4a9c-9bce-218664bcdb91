/**
 * @note
 * workflow_tmpl_dao
 *
 * <AUTHOR>
 * @date 	2019-11-26
 */
package workflow

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/internal/model/tag"
	"strings"

	"github.com/jianfengye/collection"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

// 在workflow_template表中插入workflow模板
func (this *WorkflowModel) InsertWorkflowTemplate(groupId int64, uuid, name, description,
	detail, edition string, origin bpmCom.UserOriginEnum, state bpmCom.WorkflowTemplateStateEnum, status bpmCom.StatusEnum) (int64, error) {
	db := this.ShardingWorkflowTemplateTable()
	newRow := &WorkflowTemplateTable{
		UserId:      this.GetParent().Ctx().User().UserId,
		Uuid:        uuid,
		Name:        name,
		Description: description,
		GroupId:     groupId,
		Detail:      detail,
		Origin:      origin,
		State:       state,
		Status:      status,
		Edition:     edition,
	}
	db = db.Create(newRow)
	return newRow.ID, db.Error
}

// 在事务中批量插入node模板和workflow模板
func (this *WorkflowModel) BatchInsertWorkflowAndNodeTemplate(nodes []*NodeTemplateTable,
	staff string, groupId int64, uuid, name, description, detail, edition string, origin bpmCom.UserOriginEnum,
	state bpmCom.WorkflowTemplateStateEnum, status bpmCom.StatusEnum, tags WorkflowTemplateTags) (*WorkflowTemplateTable, error) {
	//先插入node_template
	this.gormDB = this.gormDB.Begin() //开事务
	tx := this.ShardingNodeTemplateTable()
	for _, node := range nodes {
		txTmp := tx.Create(node)
		if txTmp.Error != nil {
			tx.Rollback()
			return nil, txTmp.Error
		}
	}

	//再插入workflow_template
	tx = this.ShardingWorkflowTemplateTable()
	newRow := &WorkflowTemplateTable{}
	assignMap := map[string]interface{}{
		"user_id":     this.GetParent().Ctx().User().UserId,
		"uuid":        uuid,
		"name":        name,
		"description": description,
		"staff":       staff,
		"group_id":    groupId,
		"detail":      detail,
		"origin":      origin,
		"state":       state,
		"status":      status,
		"tags":        tags,
		"edition":     edition,
	}
	tx = tx.Where(
		map[string]interface{}{
			"uuid":    uuid,
			"edition": edition,
		}).Assign(assignMap).FirstOrCreate(newRow)
	if tx.Error != nil {
		tx.Rollback()
		return nil, tx.Error
	}
	tx.Commit()
	return newRow, nil
}

func (this *WorkflowModel) DeleteNodeTemplateByNodeUuids(nodeUuids []string) error {
	//先删除node_template
	db := this.ShardingNodeTemplateTable()
	db = db.Where("uuid in (?) ", nodeUuids).
		Updates(map[string]interface{}{
			"status": bpmCom.STATUS_DELETED,
		})
	return db.Error
}

// 在事务中批量删除node模板和workflow模板
func (this *WorkflowModel) BatchDeleteStagingWorkflowAndNodeTemplate(nodeUuids []string, workflowTemplateUuid string) error {
	//先删除node_template
	this.gormDB = this.gormDB.Begin() //开事务
	tx := this.ShardingNodeTemplateTable()
	txTmp := tx.Where("uuid in (?) ", nodeUuids).
		Updates(map[string]interface{}{
			"status": bpmCom.STATUS_DELETED,
		})
	if txTmp.Error != nil {
		tx.Rollback()
		return txTmp.Error
	}

	tx = this.ShardingWorkflowTemplateTable()
	//再删除workflow_template
	tx = tx.Where("uuid = ? and state = ? ", workflowTemplateUuid, bpmCom.STATE_STAGING).
		Updates(map[string]interface{}{
			"status": bpmCom.STATUS_DELETED,
		})
	if tx.Error != nil {
		tx.Rollback()
		return tx.Error
	}
	tx.Commit()
	return nil
}

// 批量软删除workflow模板的所有版本
func (this *WorkflowModel) DeleteAllVersionsOfWorkflowAndNodeTemplate(workflowTemplateUuid string) error {
	this.gormDB = this.gormDB.Begin()
	tx := this.ShardingNodeTemplateTable()
	txTmp := tx.Where("parent_uuid = ? ", workflowTemplateUuid).
		Updates(map[string]interface{}{
			"status": bpmCom.STATUS_DELETED,
		})
	//txTmp := tx.Exec("DELETE FROM "+NODE_TEMPLATE_TABLE_NAME+" WHERE parent_uuid = ? ", workflowTemplateUuid)
	if txTmp.Error != nil {
		tx.Rollback()
		return txTmp.Error
	}

	tx = this.ShardingWorkflowTemplateTable()
	tx = tx.Where("uuid = ? ", workflowTemplateUuid).
		Updates(map[string]interface{}{
			"status": bpmCom.STATUS_DELETED,
		})
	//tx = tx.Exec("DELETE FROM "+WORKFLOW_TEMPLATE_TABLE_NAME+" WHERE uuid = ? ", workflowTemplateUuid)
	if tx.Error != nil {
		tx.Rollback()
		return tx.Error
	}
	tx.Commit()
	return nil
}

// 根据uuid和版本号查询模板
func (this *WorkflowModel) QueryWorkflowTemplate(workflowTemplateUuid string) (
	workflowTemplateTables []*WorkflowTemplateTable, err error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTables = []*WorkflowTemplateTable{}
	db = db.Where("uuid = ?", workflowTemplateUuid).Where("status = ?", bpmCom.STATUS_NORMAL).Find(&workflowTemplateTables)
	return workflowTemplateTables, db.Error
}

// 根据workflow模板的非删除状态的所有版本号
func (this *WorkflowModel) QueryWorkflowTemplateNormalVersions(workflowTemplateUuid string) (versions []string, err error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTables := []*WorkflowTemplateTable{}
	db = db.Select("edition").Where(map[string]interface{}{
		"uuid":   workflowTemplateUuid,
		"status": bpmCom.STATUS_NORMAL,
	}).Find(&workflowTemplateTables)
	for _, workflowTemplate := range workflowTemplateTables {
		versions = append(versions, workflowTemplate.Edition)
	}
	return versions, db.Error
}

// 根据workflow模板的所有版本号
func (this *WorkflowModel) QueryWorkflowTemplateAllVersions(workflowTemplateUuid string) (versions []string, err error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTables := []*WorkflowTemplateTable{}
	db = db.Select("edition").Where(map[string]interface{}{
		"uuid": workflowTemplateUuid,
	}).Find(&workflowTemplateTables)
	for _, workflowTemplate := range workflowTemplateTables {
		if len(workflowTemplate.Edition) == 0 { //忽略草稿状态
			continue
		}
		versions = append(versions, workflowTemplate.Edition)
	}
	return versions, db.Error
}

// 根据uuid和版本号查询模板
func (this *WorkflowModel) QueryWorkflowTemplateByUuidAndVersion(workflowTemplateUuid, version string, staging bool) (
	WorkflowTemplateTable, error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTable := WorkflowTemplateTable{}
	db = db.Where(map[string]interface{}{
		"uuid":   workflowTemplateUuid,
		"status": bpmCom.STATUS_NORMAL,
	})
	if staging {
		db = db.Where("state = ?", bpmCom.STATE_STAGING)
	} else if version != common.StringEmpty {
		db = db.Where("edition = ? ", version)
	}
	db = db.Order("LENGTH(edition) DESC, edition DESC").First(&workflowTemplateTable)
	return workflowTemplateTable, db.Error
}

// 根据uuid和版本号查询模板
func (this *WorkflowModel) QueryStagingWorkflowTemplateByUuid(workflowTemplateUuid string) (
	WorkflowTemplateTable, error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTable := WorkflowTemplateTable{}
	db = db.Where(map[string]interface{}{
		"uuid":   workflowTemplateUuid,
		"status": bpmCom.STATUS_NORMAL,
		"state":  bpmCom.STATE_STAGING,
	}).First(&workflowTemplateTable)
	return workflowTemplateTable, db.Error
}

// 根据uuid查询可用的模板
func (this *WorkflowModel) QueryEnabledWorkflowTemplateByUuid(workflowTemplateUuid string) (*WorkflowTemplateTable, error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTable := &WorkflowTemplateTable{}
	db = db.Where(map[string]interface{}{
		"uuid":   workflowTemplateUuid,
		"state":  bpmCom.STATE_ENABLED,
		"status": bpmCom.STATUS_NORMAL,
	})
	db = db.First(&workflowTemplateTable)
	return workflowTemplateTable, db.Error
}

// 根据uuid列表查询可用的模板
func (this *WorkflowModel) QueryEnabledWorkflowTemplateByUuidsAndOrigins(
	workflowTemplateUuids []string, origins []bpmCom.UserOriginEnum) ([]*WorkflowTemplateTable, error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTables := []*WorkflowTemplateTable{}
	m := map[string]interface{}{
		"state":  bpmCom.STATE_ENABLED,
		"status": bpmCom.STATUS_NORMAL,
	}
	if len(origins) > 0 {
		db = db.Where("origin in (?)", origins)
	}
	db = db.Where("uuid in (?)", workflowTemplateUuids).Where(m)
	db = db.Find(&workflowTemplateTables)
	return workflowTemplateTables, db.Error
}

// 根据uuid查询模板
func (this *WorkflowModel) QueryWorkflowTemplateByUuid(workflowTemplateUuid string) (*WorkflowTemplateTable, error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTable := &WorkflowTemplateTable{}
	db = db.Where(map[string]interface{}{
		"uuid":   workflowTemplateUuid,
		"status": bpmCom.STATUS_NORMAL,
	})
	db = db.First(&workflowTemplateTable)
	return workflowTemplateTable, db.Error
}

// 根据uuid列表查询模板列表
func (this *WorkflowModel) QueryWorkflowTemplateByUuids(workflowTemplateUuids []string) ([]*WorkflowTemplateTable, error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateTables := []*WorkflowTemplateTable{}
	db = db.Where("uuid in (?)", workflowTemplateUuids).
		Where(map[string]interface{}{
			"status": bpmCom.STATUS_NORMAL,
		})
	db = db.Find(&workflowTemplateTables)
	return workflowTemplateTables, db.Error
}

// 根据Conditon,Offset,Limit进行条件查询
func (this *WorkflowModel) QueryWorkflowTemplateByWhereMapWithOffsetAndLimit(whereMap map[string]interface{},
	allowGroupIds []int64, allowTemplateUuids []string, conditionIds []int64, conditionGroupIds []int64, conditionTemplateUuids []string, conditionTagIds []int64, noneTag bool, workflowTemplateName string, offset, limit int64) ([]*WorkflowTemplateTable, int64, error) {
	db := this.ShardingWorkflowTemplateTable()
	var workflowTemplateInfos []*WorkflowTemplateTable

	if workflowTemplateName != common.StringEmpty {
		db = db.Where(`name->'$."zh-cn"' like ?`, "%"+workflowTemplateName+"%")
	}
	db = db.Where(whereMap)
	if len(allowGroupIds) > 0 && len(allowTemplateUuids) > 0 {
		db = db.Where("group_id in (?) or uuid in (?)", allowGroupIds, allowTemplateUuids)
	} else if len(allowGroupIds) > 0 {
		db = db.Where("group_id in (?)", allowGroupIds)
	} else if len(allowTemplateUuids) > 0 {
		db = db.Where("uuid in (?)", allowTemplateUuids)
	}
	if len(conditionGroupIds) > 0 {
		db = db.Where("group_id in (?)", conditionGroupIds)
	}
	if len(conditionTemplateUuids) > 0 {
		db = db.Where("uuid in (?)", conditionTemplateUuids)
	}
	if len(conditionIds) > 0 {
		db = db.Where("id in (?)", conditionIds)
	}

	if len(conditionTagIds) > 0 {
		var conditionTagsStr string
		var conditionTagArgs []interface{}

		for _, tagId := range conditionTagIds {
			conditionTagArgs = append(conditionTagArgs, tagId)
			conditionTagsStr += "OR JSON_CONTAINS(tags,JSON_ARRAY(?)) "
		}

		if noneTag {
			conditionTagsStr += "OR JSON_LENGTH(tags) = 0"
		}

		conditionTagsStr = strings.TrimPrefix(conditionTagsStr, "OR")

		db = db.Where(conditionTagsStr, conditionTagArgs...)

	} else {
		if noneTag {
			db = db.Where(" JSON_LENGTH( tags ) = 0 ")
		}
	}

	if common.IsNil(whereMap["state"]) { //没指定state，则只查enable和disable的
		db = db.Where("state in (?)", []bpmCom.WorkflowTemplateStateEnum{bpmCom.STATE_ENABLED, bpmCom.STATE_DISABLED})
	}
	var totalCount int64
	if err := db.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	db = db.Order("create_time DESC").Offset(int(offset)).Limit(int(limit)).Find(&workflowTemplateInfos)
	return workflowTemplateInfos, totalCount, db.Error
}

// 根据Conditon进行条件查询
func (this *WorkflowModel) QueryWorkflowTemplateByWhereMap(whereMap map[string]interface{}) ([]*WorkflowTemplateTable, error) {
	db := this.ShardingWorkflowTemplateTable()
	var workflowTemplateInfos []*WorkflowTemplateTable
	db = db.Where("state in (?)", []bpmCom.WorkflowTemplateStateEnum{bpmCom.STATE_ENABLED, bpmCom.STATE_DISABLED})
	db = db.Where(whereMap).Order("create_time DESC").Find(&workflowTemplateInfos)
	return workflowTemplateInfos, db.Error
}

// 根据Conditon进行查询到所有符合条件的模板所属的GroupId集合
func (this *WorkflowModel) QueryWorkflowTemplateGroupIdsByWhereMap(whereMap map[string]interface{},
	workflowTemplateName string) ([]int64, error) {
	db := this.ShardingWorkflowTemplateTable()
	var workflowTemplateInfos []*WorkflowTemplateTable
	if workflowTemplateName != common.StringEmpty {
		db = db.Where(`name->'$."zh-cn"' like ? or name->'$."en-us"' like ?`,
			"%"+workflowTemplateName+"%", "%"+workflowTemplateName+"%")
	}
	db = db.Select("distinct group_id").Where(whereMap).Where("status = ?", bpmCom.STATUS_NORMAL).Find(&workflowTemplateInfos)
	if db.Error != nil {
		return nil, db.Error
	}
	var groupIds []int64
	for _, info := range workflowTemplateInfos {
		groupIds = append(groupIds, info.GroupId)
	}
	return groupIds, nil
}

// 查询workflowTemplate的Staff字段
func (this *WorkflowModel) QueryAllEnabledWorkflowTemplateStaff() ([]*WorkflowTemplateTable, error) {
	db := this.ShardingWorkflowTemplateTable()
	workflowTemplateInfos := []*WorkflowTemplateTable{}
	db = db.Select("uuid, staff").Where(map[string]interface{}{
		"state":  bpmCom.STATE_ENABLED,
		"status": bpmCom.STATUS_NORMAL,
	}).Find(&workflowTemplateInfos)
	return workflowTemplateInfos, db.Error
}

func (this *WorkflowModel) QueryTemplateCntForPaging(whereMap map[string]interface{}, workflowTemplateName string) (int64, error) {
	var cnt int64
	db := this.ShardingWorkflowTemplateTable()
	if workflowTemplateName != common.StringEmpty {
		db = db.Where(`name->'$."zh-cn"' like ?`, "%"+workflowTemplateName+"%")
	}
	db = db.Where(whereMap).Count(&cnt)
	return cnt, db.Error
}

// 根据tag查询workflowTemplate
func (this *WorkflowModel) QueryTemplateByTags(tagIds []int64, noneTag bool) ([]*WorkflowTemplateTable, error) {

	db := this.ShardingWorkflowTemplateTable()
	var workflowTemplateInfos []*WorkflowTemplateTable

	if len(tagIds) > 0 {
		var conditionTagsStr string
		var conditionTagArgs []interface{}

		for _, tagId := range tagIds {
			conditionTagArgs = append(conditionTagArgs, tagId)
			conditionTagsStr += "OR JSON_CONTAINS(tags,JSON_ARRAY(?)) "
		}

		if noneTag {
			conditionTagsStr += "OR JSON_LENGTH(tags) = 0"
		}

		conditionTagsStr = strings.TrimPrefix(conditionTagsStr, "OR")

		db = db.Where(conditionTagsStr, conditionTagArgs...)
	} else {
		if noneTag {
			db = db.Where(" JSON_LENGTH( tags ) = 0 ")
		}
	}

	err := db.Find(&workflowTemplateInfos).Error
	return workflowTemplateInfos, err
}

// 查询指定uuid的tags信息
func (this *WorkflowModel) QueryTemplateTagsInfoByUuid(uuid []string) (map[string]map[string][]workflow_tag.WorkflowTagTable, error) {

	db := this.ShardingWorkflowTemplateTable()
	var workflowTemplateInfos []*WorkflowTemplateTable

	result := make(map[string]map[string][]workflow_tag.WorkflowTagTable)

	tagModel, err := workflow_tag.NewWorkflowTagModel(this.GetParent(), false)
	if err != nil {
		return nil, err
	}

	if err := db.Select("id, uuid, tags, edition").Where("uuid in (?)", uuid).Find(&workflowTemplateInfos).Error; err != nil {
		return nil, err
	}

	tagIdsCollection := collection.NewInt64Collection([]int64{})
	for _, tmpl := range workflowTemplateInfos {
		for _, tagId := range tmpl.Tags {
			tagIdsCollection.Append(tagId)
		}
	}

	tagIds, _ := tagIdsCollection.Unique().ToInt64s()
	tagInfoMap := make(map[int64]workflow_tag.WorkflowTagTable)

	if tagInfoList, err := tagModel.QueryWorkflowTagsById(tagIds); err != nil {
		return nil, nil
	} else {
		for _, tagInfo := range tagInfoList {
			tagInfoMap[tagInfo.ID] = tagInfo
		}
	}

	for _, tmpl := range workflowTemplateInfos {
		if _, ok := result[tmpl.Uuid]; !ok {
			result[tmpl.Uuid] = make(map[string][]workflow_tag.WorkflowTagTable)
		}
		result[tmpl.Uuid][tmpl.Edition] = []workflow_tag.WorkflowTagTable{}
		for _, tagId := range tmpl.Tags {
			if t, ok := tagInfoMap[tagId]; ok {
				result[tmpl.Uuid][tmpl.Edition] = append(result[tmpl.Uuid][tmpl.Edition], t)
			}
		}
	}

	return result, nil
}

type TmplCntInGroup struct {
	GroupId       int64 `json:"groupId"`
	TemplateCount int64 `json:"templateCount"`
}

func (this *WorkflowModel) QueryWorkflowTemplateCntByGroupIds(groupIds []int64, states []bpmCom.WorkflowTemplateStateEnum) (
	groupIdTmplCntMap map[int64]int64, err error) {
	counts := []*TmplCntInGroup{}
	db := this.ShardingWorkflowTemplateTable()
	db = db.Where("group_id in (?) and state in (?) and status = ? ", groupIds,
		states, bpmCom.STATUS_NORMAL).
		Select("group_id, count(distinct(uuid)) AS template_count").
		Group("group_id").Find(&counts)
	if db.Error != nil {
		return nil, db.Error
	}
	groupIdTmplCntMap = make(map[int64]int64)
	for _, count := range counts {
		groupIdTmplCntMap[count.GroupId] = count.TemplateCount
	}
	return groupIdTmplCntMap, nil
}

// 启用模板
func (this *WorkflowModel) EnableWorkflowTemplate(workflowTemplateUuid, version string) error {
	db := this.ShardingWorkflowTemplateTable()
	if version != "" {
		db = db.Where("edition = ?", version)
	}
	//如果没传版本信息，则默认启用最新版本
	db = db.Where("uuid in (?)", workflowTemplateUuid).Order("LENGTH(edition) DESC, edition DESC").Limit(1).
		Updates(map[string]interface{}{
			"state": bpmCom.STATE_ENABLED,
		})
	return db.Error
}

// 禁用模板
func (this *WorkflowModel) DisableWorkflowTemplate(workflowTemplateUuid, version string) error {
	db := this.ShardingWorkflowTemplateTable()
	db = db.Where(map[string]interface{}{
		"uuid":    workflowTemplateUuid,
		"edition": version,
	}).Updates(map[string]interface{}{
		"state": bpmCom.STATE_DISABLED,
	})
	return db.Error
}

// 更新Tag
func (this *WorkflowModel) EditWorkflowTemplateTag(cond map[string]interface{}, tagIds []int64) error {
	db := this.ShardingWorkflowTemplateTable()
	return db.Where(cond).Updates(map[string]interface{}{
		"tags": tagIds,
	}).Error
}

// 将模板状态设置为历史
func (this *WorkflowModel) SetWorkflowTemplateHistory(workflowTemplateUuid, version string) error {
	db := this.ShardingWorkflowTemplateTable()
	db = db.Where(map[string]interface{}{
		"uuid":    workflowTemplateUuid,
		"edition": version,
	}).Updates(map[string]interface{}{
		"state": bpmCom.STATE_HISTORY,
	})
	return db.Error
}

// ///SMOOTH UPGRADE/////
func (this *WorkflowModel) QueryAllWorkflowTemplateUuids() ([]string, error) {
	db := this.ShardingWorkflowTemplateTable()
	tbs := make([]*WorkflowTemplateTable, 0)
	db = db.Select("distinct uuid").Where(map[string]interface{}{
		"status": bpmCom.STATUS_NORMAL,
	}).Find(&tbs)
	if db.Error != nil {
		return nil, db.Error
	}
	var ret []string
	for _, tb := range tbs {
		ret = append(ret, tb.Uuid)
	}
	return ret, nil
}
