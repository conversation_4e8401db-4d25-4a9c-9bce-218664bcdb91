/**
 * @note
 * workflow的DAO
 *
 * <AUTHOR>
 * @date 	2019-10-30
 */
package workflow

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	"strconv"
	"strings"
	"time"

	"gitlab.docsl.com/security/bpm/pkg/common"
	"gorm.io/gorm"
)

func newWorkflowSerialId() int64 {
	serialIdStr := time.Now().In(common.LocalLocation).Format("20060102150405") + common.RandNumString(3)
	i, _ := strconv.ParseInt(serialIdStr, 10, 64)
	if i == 0 {
		panic("newWorkflowSerialId panic, parse int result 0")
	}
	return i
}

// 在事务中批量插入node和workflow
func (this *WorkflowModel) BatchInsertWorkflowAndNodeAndProcessRuntime(nodes []*NodeTable,
	procs []*processModel.ProcessTable, templateUuid string, groupId int64,
	uuid, name, description, detail, edition string,
	state bpmCom.WorkflowStateEnum, status bpmCom.StatusEnum) (*WorkflowTable, error) {
	//先插入node
	this.gormDB = this.gormDB.Begin() //开事务
	tx := this.ShardingNodeRuntimeTable()
	for _, node := range nodes {
		txTmp := tx.Create(node)
		if txTmp.Error != nil {
			tx.Rollback()
			return nil, txTmp.Error
		}
	}
	//再插入process
	tx = this.ShardingProcessRuntimeTable()
	for _, proc := range procs {
		txTmp := tx.Create(proc)
		if txTmp.Error != nil {
			tx.Rollback()
			return nil, txTmp.Error
		}
	}
	//再插入workflow
	tx = this.ShardingWorkflowRuntimeTable()
	var newRow *WorkflowTable
	for retry := 3; retry > 0; retry-- {
		newRow = &WorkflowTable{
			UserId:       this.GetParent().Ctx().User().UserId,
			InitiatorId:  this.GetParent().Ctx().User().UserId, //TODO 暂时不做代发功能
			TemplateUuid: templateUuid,
			Uuid:         uuid,
			SerialId:     newWorkflowSerialId(),
			Name:         name,
			Description:  description,
			GroupId:      groupId,
			Front:        common.StringJsonEmpty,
			Data:         common.StringJsonEmpty,
			Detail:       detail,
			State:        state,
			Status:       status,
			Edition:      edition,
		}
		tx = tx.Create(newRow)
		if tx.Error != nil {
			if retry > 0 {
				continue
			} else {
				tx.Rollback()
				return nil, tx.Error
			}
		} else {
			break
		}
	}
	tx.Commit()
	return newRow, nil
}

// 软删除workflow表
func (this *WorkflowModel) softDeleteWorkflowByUuid(db *gorm.DB, uuids ...string) (rowsAffected int64, err error) {
	db = db.Where("uuid in (?) AND state != ?", uuids, bpmCom.STATE_EXECUTING).Updates(map[string]interface{}{
		"status": bpmCom.STATUS_DELETED,
	})
	return db.RowsAffected, db.Error
}

// 在持久化workflow表中软删除workflow
func (this *WorkflowModel) SoftDeleteWorkflowPersistenceByUuid(uuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingWorkflowPersistenceTable()
	return this.softDeleteWorkflowByUuid(db, uuids...)
}

// 在运行时workflow表中软删除workflow
func (this *WorkflowModel) SoftDeleteWorkflowRuntimeByUuid(uuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	return this.softDeleteWorkflowByUuid(db, uuids...)
}

// 硬删除运行时workflow，（仅用于运行时workflow表）
func (this *WorkflowModel) HardDeleteWorkflowRuntimeByUuid(uuids string) (rowsAffected int64, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	db = db.Exec("DELETE FROM"+WORKFLOW_RUNTIME_TABLE_NAME+" WHERE uuid in (?)", uuids)
	return db.RowsAffected, db.Error
}

func (this *WorkflowModel) updateWorkflowByWhereMap(db *gorm.DB, updateMap, whereMap map[string]interface{}, version int64) (rowsAffected int64, err error) {
	whereMap["version"] = version
	updateMap["version"] = version + 1
	db = db.Where(whereMap).Updates(updateMap)
	return db.RowsAffected, db.Error
}

func (this *WorkflowModel) UpdateWorkflowRuntimeState(uuid string, state bpmCom.WorkflowStateEnum) (rowsAffected int64, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	db = db.Where("uuid = ?", uuid).Updates(map[string]interface{}{
		"state": state,
	})
	return db.RowsAffected, db.Error
}

func (this *WorkflowModel) UpdateWorkflowRuntimeByWhereMap(updateMap, whereMap map[string]interface{}, version int64) (rowsAffected int64, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	return this.updateWorkflowByWhereMap(db, updateMap, whereMap, version)
}

func (this *WorkflowModel) UpdateWorkflowPersistenceByWhereMap(updateMap, whereMap map[string]interface{}, version int64) (rowsAffected int64, err error) {
	db := this.ShardingWorkflowPersistenceTable()
	return this.updateWorkflowByWhereMap(db, updateMap, whereMap, version)
}

func (this *WorkflowModel) queryWorkflowByUuid(db *gorm.DB, uuid string) (workflow *WorkflowTable, err error) {
	workflow = &WorkflowTable{}
	db = db.Where(map[string]interface{}{
		"uuid":   uuid,
		"status": bpmCom.STATUS_NORMAL,
	}).First(&workflow)
	return workflow, db.Error
}

func (this *WorkflowModel) QueryWorkflowRuntimeByUuid(uuid string) (workflow *WorkflowTable, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	return this.queryWorkflowByUuid(db, uuid)
}

func (this *WorkflowModel) queryWorkflowByUuids(db *gorm.DB, uuids []string) (workflow []*WorkflowTable, err error) {
	workflow = []*WorkflowTable{}
	db = db.Where("uuid in (?) AND status = ?", uuids, bpmCom.STATUS_NORMAL).Find(&workflow)
	return workflow, db.Error
}

func (this *WorkflowModel) QueryWorkflowRuntimeByUuids(uuids []string) (workflow []*WorkflowTable, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	return this.queryWorkflowByUuids(db, uuids)
}

func (this *WorkflowModel) QueryUserWorkflowRuntimeByWhereMap(whereMap map[string]interface{}, workflowUuids []string, workflowSerialIds []string, workflowName string, templateUuids []string, templateGroupIds []int64,
	startTime int64, endTime int64, updateStartTime, updateEndTime int64, states []string, tagIds []int64, noneTag bool, userIds []int64, offset, limit int64, archive bool) ([]*WorkflowTable, int64, error) {
	db := this.ShardingWorkflowRuntimeTable()
	workflows := []*WorkflowTable{}

	db = db.Joins("inner join workflow_template t on workflow_runtime.template_uuid = t.uuid and workflow_runtime.edition = t.edition")
	db = db.Select("workflow_runtime.*, t.tags as tags")

	if len(templateUuids) == 1 {
		db = db.Where("template_uuid in (?)", templateUuids[0])
	} else if len(templateUuids) > 1 {
		db = db.Where("template_uuid in (?)", templateUuids)
	}

	if len(tagIds) > 0 {
		var conditionTagsStr string
		var conditionTagArgs []interface{}

		for _, tagId := range tagIds {
			conditionTagArgs = append(conditionTagArgs, tagId)
			conditionTagsStr += "OR JSON_CONTAINS(t.tags,JSON_ARRAY(?)) "
		}

		if noneTag {
			conditionTagsStr += "OR JSON_LENGTH(t.tags) = 0"
		}

		conditionTagsStr = strings.TrimPrefix(conditionTagsStr, "OR")

		db = db.Where(conditionTagsStr, conditionTagArgs...)

	} else {
		if noneTag {
			db = db.Where(" JSON_LENGTH( t.tags ) = 0 ")
		}
	}

	if !archive {
		whereMap["workflow_runtime.user_id"] = this.parent.Ctx().User().UserId
	} else if len(userIds) > 0 {
		db = db.Where("workflow_runtime.user_id in (?)", userIds)
	}

	if len(workflowUuids) > 0 {
		db = db.Where("workflow_runtime.uuid in (?)", workflowUuids)
	}
	if len(workflowSerialIds) > 0 {
		var ids []int64
		for _, serialId := range workflowSerialIds {
			id, _ := strconv.ParseInt(serialId, 10, 64)
			ids = append(ids, id)
		}
		db = db.Where("workflow_runtime.serial_id in (?)", ids)
	}
	if len(templateGroupIds) > 0 {
		db = db.Where("workflow_runtime.group_id in (?)", templateGroupIds)
	}
	if startTime > 0 {
		db = db.Where("workflow_runtime.create_time > ?", startTime)
	}
	if endTime > 0 {
		db = db.Where("workflow_runtime.create_time < ?", endTime)
	}
	if len(workflowName) > 0 {
		db = db.Where(`workflow_runtime.name->'$."zh-cn"' like ?`, "%"+workflowName+"%")
	}
	if updateStartTime > 0 {
		db = db.Where("workflow_runtime.update_time > ?", updateStartTime)
	}
	if updateEndTime > 0 {
		db = db.Where("workflow_runtime.update_time < ?", updateEndTime)
	}
	if len(states) > 0 {
		db = db.Where("workflow_runtime.state in (?)", states)
	}

	db = db.Where(whereMap)

	//count
	var totalCount int64
	if err := db.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	db = db.Order("workflow_runtime.create_time DESC").Offset(int(offset)).Limit(int(limit)).Find(&workflows)
	return workflows, totalCount, db.Error
}

func (this *WorkflowModel) QueryUserWorkflowCountForPaging(whereMap map[string]interface{}, workflowName string, templateUuids []string, templateGroupIds []int64,
	startTime int64, endTime int64, updateStartTime, updateEndTime int64, states []string, archive bool) (int64, error) {
	var cnt int64
	db := this.ShardingWorkflowRuntimeTable()
	if !archive {
		whereMap["user_id"] = this.parent.Ctx().User().UserId
	}
	if len(workflowName) > 0 {
		db = db.Where(`name->'$."zh-cn"' like ?`, "%"+workflowName+"%")
	}
	if len(templateUuids) == 1 {
		whereMap["template_uuid"] = templateUuids[0]
	} else if len(templateUuids) > 1 {
		db = db.Where("template_uuid in (?)", templateUuids)
	}
	if len(templateGroupIds) > 0 {
		db = db.Where("group_id in (?)", templateGroupIds)
	}
	if updateStartTime > 0 {
		db = db.Where("update_time > ?", updateStartTime)
	}
	if updateEndTime > 0 {
		db = db.Where("update_time < ?", updateEndTime)
	}
	if startTime > 0 {
		db = db.Where("create_time > ?", startTime)
	}
	if endTime > 0 {
		db = db.Where("create_time < ?", endTime)
	}
	if len(states) > 0 {
		db = db.Where("state in (?)", states)
	}
	db = db.Where(whereMap).Count(&cnt)
	return cnt, db.Error
}

func (this *WorkflowModel) WithdrawWorkflowRuntime(workflowUuids []string) error {
	db := this.ShardingWorkflowRuntimeTable()
	db = db.Where("uuid in (?) and state != ? and status = ?", workflowUuids, bpmCom.STATE_EXECUTING,
		bpmCom.STATUS_NORMAL).Updates(map[string]interface{}{
		"state": bpmCom.STATE_DISCARD,
	})
	return db.Error
}

func (this *WorkflowModel) QueryWorkflowPersistenceByUuid(uuid string) (workflow *WorkflowTable, err error) {
	db := this.ShardingWorkflowPersistenceTable()
	return this.queryWorkflowByUuid(db, uuid)
}

func (this *WorkflowModel) QueryExecutingWorkflowByPulseTimeout(pulseTimeout int64) (workflows []*WorkflowTable, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	workflows = []*WorkflowTable{}
	db = db.Where(map[string]interface{}{
		"state":  bpmCom.STATE_EXECUTING,
		"status": bpmCom.STATUS_NORMAL,
	}).Where("unix_timestamp(now()) - pulse_time > ?", pulseTimeout).Find(&workflows)
	return workflows, db.Error
}

func (this *WorkflowModel) QueryReadyWorkflowByCreateTimeout(createTimeout int64) (workflows []*WorkflowTable, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	workflows = []*WorkflowTable{}
	db = db.Where(map[string]interface{}{
		"state":  bpmCom.STATE_READY,
		"status": bpmCom.STATUS_NORMAL,
	}).Where("unix_timestamp(now()) - create_time > ?", createTimeout).Find(&workflows)
	return workflows, db.Error
}

func (this *WorkflowModel) UpdateTimeoutExecutingWorkflowToReady(uuid string, pulseTimeout int64, version int64) (rowsAffected int64, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	db = db.Where(
		map[string]interface{}{
			"uuid":    uuid,
			"version": version,
		}).Where("unix_timestamp(now()) - pulse_time > ?", pulseTimeout).
		Updates(map[string]interface{}{
			"pulse_time": time.Now().Unix(),
			"version":    version + 1,
			"state":      bpmCom.STATE_READY,
		})
	return db.RowsAffected, db.Error
}

// Workflow心跳
func (this *WorkflowModel) UpdateWorkflowPulseTimeByUuid(uuid string, pulseTime time.Time,
	version int64) (rowsAffected int64, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	db = db.Where(
		map[string]interface{}{
			"uuid":    uuid,
			"version": version,
			"state":   bpmCom.STATE_EXECUTING,
		}).Updates(map[string]interface{}{
		"pulse_time": pulseTime.Unix(),
		"version":    version + 1,
	})
	return db.RowsAffected, db.Error
}

func (this *WorkflowModel) QueryWorkflowUuidsByNameAndTmplUuidsAndUserIds(
	name string, templateUuids []string, workflowStates []string, userIds []int64, workflowTemplateGroupIds []int64,
	queryWorkflowUuids []string, workflowSerialIds []string) (workflowUuids []string, err error) {
	db := this.ShardingWorkflowRuntimeTable()
	workflows := []*WorkflowTable{}
	workflowUuids = make([]string, 0)
	if len(userIds) > 0 {
		db = db.Where("user_id in (?)", userIds)
	}
	if len(templateUuids) == 1 {
		db = db.Where("template_uuid = ?", templateUuids[0])
	} else if len(templateUuids) > 1 {
		db = db.Where("template_uuid in (?)", templateUuids)
	}
	if len(workflowTemplateGroupIds) > 0 {
		db = db.Where("group_id in (?)", workflowTemplateGroupIds)
	}
	if len(queryWorkflowUuids) > 0 {
		db = db.Where("uuid in (?)", queryWorkflowUuids)
	}
	if len(workflowSerialIds) > 0 {
		var ids []int64
		for _, serialId := range workflowSerialIds {
			id, _ := strconv.ParseInt(serialId, 10, 64)
			ids = append(ids, id)
		}
		db = db.Where("serial_id in (?)", ids)
	}
	if len(name) > 0 {
		db = db.Where(`name->'$."zh-cn"' like ?`, "%"+name+"%")
	}
	if len(workflowStates) > 0 {
		db = db.Where(`state in (?)`, workflowStates)
	}
	db = db.Select("uuid").Find(&workflows)
	for _, w := range workflows {
		workflowUuids = append(workflowUuids, w.Uuid)
	}
	return workflowUuids, db.Error
}
