/**
 * @note
 * workflow.go
 *
 * <AUTHOR>
 * @date 	2019-11-08
 */
package workflow

import (
	"gitlab.docsl.com/security/bpm/internal/common"
	. "gitlab.docsl.com/security/bpm/pkg/common"
	"time"
)

type WorkflowBase struct {
	Base
	Table *WorkflowTable
}

func InitWorkflowBase(op Operator, uuid string) (wb *WorkflowBase, err error) {
	wb = &WorkflowBase{
		Base: Base{
			Uuid:       uuid,
			finishChan: make(chan struct{}),
		},
	}
	wb.WorkflowModel, err = NewWorkflowModel(op, false)
	return wb, err
}

// 成功获得锁后开始心跳
func (wb *WorkflowBase) Pulse() {
	wb.mu.Lock()
	defer wb.mu.Unlock()
	rowsAffected, err := wb.UpdateWorkflowPulseTimeByUuid(wb.Uuid, time.Now(), wb.currentVersion)
	if err != nil || rowsAffected == 0 {
		wb.GetParent().Ctx().Log().Errorf("workflow [%s] pulse failed, rows affected [%d], error [%v]", wb.Uuid, rowsAffected, err)
	} else {
		wb.currentVersion += 1
	}
}

func (wb *WorkflowBase) HeartBeat() {
	ticker := time.NewTicker(time.Second * time.Duration(common.GetConfig().PulseInterval))
	for {
		select {
		case <-ticker.C:
			wb.Pulse()
		case <-wb.finishChan:
			return
		}
	}
}

// Load and reload
func (wb *WorkflowBase) Load() error {
	var err error
	wb.mu.Lock()
	defer wb.mu.Unlock()
	wb.Table, err = wb.QueryWorkflowRuntimeByUuid(wb.Uuid)
	if err != nil {
		return err
	} else {
		wb.currentVersion = wb.Table.Version
		return nil
	}
}

// when acquire lock failed，wait for workflow finish
func (wb *WorkflowBase) Wait() error {
	ticker := time.NewTicker(time.Second * time.Duration(common.GetConfig().WaitInterval))
	timeoutTicker := time.NewTicker(time.Second * time.Duration(common.GetConfig().WaitTimeout))
	var err error
	lastRetryTimes := common.GetConfig().WaitRetryTimes
	for {
		select {
		case <-ticker.C:
			wb.parent.Ctx().Log().Infof("waiting other goroutine processing workflow [%s]...", wb.Uuid)
			if wb.IsOccupied() { //if current node has occupied the lock, just wait finish chan
				continue
			}
			err = wb.Load()
			if err != nil {
				if lastRetryTimes <= 0 {
					wb.parent.Ctx().Log().Errorf("load workflow [%s] error [%v]", wb.Uuid, err)
					return err
				}
				lastRetryTimes--
				continue
			}
			lastRetryTimes = common.GetConfig().WaitRetryTimes
			if wb.State() != common.STATE_EXECUTING {
				wb.parent.Ctx().Log().Infof("finish waiting workflow [%s], result [%s]", wb.Uuid, wb.State())
				return nil
			}
			//if timeout, make it ready
			if time.Now().Unix()-wb.Table.PulseTime > common.GetConfig().PulseTimeout {
				wb.parent.Ctx().Log().Infof("waiting workflow [%s], find it pulse timeout, try to fix it", wb.Uuid)
				if rowsAffected, err := wb.UpdateTimeoutExecutingWorkflowToReady(
					wb.Uuid, common.GetConfig().PulseTimeout, wb.currentVersion); err != nil || rowsAffected == 0 {
					wb.parent.Ctx().Log().Infof("fix pulse timeout workflow [%s] err [%v], rowsAffected [%d]", wb.Uuid, err, rowsAffected)
				} else {
					return nil
				}
			}
		case <-wb.finishChan:
			return nil
		case <-timeoutTicker.C:
			return common.ErrWaitTimeout
		}
	}
}

// Ready->Executing, acquire lock
func (wb *WorkflowBase) Begin() error {
	wb.InitFinishChanIfClosed()
	err := wb.transferState(common.STATE_READY, common.STATE_EXECUTING)
	defer func() {
		if err != nil {
			wb.CloseChanIgnorePanic()
		} else { //begin heartbeat
			wb.SetOccupied()
			go func() {
				wb.HeartBeat()
			}()
		}
	}()
	return err
}

// Executing->Reset
func (wb *WorkflowBase) Reset() error {
	defer wb.CloseChanIgnorePanic()
	defer wb.SetUnOccupied()
	return wb.transferState(common.STATE_EXECUTING, common.STATE_RESET)
}

// Executing->NeedComplete
func (wb *WorkflowBase) NeedComplete() error {
	defer wb.CloseChanIgnorePanic()
	defer wb.SetUnOccupied()
	return wb.transferState(common.STATE_EXECUTING, common.STATE_NEED_COMPLETE)
}

// Executing->Pending
func (wb *WorkflowBase) Pend() error {
	defer wb.CloseChanIgnorePanic()
	defer wb.SetUnOccupied()
	return wb.transferState(common.STATE_EXECUTING, common.STATE_PENDING)
}

// Pending->Executing, acquire lock
func (wb *WorkflowBase) Continue() error {
	wb.InitFinishChanIfClosed()
	err := wb.transferState(common.STATE_PENDING, common.STATE_EXECUTING)
	defer func() {
		if err != nil {
			wb.CloseChanIgnorePanic()
		} else { //begin heartbeat
			wb.SetOccupied()
			go func() {
				wb.HeartBeat()
			}()
		}
	}()
	return err
}

// Executing->Finish
func (wb *WorkflowBase) Finish() error {
	defer wb.CloseChanIgnorePanic()
	defer wb.SetUnOccupied()
	return wb.transferState(common.STATE_EXECUTING, common.STATE_FINISHED)
}

// Executing->Failed
func (wb *WorkflowBase) Fail() error {
	defer wb.CloseChanIgnorePanic()
	defer wb.SetUnOccupied()
	return wb.transferState(common.STATE_EXECUTING, common.STATE_FAILED)
}

// Persist data, use data in Table.Data
func (wb *WorkflowBase) Persist() error {
	wb.mu.Lock()
	defer wb.mu.Unlock()
	now := time.Now().Unix()
	rowsAffected, err := wb.UpdateWorkflowRuntimeByWhereMap(map[string]interface{}{
		"data":       wb.Table.Data,
		"pulse_time": now,
	}, map[string]interface{}{
		"uuid": wb.Uuid,
	}, wb.currentVersion)
	if err != nil {
		return err
	} else if rowsAffected == 0 {
		return common.ErrTransferFailed
	} else {
		wb.Table.PulseTime = now
		wb.currentVersion += 1
		return nil
	}
}

func (wb *WorkflowBase) State() common.WorkflowStateEnum {
	wb.mu.RLock()
	defer wb.mu.RUnlock()
	return wb.Table.State
}

// stateTransfer
func (wb *WorkflowBase) transferState(oldState, newState common.WorkflowStateEnum) error {
	wb.mu.Lock()
	defer wb.mu.Unlock()
	now := time.Now().Unix()
	rowsAffected, err := wb.UpdateWorkflowRuntimeByWhereMap(map[string]interface{}{
		"state":      newState,
		"pulse_time": now,
	}, map[string]interface{}{
		"uuid":  wb.Uuid,
		"state": oldState,
	}, wb.currentVersion)
	if err != nil {
		return err
	} else if rowsAffected == 0 {
		return common.ErrTransferFailed
	} else {
		wb.Table.State = newState
		wb.Table.PulseTime = now
		wb.currentVersion += 1
		return nil
	}
}
