package workflow_tag

import (
	"gitlab.docsl.com/security/bpm/internal/common"
)

func (this *WorkflowTagModel) InsertWorkflowTag(tagName, createUser string) (int64, error) {
	db := this.ShardingTable()
	newRow := &WorkflowTagTable{
		TagName:    tagName,
		CreateUser: createUser,
		Status:     common.STATUS_NORMAL,
	}
	db = db.Create(newRow)
	return newRow.ID, db.Error
}

func (this *WorkflowTagModel) SoftDeleteWorkflowTag(tagId int64) error {
	rowsAffected, err := this.UpdateWorkflowTagById(tagId, map[string]interface{}{
		"status": common.STATUS_DELETED,
	})

	if rowsAffected == 0 {
		return common.ErrRecordNotFound
	}

	return err
}

func (this *WorkflowTagModel) UpdateWorkflowTagById(tagId int64, data map[string]interface{}) (int64, error) {
	db := this.ShardingTable()
	db = db.Where(map[string]interface{}{
		"id": tagId,
	}).Updates(data)
	return db.RowsAffected, db.Error
}

func (this *WorkflowTagModel) QueryWorkflowTag(cond map[string]interface{}) (*WorkflowTagTable, error) {
	db := this.ShardingTable()
	tagInfo := &WorkflowTagTable{}
	err := db.Where(cond).First(&tagInfo).Error
	return tagInfo, err
}

func (this *WorkflowTagModel) QueryWorkflowTagsById(ids []int64) (items []WorkflowTagTable, err error) {
	db := this.ShardingTable()

	if len(ids) == 0 {
		return items, nil
	}

	if err = db.Where("id in (?)", ids).Where(map[string]interface{}{
		"status": common.STATUS_NORMAL,
	}).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

func (this *WorkflowTagModel) QueryList(cond map[string]interface{}) (items []WorkflowTagTable, count int64, err error) {

	db := this.ShardingTable()

	if err = db.Where(cond).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if err = db.Where(cond).Order("id desc").Find(&items).Error; err != nil {
		return nil, 0, err
	}

	return items, count, nil
}
