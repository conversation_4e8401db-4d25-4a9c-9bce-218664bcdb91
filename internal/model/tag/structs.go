package workflow_tag

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/common/mysql"
	"gorm.io/gorm"
)

const WORKFLOW_TAG_TABLE_NAME = "workflow_tag"

type WorkflowTagTable struct {
	common.GormModel
	TagName    string            `json:"tag_name"`
	CreateUser string            `json:"create_user"`
	Status     bpmCom.StatusEnum `json:"status"` //状态，正常/删除
}

type WorkflowTagModel struct {
	parent   common.Operator
	gormDB   *gorm.DB
	readOnly bool
}

func (model *WorkflowTagModel) ShardingTable() (db *gorm.DB) {
	return model.gormDB.Table(WORKFLOW_TAG_TABLE_NAME).Model(&WorkflowTagTable{})
}

func NewWorkflowTagModel(op common.Operator, readOnly bool) (model *WorkflowTagModel, err error) {
	model = &WorkflowTagModel{
		parent:   op,
		readOnly: readOnly,
	}
	model.gormDB, err = mysql.GetDB(common.ModuleName, readOnly, op.Ctx().Log())
	return
}
