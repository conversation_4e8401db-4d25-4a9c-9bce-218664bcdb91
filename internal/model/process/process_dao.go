/**
 * @note
 * process的DAO
 *
 * <AUTHOR>
 * @date 	2019-11-14
 */
package process

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gorm.io/gorm/clause"
	"time"

	"gitlab.docsl.com/security/bpm/pkg/common"
	"gorm.io/gorm"
)

func (this *ProcessModel) InsertOrUpdateApprovalProcessRuntime(userId int64, processUuid,
	workflowUuid, nodeUuid string, detail ApprovalProcessDetail, data ApprovalProcessData,
	status bpmCom.StatusEnum, state bpmCom.ProcessStateEnum, title, desc string, initiatorId int64) (id int64, err error) {
	if detailBytes, err := common.JsonEncode(detail); err != nil {
		return 0, err
	} else if dataBytes, err := common.JsonEncode(data); err != nil {
		return 0, err
	} else {
		return this.InsertOrUpdateProcessRuntime(userId, processUuid, workflowUuid, nodeUuid,
			string(detailBytes), string(dataBytes), bpmCom.PROCESS_TYPE_APPROVAL, status, state,
			title, desc, bpmCom.PORTAL_BPM_TASK, initiatorId)
	}
}

func (this *ProcessModel) InsertOrUpdateNotifyProcessRuntime(userId int64, processUuid,
	workflowUuid, nodeUuid string, detail NotifyProcessDetail, data NotifyProcessData,
	status bpmCom.StatusEnum, state bpmCom.ProcessStateEnum, title, desc string,
	initiatorId int64) (id int64, err error) {
	if detailBytes, err := common.JsonEncode(detail); err != nil {
		return 0, err
	} else if dataBytes, err := common.JsonEncode(data); err != nil {
		return 0, err
	} else {
		return this.InsertOrUpdateProcessRuntime(userId, processUuid, workflowUuid, nodeUuid,
			string(detailBytes), string(dataBytes), bpmCom.PROCESS_TYPE_NOTIFY, status, state,
			title, desc, bpmCom.PORTAL_BPM_TASK, initiatorId)
	}
}

func (this *ProcessModel) InsertOrUpdateInteractProcessRuntime(userId int64, processUuid,
	workflowUuid, nodeUuid string, detail InteractProcessDetail, data InteractProcessData,
	status bpmCom.StatusEnum, state bpmCom.ProcessStateEnum, title, desc string,
	initiatorId int64) (id int64, err error) {
	if detailBytes, err := common.JsonEncode(detail); err != nil {
		return 0, err
	} else if dataBytes, err := common.JsonEncode(data); err != nil {
		return 0, err
	} else {
		return this.InsertOrUpdateProcessRuntime(userId, processUuid, workflowUuid, nodeUuid,
			string(detailBytes), string(dataBytes), bpmCom.PROCESS_TYPE_INTERACT, status, state,
			title, desc, bpmCom.PORTAL_BPM_TASK, initiatorId)
	}
}

func (this *ProcessModel) InsertOrUpdateProcessRuntime(userId int64, processUuid, workflowUuid, nodeUuid,
	detail, data string, typ bpmCom.ProcessTypeEnum, status bpmCom.StatusEnum, state bpmCom.ProcessStateEnum,
	title, desc string, tag bpmCom.TaskTagEnum, initiatorId int64) (id int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.insertOrUpdateProcess(db, userId, processUuid, workflowUuid, nodeUuid, detail, data, typ,
		status, state, title, desc, initiatorId, tag)
}

// 用于转交/加签 在更新原process的同时新增一条
func (this *ProcessModel) UpdateAndInsertProcessRuntime(processUuid string, data ApprovalProcessData,
	state bpmCom.ProcessStateEnum, //前面是旧process的信息，后面是新process的信息
	newUserId int64, newProcessUuid,
	workflowUuid, nodeUuid string, newDetail ApprovalProcessDetail, newData ApprovalProcessData,
	newStatus bpmCom.StatusEnum, newState bpmCom.ProcessStateEnum, newType bpmCom.ProcessTypeEnum, newSubType bpmCom.ProcessSubTypeEnum, title, desc string, initiatorId int64) (id int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	if dataStr, err := common.JsonStringEncode(data); err != nil {
		return 0, err
	} else if newDetailStr, err := common.JsonStringEncode(newDetail); err != nil {
		return 0, err
	} else if newDataStr, err := common.JsonStringEncode(newData); err != nil {
		return 0, err
	} else {
		tx := db.Begin() //开事务
		txTmp := tx.Where(map[string]interface{}{
			"uuid": processUuid,
		}).Updates(map[string]interface{}{
			"state":        state,
			"data":         dataStr,
			"approve_time": time.Now().UnixNano(),
		})
		if txTmp.Error != nil {
			tx.Rollback()
			return 0, txTmp.Error
		}
		origin := this.parent.Ctx().User().Origin
		if len(origin) == 0 {
			origin = bpmCom.OriginBpm
		}
		newRow := &ProcessTable{}
		assignMap := map[string]interface{}{
			"user_id":       newUserId,
			"uuid":          newProcessUuid,
			"workflow_uuid": workflowUuid,
			"node_uuid":     nodeUuid,
			"detail":        newDetailStr,
			"data":          newDataStr,
			"type":          newType,
			"status":        newStatus,
			"state":         newState,
			"tag":           bpmCom.PORTAL_BPM_TASK,
			"origin":        origin,
			"title":         title,
			"desc":          desc,
			"initiator_id":  initiatorId,
		}
		//可重入
		tx = tx.Where("detail->'$.parentUuid' = ? AND detail->'$.subType' = ?", processUuid, newSubType).Where(
			map[string]interface{}{
				"user_id":       newUserId,
				"workflow_uuid": workflowUuid,
				"node_uuid":     nodeUuid,
			}).Assign(assignMap).FirstOrCreate(newRow)
		if tx.Error != nil {
			tx.Rollback()
			return 0, tx.Error
		}
		tx.Commit()
		return newRow.ID, tx.Error
	}
}

func (this *ProcessModel) InsertTaskForPortal(userId int64, processUuid, workflowUuid, nodeUuid,
	detail, data string, typ bpmCom.ProcessTypeEnum, status bpmCom.StatusEnum, state bpmCom.ProcessStateEnum,
	title, desc string, tag bpmCom.TaskTagEnum, initiatorId int64) (id int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.insertTaskForPortal(db, userId, processUuid, workflowUuid, nodeUuid, detail, data, typ,
		status, state, title, desc, initiatorId, tag)
}

func (this *ProcessModel) insertOrUpdateProcess(db *gorm.DB,
	userId int64, processUuid, workflowUuid, nodeUuid, detail, data string, typ bpmCom.ProcessTypeEnum,
	status bpmCom.StatusEnum, state bpmCom.ProcessStateEnum, title, desc string,
	initiatorId int64, tag bpmCom.TaskTagEnum) (id int64, err error) {
	origin := this.parent.Ctx().User().Origin
	if len(origin) == 0 {
		origin = bpmCom.OriginBpm
	}
	newRow := &ProcessTable{
		UserId:       userId,
		Uuid:         processUuid,
		WorkflowUuid: workflowUuid,
		NodeUuid:     nodeUuid,
		Detail:       detail,
		Data:         data,
		Type:         typ,
		Status:       status,
		State:        state,
		Tag:          tag,
		Origin:       origin,
		Title:        title,
		Desc:         desc,
		InitiatorId:  initiatorId,
	}
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "uuid"}},                    // 主键冲突检测
		DoUpdates: clause.AssignmentColumns(newRow.OnUpdateColumns()), // 冲突时更新这些字段
	}).Create(newRow).Error
	return newRow.ID, db.Error
}

func (this *ProcessModel) insertTaskForPortal(db *gorm.DB,
	userId int64, processUuid, workflowUuid, nodeUuid, detail, data string, typ bpmCom.ProcessTypeEnum,
	status bpmCom.StatusEnum, state bpmCom.ProcessStateEnum, title, desc string,
	initiatorId int64, tag bpmCom.TaskTagEnum) (id int64, err error) {
	origin := this.parent.Ctx().User().Origin
	if len(origin) == 0 {
		origin = bpmCom.OriginBpm
	}
	newRow := &ProcessTable{
		UserId:       userId,
		Uuid:         processUuid,
		WorkflowUuid: workflowUuid,
		NodeUuid:     nodeUuid,
		Detail:       detail,
		Data:         data,
		Type:         typ,
		Status:       status,
		State:        state,
		Tag:          tag,
		Origin:       origin,
		Title:        title,
		Desc:         desc,
		InitiatorId:  initiatorId,
	}
	db = db.Create(newRow)
	return newRow.ID, db.Error
}

// 将workflow的未审批的审批记录置为已过期
func (this *ProcessModel) setProcessExpiredByWorkflowUuids(db *gorm.DB, workflowUuids ...string) (rowsAffected int64, err error) {
	db = db.Where("workflow_uuid in (?) AND state in (?) AND status = ?", workflowUuids,
		[]bpmCom.ProcessStateEnum{bpmCom.PROCESS_STATE_UNAPPROVED, bpmCom.PROCESS_STATE_UNDONE,
			bpmCom.PROCESS_STATE_INITIALIZED}, bpmCom.STATUS_NORMAL).Updates(map[string]interface{}{
		"state": bpmCom.PROCESS_STATE_EXPIRED,
	})
	return db.RowsAffected, db.Error
}

// 将workflow的未审批的Runtime审批记录置为已过期
func (this *ProcessModel) SetProcessRuntimeExpiredByWorkflowUuids(workflowUuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.setProcessExpiredByWorkflowUuids(db, workflowUuids...)
}

// 将node的未审批的审批记录置为已过期
func (this *ProcessModel) setProcessExpiredByNodeUuid(db *gorm.DB, nodeUuid string) (rowsAffected int64, err error) {
	db = db.Where(map[string]interface{}{
		"node_uuid": nodeUuid,
		"status":    bpmCom.STATUS_NORMAL,
	}).Where("state in (?)", []bpmCom.ProcessStateEnum{bpmCom.PROCESS_STATE_UNAPPROVED,
		bpmCom.PROCESS_STATE_INITIALIZED}).UpdateColumn("state", bpmCom.PROCESS_STATE_EXPIRED)
	return db.RowsAffected, db.Error
}

// 将node的审批记录置为已失败
func (this *ProcessModel) setProcessFailedByNodeUuid(db *gorm.DB, nodeUuid string) (rowsAffected int64, err error) {
	db = db.Where(map[string]interface{}{
		"node_uuid": nodeUuid,
		"state":     bpmCom.PROCESS_STATE_INITIALIZED,
		"status":    bpmCom.STATUS_NORMAL,
	}).UpdateColumn("state", bpmCom.PROCESS_STATE_FAILED)
	return db.RowsAffected, db.Error
}

// 将node的未审批的Runtime审批记录置为已过期
func (this *ProcessModel) SetProcessRuntimeExpiredByNodeUuid(nodeUuid string) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.setProcessExpiredByNodeUuid(db, nodeUuid)
}

// 将node的未审批的Runtime审批记录置为已过期
func (this *ProcessModel) SetProcessRuntimeFailedByNodeUuid(nodeUuid string) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.setProcessFailedByNodeUuid(db, nodeUuid)
}

// 软删除ProcessRuntime记录
func (this *ProcessModel) softDeleteProcessByUuid(db *gorm.DB, uuids ...string) (rowsAffected int64, err error) {
	db = db.Where("uuid in (?) AND state = ?", uuids, bpmCom.STATUS_NORMAL).Updates(map[string]interface{}{
		"status": bpmCom.STATUS_DELETED,
	})
	return db.RowsAffected, db.Error
}

// 软删除Process记录
func (this *ProcessModel) softDeleteProcessByWorkflowUuid(db *gorm.DB, workflowUuids ...string) (rowsAffected int64, err error) {
	db = db.Where("workflow_uuid in (?) AND status = ?", workflowUuids, bpmCom.STATUS_NORMAL).Updates(map[string]interface{}{
		"status": bpmCom.STATUS_DELETED,
	})
	return db.RowsAffected, db.Error
}

// 根据workflowUuid软删除Process记录
func (this *ProcessModel) SoftDeleteProcessRuntimeByWorkflowUuid(workflowUuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.softDeleteProcessByWorkflowUuid(db, workflowUuids...)
}

// 硬删除Process记录
func (this *ProcessModel) HardDeleteProcessRuntimeByUuid(uuids ...string) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	db = db.Exec("DELETE FROM"+PROCESS_RUNTIME_TABLE_NAME+" WHERE uuid in (?)", uuids)
	return db.RowsAffected, db.Error
}

// 审批之后更新process的状态
func (this *ProcessModel) UpdateProcessStateByUserIdAndNodeId(userId int64, nodeUuid string,
	state bpmCom.ProcessStateEnum) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.updateProcessState(db, userId, nodeUuid, state)
}

func (this *ProcessModel) updateProcessState(db *gorm.DB,
	userId int64, nodeUuid string, state bpmCom.ProcessStateEnum) (rowsAffected int64, err error) {
	db = db.Where(
		map[string]interface{}{
			"user_id":   userId,
			"node_Uuid": nodeUuid,
		}).Updates(map[string]interface{}{
		"state": state,
	})
	return db.RowsAffected, db.Error
}

// 根据用户uuid查询所有待审批的Process
func (this *ProcessModel) QueryProcessRuntimeByUserIdAndStates(userId int64,
	states ...bpmCom.ProcessStateEnum) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryProcessByUserIdAndStates(db, userId, states...)
}

func (this *ProcessModel) queryProcessByUserIdAndStates(db *gorm.DB, userId int64,
	states ...bpmCom.ProcessStateEnum) (process []*ProcessTable, err error) {
	process = []*ProcessTable{}
	whereMap := map[string]interface{}{
		"user_id": userId,
		"status":  bpmCom.STATUS_NORMAL,
	}
	if len(states) > 0 {
		db = db.Where("state in ?", states)
	}
	db = db.Where(whereMap).Find(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryProcessRuntimeByWorkflowUuid(
	workflowUuid string) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryProcessRuntimeByWorkflowUuid(db, workflowUuid)
}

func (this *ProcessModel) QueryProcessRuntimeByWorkflowUuidsAndStates(
	workflowUuids []string, states []bpmCom.ProcessStateEnum) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryProcessRuntimeByWorkflowUuidAndStates(db, workflowUuids, states)
}

func (this *ProcessModel) queryProcessRuntimeByWorkflowUuidAndStates(db *gorm.DB,
	workflowUuids []string, states []bpmCom.ProcessStateEnum) (process []*ProcessTable, err error) {
	process = []*ProcessTable{}
	db = db.Where("workflow_uuid in (?) AND status = ? AND state in (?)",
		workflowUuids, bpmCom.STATUS_NORMAL, states).Find(&process)
	return process, db.Error
}

func (this *ProcessModel) queryProcessRuntimeByWorkflowUuid(db *gorm.DB,
	workflowUuid string) (process []*ProcessTable, err error) {
	process = []*ProcessTable{}
	whereMap := map[string]interface{}{
		"workflow_uuid": workflowUuid,
		"status":        bpmCom.STATUS_NORMAL,
	}
	db = db.Where(whereMap).Find(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryProcessRuntimeByWorkflowUuidAndUserIds(
	workflowUuid string, userIds []int64) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryProcessRuntimeByWorkflowUuidAndUserIds(db, workflowUuid, userIds)
}

func (this *ProcessModel) queryProcessRuntimeByWorkflowUuidAndUserIds(db *gorm.DB,
	workflowUuid string, userIds []int64) (process []*ProcessTable, err error) {
	process = []*ProcessTable{}
	whereMap := map[string]interface{}{
		"workflow_uuid": workflowUuid,
		"status":        bpmCom.STATUS_NORMAL,
	}
	db = db.Where(whereMap).Where("user_id in (?)", userIds).Find(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryProcessRuntimeByNodeUuidAndType(
	nodeUuid string, processType bpmCom.ProcessTypeEnum) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryProcessRuntimeByNodeUuidAndType(db, nodeUuid, processType)
}

func (this *ProcessModel) QueryProcessRuntimeByWorkflowUuidAndProcessState(
	workflowUuid string, processState bpmCom.ProcessStateEnum) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryProcessRuntimeByWorkflowUuid(db, workflowUuid)
}

func (this *ProcessModel) queryProcessRuntimeByWorkflowUuidAndProcessState(db *gorm.DB,
	workflowUuid string, processState bpmCom.ProcessStateEnum) (process []*ProcessTable, err error) {
	process = []*ProcessTable{}
	whereMap := map[string]interface{}{
		"workflow_uuid": workflowUuid,
		"state":         processState,
		"status":        bpmCom.STATUS_NORMAL,
	}
	db = db.Where(whereMap).Find(&process)
	return process, db.Error
}

func (this *ProcessModel) queryProcessRuntimeByNodeUuidAndType(db *gorm.DB,
	nodeUuid string, processType bpmCom.ProcessTypeEnum) (process []*ProcessTable, err error) {
	process = []*ProcessTable{}
	whereMap := map[string]interface{}{
		"node_uuid": nodeUuid,
		"type":      processType,
		"status":    bpmCom.STATUS_NORMAL,
	}
	db = db.Where(whereMap).Find(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryNeedToResetProcessRuntimeByNodeUuids(nodeUuids []string) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	process = []*ProcessTable{}
	whereMap := map[string]interface{}{
		"status": bpmCom.STATUS_NORMAL,
	}
	db = db.Where("node_uuid in (?)", nodeUuids).Where("state not in (?)", []bpmCom.ProcessStateEnum{
		bpmCom.PROCESS_STATE_INITIALIZED, bpmCom.PROCESS_STATE_ROLLBACKED, bpmCom.PROCESS_STATE_NEED_COMPLETE,
	}).Where(whereMap).Find(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryApprovalProcessRuntimeByNodeUuidAndState(nodeUuid string,
	state bpmCom.ProcessStateEnum, processType bpmCom.ProcessTypeEnum) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	process = []*ProcessTable{}
	whereMap := map[string]interface{}{
		"node_uuid": nodeUuid,
		"type":      processType,
		"state":     state,
		"status":    bpmCom.STATUS_NORMAL,
	}
	db = db.Where(whereMap).Find(&process)
	return process, db.Error
}

func (this *ProcessModel) updateProcessByWhereMap(db *gorm.DB, updateMap, whereMap map[string]interface{}) (rowsAffected int64, err error) {
	db = db.Where(whereMap).Updates(updateMap)
	return db.RowsAffected, db.Error
}

func (this *ProcessModel) UpdateProcessRuntimeByWhereMap(updateMap, whereMap map[string]interface{}) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.updateProcessByWhereMap(db, updateMap, whereMap)
}

func (this *ProcessModel) UpdateProcessRuntimeFromOldStatesByUuids(processUuids []string,
	oldStates []bpmCom.ProcessStateEnum, newState bpmCom.ProcessStateEnum) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	db = db.Where("uuid in (?)", processUuids).Where("state in (?)", oldStates).
		Updates(map[string]interface{}{
			"state": newState,
		})
	return db.RowsAffected, db.Error
}

func (this *ProcessModel) DelPortalProcessByUuid(processUuid string) (rowsAffected int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	db = db.Exec("DELETE FROM "+PROCESS_RUNTIME_TABLE_NAME+" WHERE uuid = ?", processUuid)
	return db.RowsAffected, db.Error
}

func (this *ProcessModel) UpdateProcessRuntimeStateByProcessUuid(processUuid string,
	state bpmCom.ProcessStateEnum) (rowsAffected int64, err error) {
	return this.UpdateProcessRuntimeByWhereMap(map[string]interface{}{
		"state": state,
	}, map[string]interface{}{
		"uuid": processUuid,
	})
}

func (this *ProcessModel) UpdateProcessRuntimeDataAndStateByProcessUuid(processUuid, data string, state bpmCom.ProcessStateEnum) (rowsAffected int64, err error) {
	return this.UpdateProcessRuntimeByWhereMap(map[string]interface{}{
		"data":  data,
		"state": state,
	}, map[string]interface{}{
		"uuid": processUuid,
	})
}

func (this *ProcessModel) UpdateProcessRuntimeApprovalTime(processUuid string) (int64, error) {
	return this.UpdateProcessRuntimeByWhereMap(map[string]interface{}{
		"approve_time": time.Now().UnixNano(),
	}, map[string]interface{}{
		"uuid": processUuid,
	})
}

func (this *ProcessModel) UpdateProcessRuntimeDataByProcessUuid(processUuid, data string) (
	rowsAffected int64, err error) {
	return this.UpdateProcessRuntimeByWhereMap(map[string]interface{}{
		"data": data,
	}, map[string]interface{}{
		"uuid": processUuid,
	})
}

func (this *ProcessModel) UpdateProcessPersistenceByWhereMap(updateMap, whereMap map[string]interface{}) (rowsAffected int64, err error) {
	db := this.ShardingProcessPersistenceTable()
	return this.updateProcessByWhereMap(db, updateMap, whereMap)
}

// 查询C端用户的流程
func (this *ProcessModel) queryUserProcessExcludeSomeStatesByWhereMapAndOffsetAndLimit(db *gorm.DB, whereMap map[string]interface{},
	workflowUuids, states, types []string, offset, limit, startTime, endTime, updateStartTime, updateEndTime int64) (process []*ProcessTable, err error) {
	process = []*ProcessTable{}
	whereMap["user_id"] = this.parent.Ctx().User().UserId
	whereMap["status"] = bpmCom.STATUS_NORMAL
	db = db.Where(whereMap).Order("create_time DESC").Offset(int(offset)).Limit(int(limit))
	if startTime > 0 {
		db = db.Where("create_time > ?", startTime)
	}
	if endTime > 0 {
		db = db.Where("create_time < ?", endTime)
	}
	if updateStartTime > 0 {
		db = db.Where("update_time > ?", updateStartTime)
	}
	if updateEndTime > 0 {
		db = db.Where("update_time < ?", updateEndTime)
	}
	if workflowUuids != nil {
		db = db.Where("workflow_uuid in (?)", workflowUuids)
	}
	if len(types) > 0 {
		db = db.Where("type in (?) ", types)
	}
	if len(states) > 0 {
		db = db.Where("state in (?) ", states)
	} else {
		db = db.Where("state in (?) ", listProcessIncludeStates)
	}
	db = db.Find(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryPortalProcessByWhereMap(whereMap map[string]interface{}, keyword string,
	orderBy bpmCom.TimeOrderType, offset, limit int64) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	process = []*ProcessTable{}
	whereMap["user_id"] = this.parent.Ctx().User().UserId
	order := "create_time DESC" //默认降序
	if orderBy == bpmCom.TIME_ORDER_ASC {
		order = "create_time ASC"
	}
	if len(keyword) > 0 {
		db = db.Where(`title->'$."zh-cn"' like ?`, "%"+keyword+"%")
	}
	db = db.Where(whereMap).Where("state = ? and status = ?", bpmCom.PROCESS_STATE_UNAPPROVED,
		bpmCom.STATUS_NORMAL).Order(order).Offset(int(offset)).Limit(int(limit))
	db = db.Find(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryPortalProcessCnt(whereMap map[string]interface{}, keyword string) (count int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	var cnt int64
	whereMap["user_id"] = this.parent.Ctx().User().UserId
	whereMap["status"] = bpmCom.STATUS_NORMAL
	if len(keyword) > 0 {
		db = db.Where(`title->'$."zh-cn"' like ?`, "%"+keyword+"%")
	}
	db = db.Where(whereMap).Where("state = ?", bpmCom.PROCESS_STATE_UNAPPROVED).Count(&cnt)
	return cnt, db.Error
}

func (this *ProcessModel) QueryUserProcessRuntimeExcludeSomeStatesByWhereMapAndOffsetAndLimit(whereMap map[string]interface{},
	workflowUuids, states, types []string, offset, limit, startTime, endTime, updateStartTime, updateEndTime int64) (process []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryUserProcessExcludeSomeStatesByWhereMapAndOffsetAndLimit(db, whereMap, workflowUuids, states, types, offset, limit, startTime, endTime, updateStartTime, updateEndTime)
}

func (this *ProcessModel) QueryUserDoneProcess(whereMap map[string]interface{},
	workflowUuids, states, types []string, offset, limit, startTime, endTime, updateStartTime, updateEndTime int64) (process []*ProcessTable, count int64, err error) {

	db := this.ShardingProcessRuntimeTable()
	whereMap["user_id"] = this.parent.Ctx().User().UserId
	whereMap["status"] = bpmCom.STATUS_NORMAL
	db = db.Where(whereMap)
	if startTime > 0 {
		db = db.Where("create_time > ?", startTime)
	}
	if endTime > 0 {
		db = db.Where("create_time < ?", endTime)
	}
	if updateStartTime > 0 {
		db = db.Where("update_time > ?", updateStartTime)
	}
	if updateEndTime > 0 {
		db = db.Where("update_time < ?", updateEndTime)
	}
	if len(types) > 0 {
		db = db.Where("type in (?) ", types)
	}
	if len(states) > 0 {
		db = db.Where("state in (?) ", states)
	} else {
		db = db.Where("state in (?) ", listProcessIncludeStates)
	}

	// ---------- 子查询（继承过滤条件） ----------
	subQuery := db.Session(&gorm.Session{NewDB: true}).Table(PROCESS_RUNTIME_TABLE_NAME).
		Select("workflow_uuid, MAX(approve_time) AS max_time").
		Group("workflow_uuid")

	if workflowUuids != nil {
		subQuery = subQuery.Where("workflow_uuid IN ?", workflowUuids)
	}

	// ---------- 统计数量 ----------
	baseQuery := db.Table("(?) AS t", subQuery).
		Joins("INNER JOIN process_runtime pr ON pr.workflow_uuid = t.workflow_uuid AND pr.approve_time = t.max_time")
	// ---------- 统计数量 ----------

	if err = baseQuery.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// ---------- 查询数据 ----------
	if err = baseQuery.Select("pr.*").
		Order("pr.create_time DESC").
		Offset(int(offset)).
		Limit(int(limit)).
		Find(&process).Error; err != nil {
		return nil, 0, err
	}
	return process, count, err
}

// 查询C端用户的流程
func (this *ProcessModel) queryUserProcessCntByWhereMapAndOffsetAndLimit(db *gorm.DB, whereMap map[string]interface{},
	workflowUuids, states, types []string, startTime, endTime, updateStartTime, updateEndTime int64) (count int64, err error) {
	whereMap["user_id"] = this.parent.Ctx().User().UserId
	whereMap["status"] = bpmCom.STATUS_NORMAL
	db = db.Where(whereMap)
	if startTime > 0 {
		db = db.Where("create_time > ?", startTime)
	}
	if endTime > 0 {
		db = db.Where("create_time < ?", endTime)
	}
	if updateStartTime > 0 {
		db = db.Where("update_time > ?", updateStartTime)
	}
	if updateEndTime > 0 {
		db = db.Where("update_time < ?", updateEndTime)
	}
	if workflowUuids != nil {
		db = db.Where("workflow_uuid in (?)", workflowUuids)
	}
	if len(types) > 0 {
		db = db.Where("type in (?) ", types)
	}
	if len(states) > 0 {
		db = db.Where("state in (?) ", states)
	} else {
		db = db.Where("state in (?) ", listProcessIncludeStates)
	}
	db = db.Count(&count)
	return count, db.Error
}

func (this *ProcessModel) QueryUserProcessRuntimeCntByWhereMap(whereMap map[string]interface{},
	workflowUuids, states, types []string, startTime, endTime, updateStartTime, updateEndTime int64) (count int64, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryUserProcessCntByWhereMapAndOffsetAndLimit(db, whereMap, workflowUuids, states, types, startTime, endTime, updateStartTime, updateEndTime)
}

func (this *ProcessModel) queryUserProcessByUuid(db *gorm.DB, uuid string) (process *ProcessTable, err error) {
	process = &ProcessTable{}
	db = db.Where(map[string]interface{}{
		"user_id": this.parent.Ctx().User().UserId,
		"uuid":    uuid,
		"status":  bpmCom.STATUS_NORMAL,
	}).First(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryUserProcessRuntimeByUuid(uuid string) (process *ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	return this.queryUserProcessByUuid(db, uuid)
}

func (this *ProcessModel) QueryProcessByUuid(uuid string) (process *ProcessTable, err error) {
	process = &ProcessTable{}
	db := this.ShardingProcessRuntimeTable()
	db = db.Where(map[string]interface{}{
		"uuid":   uuid,
		"status": bpmCom.STATUS_NORMAL,
	}).First(&process)
	return process, db.Error
}

func (this *ProcessModel) QueryProcessRuntimeByUuids(uuids []string) (processes []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	processes = []*ProcessTable{}
	db = db.Where(map[string]interface{}{
		"status": bpmCom.STATUS_NORMAL,
	}).Where("uuid in (?)", uuids).Find(&processes)
	return processes, db.Error
}

func (this *ProcessModel) QuerySubProcessesRuntimeByParentUuid(nodeUuid, parentUuid string) (processes []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	processes = []*ProcessTable{}
	db = db.Where(map[string]interface{}{
		"node_uuid": nodeUuid,
		"status":    bpmCom.STATUS_NORMAL,
	}).Where("detail->'$.parentUuid' = ?", parentUuid).Find(&processes)
	return processes, db.Error
}

func (this *ProcessModel) QueryUserProcessRuntimeByWorkflowUuids(workflowUuids []string) (processes []*ProcessTable, err error) {
	db := this.ShardingProcessRuntimeTable()
	processes = []*ProcessTable{}
	db = db.Where(map[string]interface{}{
		"user_id": this.parent.Ctx().User().UserId,
		"status":  bpmCom.STATUS_NORMAL,
	}).Where("workflow_uuid in (?)", workflowUuids).Find(&processes)
	return processes, db.Error
}
