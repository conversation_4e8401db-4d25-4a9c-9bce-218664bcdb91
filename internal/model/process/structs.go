package process

import (
	"encoding/json"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	notifyLogic "gitlab.docsl.com/security/bpm/internal/logic/notify"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"

	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/common/mysql"
	"gorm.io/gorm"
)

// 待审核表名
const (
	PROCESS_RUNTIME_TABLE_NAME     = "process_runtime"
	PROCESS_PERSISTENCE_TABLE_NAME = "process_persistence"
)

var (
	listProcessIncludeStates = []bpmCom.ProcessStateEnum{
		bpmCom.PROCESS_STATE_UNAPPROVED,
		bpmCom.PROCESS_STATE_APPROVED,
		bpmCom.PROCESS_STATE_REJECTED,
		bpmCom.PROCESS_STATE_HANDOVERED,
		bpmCom.PROCESS_STATE_NOTIFIED,
		bpmCom.PROCESS_STATE_NOT_NOTIFIED,
	}
)

type ProcessTable struct {
	common.GormModel
	UserId       int64                   `json:"userId"`       //待处理审批的用户Id
	Uuid         string                  `json:"uuid"`         //待处理的process的uuid
	WorkflowUuid string                  `json:"workflowUuid"` //待处理的工作流uuid
	NodeUuid     string                  `json:"nodeUuid"`     //待处理的task uuid
	Detail       string                  `json:"detail"`       //process细节，预留字段
	Data         string                  `json:"data"`         //process运行时数据，预留字段
	Status       bpmCom.StatusEnum       `json:"status"`       //是否已经处理(0代表未审批，1代表已审批)
	State        bpmCom.ProcessStateEnum `json:"state"`        //审批状态(枚举类型)
	Type         bpmCom.ProcessTypeEnum  `json:"type"`         //类型(目前有通知、审批)
	Tag          bpmCom.TaskTagEnum      `json:"tag"`          //任务tag
	Origin       string                  `json:"origin"`       //任务来源
	InitiatorId  int64                   `json:"initiatorId"`  //任务发起者
	Title        string                  `json:"title"`        //任务标题
	Desc         string                  `json:"desc"`         //任务描述
	ApproveTime  int64                   `json:"approveTime"  orm:"column(approve_time)"`
}

func (t *ProcessTable) OnUpdateColumns() []string {
	return []string{"detail", "data", "state", "status"}
}

type ProcessModel struct {
	parent   common.Operator
	gormDB   *gorm.DB
	readOnly bool
}

func (this *ProcessModel) ShardingProcessRuntimeTable() (db *gorm.DB) {
	db = this.gormDB.Table(PROCESS_RUNTIME_TABLE_NAME).Model(&ProcessTable{})
	return db
}

func (this *ProcessModel) ShardingProcessPersistenceTable() (db *gorm.DB) {
	db = this.gormDB.Table(PROCESS_PERSISTENCE_TABLE_NAME).Model(&ProcessTable{})
	return db
}

func NewProcessModel(op common.Operator, readOnly bool) (model *ProcessModel, err error) {
	model = &ProcessModel{
		parent:   op,
		readOnly: readOnly,
	}
	model.gormDB, err = mysql.GetDB(common.ModuleName, readOnly, op.Ctx().Log())
	return
}

type ApprovalProcessDetail struct {
	Mode            bpmCom.ApprovalModeEnum   `json:"mode"`
	Notify          notifyLogic.NotifyConfig  `json:"notify"`
	Order           int                       `json:"order,omitempty"`           //inTurn模式下的审批顺序
	SubType         bpmCom.ProcessSubTypeEnum `json:"subType,omitempty"`         //process的子类型
	ParentUuid      string                    `json:"parentUuid,omitempty"`      //如果是转交或加签的流程，会有此uuid
	PredecessorUuid string                    `json:"predecessorUuid,omitempty"` //对于rollback的process，记录其在退回前，对应的uuid
}

type ApprovalProcessData struct {
	Messaged                        bool                 `json:"notified,omitempty"` //是否已发送过通知给审批人来查看
	Comment                         string               `json:"comment,omitempty"`
	RollbackNodeUuid                string               `json:"rollbackNodeUuid,omitempty"`
	RollbackNodeName                json.RawMessage      `json:"rollbackNodeName,omitempty"`                //应前端要求，增加记录退回节点名称
	HandoverExplain                 string               `json:"handoverExplain,omitempty"`                 //转交备注
	HandoverExplainVisible          bool                 `json:"handoverExplainVisible,omitempty"`          //转交备注是否可见
	CountersignBeforeExplain        string               `json:"countersignBeforeExplain,omitempty"`        //向前加签备注
	CountersignBeforeExplainVisible bool                 `json:"countersignBeforeExplainVisible,omitempty"` //向前加签备注是否可见
	CountersignAfterExplain         string               `json:"countersignAfterExplain,omitempty"`         //向后加签备注
	CountersignAfterExplainVisible  bool                 `json:"countersignAfterExplainVisible,omitempty"`  //向后加签备注是否可见
	LastUrgeTime                    int64                `json:"lastUrgeTime,omitempty"`                    //最后一次催办的时间
	Remark                          []*ProcessRemarkInfo `json:"remark,omitempty"`                          //审批人评论
}

type NotifyProcessDetail struct {
	Notify notifyLogic.NotifyConfig `json:"notify"`
}

type NotifyProcessData struct {
	Messaged bool `json:"notified,omitempty"` //是否已发送过通知给审批人查看
}

type InteractProcessDetail struct {
	Url      string `json:"url,omitempty"`
	Method   string `json:"method,omitempty"`
	AppName  string `json:"appName,omitempty"`
	Callback bool   `json:"callback,omitempty"`
}

type InteractProcessData struct {
	PostData         interface{}     `json:"postData"`
	Result           interface{}     `json:"result,omitempty"`
	CallbackResult   *CallbackResult `json:"callbackResult,omitempty"`
	RollbackNodeUuid string          `json:"rollbackNodeUuid,omitempty"`
	RollbackNodeName json.RawMessage `json:"rollbackNodeName,omitempty"` //应前端要求，增加记录退回节点名称
}

type InteractResult struct {
	Code    int64          `json:"code"`
	Message string         `json:"message"`
	Data    CallbackResult `json:"data"`
}

type CallbackResult struct {
	Pass     bool        `json:"pass"`
	Rollback bool        `json:"rollback"`
	Comment  string      `json:"comment"`
	Data     interface{} `json:"data"`
}

type ProcessRemarkInfo struct {
	Remark       string                  `json:"remark"`
	File         []*RemarkFileInfo       `json:"file"`
	AtUser       []*staffLogic.UserInfo  `json:"atUser"`
	VisibleStaff []*staffLogic.StaffInfo `json:"visibleStaff"`
	CreateTime   int64                   `json:"createTime"`
}

type RemarkFileInfo struct {
	Name string `json:"name"`
	Size int64  `json:"size"`
}
