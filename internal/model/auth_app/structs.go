package auth_app

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/common/mysql"
	"gorm.io/gorm"
)

// 待审核表名
const (
	AUTH_APP_TABLE_NAME = "auth_app"
)

type AuthAppTable struct {
	common.GormModel
	UserId      int64             `json:"userId"`      //用户id
	SsoAppId    int64             `json:"ssoAppId"`    //真正的appId，标识一个内部应用
	AppName     string            `json:"appName"`     //应用名称
	Description string            `json:"description"` //应用描述
	AppId       string            `json:"appId"`       //用于加签的appId
	AppKey      string            `json:"appKey"`      //用于加签的appKey
	Status      bpmCom.StatusEnum `json:"status"`      //记录状态Normal/Deleted
}

type AuthAppModel struct {
	parent   common.Operator
	gormDB   *gorm.DB
	readOnly bool
}

func (this *AuthAppModel) ShardingAuthAppTable() (db *gorm.DB) {
	db = this.gormDB.Table(AUTH_APP_TABLE_NAME).Model(&AuthAppTable{})
	return db
}

func NewAuthAppModel(op common.Operator, readOnly bool) (model *AuthAppModel, err error) {
	model = &AuthAppModel{
		parent:   op,
		readOnly: readOnly,
	}
	model.gormDB, err = mysql.GetDB(common.ModuleName, readOnly, op.Ctx().Log())
	return
}
