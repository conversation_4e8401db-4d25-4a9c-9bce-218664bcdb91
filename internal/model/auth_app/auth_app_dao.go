/**
 * @note
 * AuthApp的DAO
 *
 * <AUTHOR>
 * @date 	2019-12-23
 */
package auth_app

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
)

// 插入appId和appKey
func (this *AuthAppModel) InsertAuthAppRecord(
	userId int64,
	ssoAppId int64,
	appName,
	desc,
	appId,
	appKey string) (
	id, key string, err error) {
	db := this.ShardingAuthAppTable()
	newRow := &AuthAppTable{
		UserId:      userId,
		SsoAppId:    ssoAppId,
		AppName:     appName,
		Description: desc,
		AppId:       appId,
		AppKey:      appKey,
		Status:      bpmCom.STATUS_NORMAL,
	}
	db = db.Create(newRow)
	return appId, appKey, db.Error
}

// 软删除auth_app记录
func (this *AuthAppModel) SoftDeleteAuthAppRecordByAppId(appIds []string) (rowsAffected int64, err error) {
	db := this.ShardingAuthAppTable()
	db = db.Where("app_id in (?) AND status = ?", appIds, bpmCom.STATUS_NORMAL).Updates(map[string]interface{}{
		"status": bpmCom.STATUS_DELETED,
	})
	return db.RowsAffected, db.Error
}

// 更新auth_app记录
func (this *AuthAppModel) UpdateAuthAppRecordByAppId(appId, appName, desc string) (rowsAffected int64, err error) {
	db := this.ShardingAuthAppTable()
	db = db.Where(
		map[string]interface{}{
			"app_id": appId,
		}).Updates(map[string]interface{}{
		"app_name":    appName,
		"description": desc,
	})
	return db.RowsAffected, db.Error
}

// 查询appId对应的记录
func (this *AuthAppModel) QueryAuthAppRecordByAppId(appId string) (authAppRecord *AuthAppTable, err error) {
	db := this.ShardingAuthAppTable()
	authAppRecord = &AuthAppTable{}
	whereMap := map[string]interface{}{
		"app_id": appId,
		"status": bpmCom.STATUS_NORMAL,
	}
	db = db.Where(whereMap).First(&authAppRecord)
	return authAppRecord, db.Error
}

// 查询appId对应的记录
func (this *AuthAppModel) QueryAuthAppRecordByAppName(appName string) (authAppRecord *AuthAppTable, err error) {
	db := this.ShardingAuthAppTable()
	authAppRecord = &AuthAppTable{}
	whereMap := map[string]interface{}{
		"app_name": appName,
		"status":   bpmCom.STATUS_NORMAL,
	}
	db = db.Where(whereMap).First(&authAppRecord)
	return authAppRecord, db.Error
}

// List接口
func (this *AuthAppModel) ListAuthAppRecordByCondition(whereMap map[string]interface{}) (authAppRecords []*AuthAppTable, err error) {
	db := this.ShardingAuthAppTable()
	authAppRecords = []*AuthAppTable{}
	db = db.Where(whereMap).Find(&authAppRecords)
	return authAppRecords, db.Error
}
