/**
 * @note
 * struct
 *
 * <AUTHOR>
 * @date 	2020-05-24
 */
package inter

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type InteractCallbackInput struct {
	WorkflowUuid string      `json:"workflowUuid"`
	NodeUuid     string      `json:"nodeUuid"`
	Pass         bool        `json:"pass"`
	Rollback     bool        `json:"rollback"`
	Comment      string      `json:"comment"`
	Data         interface{} `json:"data"`
}

func (input *InteractCallbackInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *InteractCallbackInput) Validate() error {
	if len(input.WorkflowUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("workflowUuid")
	} else if len(input.NodeUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("nodeUuid")
	}
	return nil
}

type GenerateTokenOutput struct {
	Token string `json:"token"`
}
