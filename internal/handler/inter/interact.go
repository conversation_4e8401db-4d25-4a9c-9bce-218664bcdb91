/**
 * @note
 * interact
 *
 * <AUTHOR>
 * @date 	2020-05-24
 */
package inter

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/internal/common"
	pLogic "gitlab.docsl.com/security/bpm/internal/logic/process"
	"gitlab.docsl.com/security/bpm/internal/model/process"
	wmService "gitlab.docsl.com/security/bpm/internal/srvs/workflow_message"
	. "gitlab.docsl.com/security/bpm/pkg/common"
	"time"
)

func HandleInteractCallback(gctx *gin.Context) {
	input := &InteractCallbackInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	//前置检查一下
	processTables, err := processOperator.QueryProcessRuntimeByNodeUuidAndType(input.NodeUuid, common.PROCESS_TYPE_INTERACT)
	if err == ErrRecordNotFound || len(processTables) == 0 {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_INTERACT_CALLBACK, err))
		return
	}
	//找到当前待回调的process
	var postedProcessTable *process.ProcessTable
	for _, p := range processTables {
		if p.State == common.PROCESS_STATE_FINISHED || p.State == common.PROCESS_STATE_FAILED {
			SetRet(gctx, NewError(ERR_PROCESS_MAYBE_ALREADY_DONE))
			return
		} else if p.State == common.PROCESS_STATE_POSTED || p.State == common.PROCESS_STATE_INITIALIZED {
			postedProcessTable = p
		}
	}
	//为了防止下游系统过快回调，增加一个等待环节
	for retry := 5; retry > 0 && postedProcessTable != nil && postedProcessTable.State == common.PROCESS_STATE_INITIALIZED; retry-- {
		time.Sleep(time.Second)
		if p, err := processOperator.QueryProcessRuntimeByUuid(postedProcessTable.Uuid); err != nil {
			postedProcessTable = p
		}
	}
	if postedProcessTable == nil || postedProcessTable.State == common.PROCESS_STATE_INITIALIZED {
		SetRet(gctx, NewError(ERR_INTERACT_PROCESS_NO_NEED_CALLBACK))
		return
	}
	tarNodeUuid, err := processOperator.HandleInteractProcessCallback(postedProcessTable, input.Comment,
		input.Pass, input.Rollback, input.Data)
	if err == ErrNoPermission {
		SetRet(gctx, NewError(ERR_NO_PERMISSION, err))
		return
	} else if err == common.ErrAlreadyDone {
		SetRet(gctx, NewError(ERR_PROCESS_MAYBE_ALREADY_DONE, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_INTERACT_CALLBACK, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
	messenger := wmService.NewWorkflowMessenger(ctx)
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{
		WorkflowUuid: postedProcessTable.WorkflowUuid,
		NodeUuid:     tarNodeUuid,
		TraceId:      ctx.Log().GetTraceId(),
	})
	return
}
