/**
 * @note
 * 用于给前端的一些测试接口
 *
 * <AUTHOR>
 * @date 	2020-02-04
 */
package inter

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type TestList struct {
	Name string `json:"name"`
	Id   string `json:"id"`
}

func TestL(gctx *gin.Context) {
	t := []TestList{
		{Name: "testName1", Id: "id1"},
		{Name: "testName2", Id: "id2"},
		{Name: "testName3", Id: "id3"},
		{Name: "testName4", Id: "id4"},
	}
	common.SetRet(gctx, common.NewError(common.ERR_OK).SetDesc(t))
	return
}
