/**
 * @note
 * auth_token
 *
 * <AUTHOR>
 * @date 	2020-07-06
 */
package inter

import (
	"github.com/gin-gonic/gin"
	sdkAuth "gitlab.docsl.com/security/bpm/internal/logic/sdk_auth"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func GenerateAuthToken(gctx *gin.Context) {
	ctx := GetHCtx(gctx)
	authOperator := sdkAuth.NewSdkAuthOperator(ctx)
	token, err := authOperator.GenerateAuthToken()
	if err != nil {
		SetRet(gctx, NewError(ERR_GENERATE_AUTH_TOKEN, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(&GenerateTokenOutput{
		Token: token,
	}))
	return
}
