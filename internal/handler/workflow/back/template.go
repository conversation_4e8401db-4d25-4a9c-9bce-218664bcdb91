/**
 * @note
 *
 * @author: libi
 * @date: 2019/11/28
 */

package back

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/internal/common"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wtLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/template"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

// BPM 2.1 创建+编辑（草稿箱）接口2in1
func EditWorkFlowTemplate(gctx *gin.Context) {
	ctx := GetHCtx(gctx)

	//输入处理
	input := &EditWorkFlowTemplateInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	//校验权限

	//校验传入的用户
	staffOperator := staffLogic.NewStaffOperator(ctx)
	staffs := input.Staff
	for _, n := range input.Nodes {
		if s := n.CollectStaffInfo(); len(s) > 0 {
			staffs = append(staffs, s...)
		}
	}
	if err := staffOperator.ValidateStaffInfos(staffs); err != nil {
		SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_STAFF_ERROR, err))
		return
	}
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	//校验groupId是否存在
	if input.GroupId > 0 {
		_, err := tmplOperator.QueryTemplateGroupByGroupId(input.GroupId)
		if err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_GROUP_NOT_EXIST, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_CREATE_WORKFLOW_TEMPLATE, err))
			return
		}
	}
	output := &CreateWorkFlowTemplateOutput{}
	var err error
	if input.TemplateUuid == StringEmpty {
		//分为直接创建新模板 create=true publish无关
		//将新模板保存到草稿箱不发布 create=false publish无关
		tmplUuid := NewUuid(RESOURCE_TYPE_WORKFLOW_TEMPLATE)
		state := common.STATE_STAGING
		if input.Create { //创建有版本号，保存草稿没有
			output.Version = "v1.0"
			state = common.STATE_ENABLED
		}

		output.WorkflowTemplateUuid, err = tmplOperator.BatchInsertWorkflowAndNodeTemplate(
			tmplUuid,
			input.Nodes,
			input.Form,
			input.Staff,
			input.GroupId,
			input.Name,
			input.Description,
			state,
			input.Origin,
			output.Version,
			input.RemoveDuplicate, input.TagIds, true)
		if err == common.ErrInvalidNodeDetail {
			SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_GATEWAY_CONDITION, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_CREATE_WORKFLOW_TEMPLATE, err))
			return
		}
	} else { //有templateUuid的情况，
		//分为直接创建一个旧模板的新版本 create=true publish=false
		//从草稿箱中发布一个旧模板的新版本（或新模板）create=false publish=true
		//以及更新一个新/旧模板的草稿箱 create=false publish=false
		//先校验template存不存在
		if _, err := tmplOperator.QueryWorkFlowTemplateByTmplUuid(input.TemplateUuid); err != nil {
			SetRet(gctx, NewError(ERR_EDIT_WORKFLOW_TEMPLATE, err))
			return
		}

		output.WorkflowTemplateUuid, output.Version, err = tmplOperator.EditWorkFlowTemplate(
			input.TemplateUuid,
			input.Nodes,
			input.Staff,
			input.Form,
			input.GroupId,
			input.Name,
			input.Description,
			input.Origin,
			input.RemoveDuplicate, input.Create, input.Publish, input.TagIds)
		if err == common.ErrInvalidNodeDetail {
			SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_GATEWAY_CONDITION, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_EDIT_WORKFLOW_TEMPLATE, err))
			return
		}
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

/*func CreateWorkFlowTemplate(gctx *gin.Context) {
	ctx := GetHCtx(gctx)

	//输入处理
	input := &CreateWorkFlowTemplateInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	//校验权限
	if !ctx.User().AllowCreateWorkflow {
		SetRet(gctx, NewError(ERR_NO_PERMISSION))
		return
	}
	//校验传入的用户
	staffOperator := staffLogic.NewStaffOperator(ctx)
	staffs := input.Staff
	for _, n := range input.Nodes {
		if s := n.CollectStaffInfo(); len(s) > 0 {
			staffs = append(staffs, s...)
		}
	}
	if err := staffOperator.ValidateStaffInfos(staffs); err != nil {
		SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_STAFF_ERROR, err))
		return
	}
	output := &CreateWorkFlowTemplateOutput{}
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	var err error
	tmplUuid := NewUuid(RESOURCE_TYPE_WORKFLOW_TEMPLATE)
	//校验groupId是否存在
	if input.GroupId > 0 {
		_, err := tmplOperator.QueryTemplateGroupByGroupId(input.GroupId)
		if err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_GROUP_NOT_EXIST, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_CREATE_WORKFLOW_TEMPLATE, err))
			return
		}
	}
	output.WorkflowTemplateUuid, err = tmplOperator.BatchInsertWorkflowAndNodeTemplate(
		tmplUuid,
		input.Nodes,
		input.Form,
		input.Staff,
		input.GroupId,
		input.Name,
		input.Description,
		bpmCom.STATE_STAGING,
		input.Origin,
		"v1.0",
		input.RemoveDuplicate, true)
	if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_WORKFLOW_TEMPLATE, err))
		return
	}
	output.Version = "v1.0"
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}*/

func EnableWorkFlowTemplate(gctx *gin.Context) {
	input := &EnableWorkFlowTemplateInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &EnableWorkFlowTemplateOutput{}
	ctx := GetHCtx(gctx)

	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)

	//再enable
	if err := tmplOperator.EnableWorkFlowTemplate(input.WorkflowTemplateUuids); err != nil {
		if err == ErrNoPermission {
			SetRet(gctx, NewError(ERR_NO_PERMISSION))
		} else {
			SetRet(gctx, NewError(ERR_ENABLE_WORKFLOW_TEMPLATE, err))
		}
		return
	}

	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func DisableWorkFlowTemplate(gctx *gin.Context) {
	input := &DisableWorkFlowTemplateInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &DisableWorkFlowTemplateOutput{}
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)

	//再disable
	if err := tmplOperator.DisableWorkFlowTemplate(input.WorkflowTemplateUuids); err != nil {
		if err == ErrNoPermission {
			SetRet(gctx, NewError(ERR_NO_PERMISSION))
		} else {
			SetRet(gctx, NewError(ERR_DISABLE_WORKFLOW_TEMPLATE, err))
		}
		return
	}

	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func EditWorkflowTag(gctx *gin.Context) {
	input := &EditWorkflowTagInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	//不区分版本
	input.WorkflowTemplate.Version = ""

	tmplOperator := wtLogic.NewWorkflowTmplOperator(GetHCtx(gctx))
	if err := tmplOperator.EditWorkflowTemplateTags(input.WorkflowTemplate, input.TagIds); err != nil {
		SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_EDIT_TAG, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
}

/*func StagingWorkFlowTemplate(gctx *gin.Context) {
	input := &StagingWorkFlowTemplateInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	//校验传入的用户
	staffOperator := staffLogic.NewStaffOperator(ctx)
	staffs := input.Staff
	for _, n := range input.Nodes {
		if s := n.CollectStaffInfo(); len(s) > 0 {
			staffs = append(staffs, s...)
		}
	}
	if err := staffOperator.ValidateStaffInfos(staffs); err != nil {
		SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_STAFF_ERROR, err))
		return
	}
	output := &StagingWorkFlowTemplateOutput{}

	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	//查一下，鉴权
	if len(input.TemplateUuid) > 0 {
		if _, err := tmplOperator.QueryWorkFlowTemplateByTmplUuid(input.TemplateUuid); err != nil {
			SetRet(gctx, NewError(ERR_STAGING_WORKFLOW_TEMPLATE, err))
			return
		}
		if !ctx.User().WorkflowPermission[permission.WorkflowEditPermission].HasPermission(input.TemplateUuid) {
			SetRet(gctx, NewError(ERR_NO_PERMISSION))
			return
		}
	}
	//校验groupId是否存在
	if input.GroupId > 0 {
		_, err := tmplOperator.QueryTemplateGroupByGroupId(input.GroupId)
		if err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_GROUP_NOT_EXIST, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_CREATE_WORKFLOW_TEMPLATE, err))
			return
		}
	}
	var err error
	output.WorkflowTemplateUuid, output.Version, err = tmplOperator.StagingWorkFlowTemplate(
		input.TemplateUuid,
		input.Version,
		input.Nodes,
		input.Staff,
		input.Form,
		input.GroupId,
		input.Name,
		input.Description,
		bpmCom.STATE_STAGING,
		input.Origin,
		input.RemoveDuplicate)
	if err == bpmCom.ErrWorkflowTmplNotStaging {
		SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_NOT_STAGING, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_STAGING_WORKFLOW_TEMPLATE, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}*/

func GetWorkFlowTemplateDetail(gctx *gin.Context) {
	input := &GetTemplateDetailInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)

	detail, err := tmplOperator.GetWorkFlowTemplateDetail(input.WorkflowTemplateUuid, input.Version, input.Staging)
	if err != nil {
		if err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_NOT_EXIST, err))
		} else if err == ErrNoPermission {
			SetRet(gctx, NewError(ERR_NO_PERMISSION))
		} else {
			SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TEMPLATE, err))
		}
		return
	}

	SetRet(gctx, NewError(ERR_OK).SetDesc(detail))
	return
}

func ListWorkFlowTemplate(gctx *gin.Context) {
	input := &ListWorkFlowTemplateInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &ListWorkFlowTemplateOutput{}
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	infos, totalCnt, err := tmplOperator.ListWorkFlowTemplate(input.Condition, input.Offset, input.Limit)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TEMPLATE, err))
		return
	}
	output.Count = totalCnt
	output.Items = infos
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func DeleteWorkFlowTemplate(gctx *gin.Context) {
	input := &DeleteWorkFlowTemplateInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &DeleteWorkFlowTemplateOutput{}
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)

	//再删
	if err := tmplOperator.DeleteWorkFlowTemplate(input.WorkflowTemplateUuids, input.Staging); err != nil {
		if err == ErrNoPermission {
			SetRet(gctx, NewError(ERR_NO_PERMISSION))
		} else {
			SetRet(gctx, NewError(ERR_DELETE_WORKFLOW_TEMPLATE, err))
		}
		return
	}

	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func ValidateCondition(gctx *gin.Context) {
	input := &ValidateConditionInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	err := tmplOperator.ValidateGatewayCondition(input.Condition)
	if err != nil {
		SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_GATEWAY_CONDITION, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
	return
}
