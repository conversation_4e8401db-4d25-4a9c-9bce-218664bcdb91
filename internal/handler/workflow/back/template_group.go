/**
 * @note
 * 模板组增、删、改、查相关接口，一个模板组包含多个workflow模板
 *
 *
 * @author: libi
 * @date: 2019/12/04
 */

package back

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/internal/common"
	wtLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/template"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func CreateTemplateGroup(gctx *gin.Context) {
	ctx := GetHCtx(gctx)

	//输入处理
	input := &CreateTemplateGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	output := &CreateTemplateGroupOutput{}
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	var err error
	output.WorkflowTemplateGroupId, err = tmplOperator.InsertWorkflowTmplGroup(
		input.Name,
		input.Description,
	)
	if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func UpdateTemplateGroup(gctx *gin.Context) {
	ctx := GetHCtx(gctx)
	//输入处理
	input := &UpdateWorkflowTemplateGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	output := &UpdateTemplateGroupOutput{}
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)

	// 权限
	_, err := tmplOperator.QueryTemplateGroupByGroupId(input.WorkflowTemplateGroupId)
	if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}

	if input.Name != nil && input.Description != nil {
		err := tmplOperator.UpdateWorkflowTmplGroupNameAndDesc(
			input.WorkflowTemplateGroupId,
			input.Name,
			input.Description,
		)
		if err != nil {
			SetRet(gctx, NewError(ERR_UPDATE_WORKFLOW_TEMPLATE_GROUP, err))
			return
		}
	}
	if input.WorkflowTemplateUuids != nil {
		err := tmplOperator.UpdateWorkflowTmplGroupTemplates(
			input.WorkflowTemplateGroupId,
			input.WorkflowTemplateUuids,
		)
		if err != nil {
			SetRet(gctx, NewError(ERR_UPDATE_WORKFLOW_TEMPLATE_GROUP, err))
			return
		}
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func EnableTemplateGroup(gctx *gin.Context) {
	input := &EnableTemplateGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &EnableTemplateGroupOutput{}
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	//先查询是否是创建者
	groups, err := tmplOperator.QueryTemplateGroupByGroupIds(input.WorkflowTemplateGroupIds)
	if err != nil {
		SetRet(gctx, NewError(ERR_DELETE_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}
	for _, group := range groups {
		if ctx.User().UserId != group.UserId {
			SetRet(gctx, NewError(ERR_NOT_TEMPLATE_GROUP_OWNER, err))
			return
		}
	}
	err = tmplOperator.EnableTemplateGroup(input.WorkflowTemplateGroupIds)
	if err != nil {
		SetRet(gctx, NewError(ERR_ENABLE_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func DisableTemplateGroup(gctx *gin.Context) {
	input := &DisableTemplateGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &DisableTemplateGroupOutput{}
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	//先查询是否是创建者
	groups, err := tmplOperator.QueryTemplateGroupByGroupIds(input.WorkflowTemplateGroupIds)
	if err != nil {
		SetRet(gctx, NewError(ERR_DELETE_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}
	for _, group := range groups {
		if ctx.User().UserId != group.UserId {
			SetRet(gctx, NewError(ERR_NOT_TEMPLATE_GROUP_OWNER, err))
			return
		}
	}
	err = tmplOperator.DisableTemplateGroup(input.WorkflowTemplateGroupIds)
	if err != nil {
		SetRet(gctx, NewError(ERR_DISABLE_WORKFLOW_TEMPLATE, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func ListTemplateGroup(gctx *gin.Context) {
	input := &ListTemplateGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &ListTemplateGroupOutput{}
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	templateGroupInfos, totalCnt, err := tmplOperator.ListTemplateGroup(input.Condition, input.Offset, input.Limit)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}
	output.Count = totalCnt
	output.Items = templateGroupInfos
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func GetTemplateGroupDetail(gctx *gin.Context) {
	input := &GetTemplateGroupDetailInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)

	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	output, err := tmplOperator.GetTemplateGroupDetailByGroupId(input.WorkflowTemplateGroupId)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}

	tmplInfos, err := tmplOperator.QueryAllWorkflowTmplByTmplGroupId(input.WorkflowTemplateGroupId, nil, nil)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}

	output.WorkflowTemplates = tmplInfos
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func DeleteTemplateGroup(gctx *gin.Context) {
	input := &DeleteTemplateGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &DeleteTemplateGroupOutput{}
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)

	//权限判断
	_, err := tmplOperator.QueryTemplateGroupByGroupIds(input.WorkflowTemplateGroupIds)
	if err != nil {
		SetRet(gctx, NewError(ERR_DELETE_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}

	//再删除
	err = tmplOperator.DeleteTemplateGroup(input.WorkflowTemplateGroupIds)
	if err == common.ErrDelTmplGroupHasTmpls {
		SetRet(gctx, NewError(ERR_DELETE_WORKFLOW_TEMPLATE_GROUP_HAS_TEMPLATE, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_DELETE_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}
