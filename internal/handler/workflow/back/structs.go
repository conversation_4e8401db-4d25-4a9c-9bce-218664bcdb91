/**
 * @note
 *
 * @author: libi
 * @date: 2019/11/28
 */

package back

import (
	common2 "gitlab.docsl.com/security/bpm/internal/common"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wrLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/runtime"
	wtLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/template"
	"strconv"
	"strings"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type EditWorkFlowTemplateInput struct {
	TemplateUuid    string                       `json:"templateUuid"`
	Create          bool                         `json:"create"`  //传了是直接创建，不传是保存草稿
	Publish         bool                         `json:"publish"` //是否保存草稿时同时发布
	GroupId         int64                        `json:"groupId"`
	Origin          common2.UserOriginEnum       `json:"origin"` //从哪些来源可以发起
	Staff           []*staffLogic.StaffInfo      `json:"staff"`  //哪些人可以使用该模板
	Name            common2.I18nString           `json:"name" validate:"required"`
	Description     common2.I18nString           `json:"description"`
	Nodes           []*wtLogic.NodeTemplateInput `json:"nodes" validate:"required,gt=0,dive"`
	Form            interface{}                  `json:"form"`
	RemoveDuplicate bool                         `json:"removeDuplicate"`
	TagIds          []int64                      `json:"tagIds"`
}

func (input *EditWorkFlowTemplateInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input) //需要根据类型来绑定
	if err != nil {
		return err
	}
	if input.Name.MaxLength() == 0 {
		return common.ErrMissingAMandatoryParameterf("name")
	} else if input.Name.MaxLength() > common.MaxNameLength {
		return common.ErrInvalidFieldf("name")
	} else if err := input.Name.Validate(); err != nil {
		return err
	}
	if input.Description.MaxLength() > common.MaxDescLength {
		return common.ErrInvalidFieldf("description")
	} else if err := input.Description.Validate(); err != nil {
		return err
	}
	for _, n := range input.Nodes {
		if err = n.Validate(); err != nil {
			return err
		}
	}
	if input.GroupId == 0 {
		return common.ErrMissingAMandatoryParameterf("workflowTemplateGroupId")
	}
	//TODO 默认去重
	input.RemoveDuplicate = true
	if len(input.Origin) == 0 {
		input.Origin = common2.OriginAll
	} else if input.Origin != common2.OriginBpm && input.Origin != common2.OriginInternalSystem && input.Origin != common2.OriginAll {
		return common.ErrInvalidFieldf("origin")
	}
	if len(input.Staff) == 0 {
		input.Staff = append(input.Staff, &staffLogic.StaffInfo{
			Type: common2.STAFF_TYPE_ALL,
		})
	} else {
		for _, s := range input.Staff {
			if s.Type != common2.STAFF_TYPE_PERSON &&
				s.Type != common2.STAFF_TYPE_GROUP &&
				s.Type != common2.STAFF_TYPE_DEPARTMENT &&
				s.Type != common2.STAFF_TYPE_ALL {
				return common.ErrInvalidParameterOptionf("staff")
			} else if s.Type == common2.STAFF_TYPE_PERSON && s.UserId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.userId")
			} else if s.Type == common2.STAFF_TYPE_DEPARTMENT && s.DepartmentId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.departmentId")
			} else if s.Type == common2.STAFF_TYPE_GROUP && s.UserGroupId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.userGroupId")
			}
		}
	}

	return common.Validator.Struct(input)
}

type CreateWorkFlowTemplateInput struct {
	TemplateUuid    string                       `json:"templateUuid"`
	Name            common2.I18nString           `json:"name" validate:"required"`
	Description     common2.I18nString           `json:"description"`
	GroupId         int64                        `json:"groupId"`
	Origin          common2.UserOriginEnum       `json:"origin"` //从哪些来源可以发起
	Staff           []*staffLogic.StaffInfo      `json:"staff"`  //哪些人可以使用该模板
	Nodes           []*wtLogic.NodeTemplateInput `json:"nodes" validate:"required,gt=0,dive"`
	Form            interface{}                  `json:"form"`
	RemoveDuplicate bool                         `json:"removeDuplicate"`
}

func (input *CreateWorkFlowTemplateInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input) //需要根据类型来绑定
	if err != nil {
		return err
	}
	if input.Name.MaxLength() == 0 {
		return common.ErrMissingAMandatoryParameterf("name")
	} else if input.Name.MaxLength() > common.MaxNameLength {
		return common.ErrInvalidFieldf("name")
	} else if err := input.Name.Validate(); err != nil {
		return err
	}
	if input.GroupId == 0 {
		return common.ErrMissingAMandatoryParameterf("workflowTemplateGroupId")
	}
	if input.Description.MaxLength() == 0 {
		return common.ErrMissingAMandatoryParameterf("description")
	} else if input.Description.MaxLength() > common.MaxDescLength {
		return common.ErrInvalidFieldf("description")
	} else if err := input.Description.Validate(); err != nil {
		return err
	}

	//TODO 默认去重
	input.RemoveDuplicate = true
	for _, n := range input.Nodes {
		if err = n.Validate(); err != nil {
			return err
		}
	}
	if len(input.Origin) == 0 {
		input.Origin = common2.OriginBpm
	} else if input.Origin != common2.OriginBpm && input.Origin != common2.OriginInternalSystem {
		return common.ErrInvalidFieldf("origin")
	}
	if len(input.Staff) == 0 {
		input.Staff = append(input.Staff, &staffLogic.StaffInfo{
			Type: common2.STAFF_TYPE_ALL,
		})
	} else {
		for _, s := range input.Staff {
			if s.Type != common2.STAFF_TYPE_PERSON &&
				s.Type != common2.STAFF_TYPE_GROUP &&
				s.Type != common2.STAFF_TYPE_DEPARTMENT &&
				s.Type != common2.STAFF_TYPE_ALL {
				return common.ErrInvalidParameterOptionf("staff")
			} else if s.Type == common2.STAFF_TYPE_PERSON && s.UserId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.userId")
			} else if s.Type == common2.STAFF_TYPE_DEPARTMENT && s.DepartmentId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.departmentId")
			} else if s.Type == common2.STAFF_TYPE_GROUP && s.UserGroupId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.userGroupId")
			}
		}
	}
	return common.Validator.Struct(input)
}

type CreateWorkFlowTemplateOutput struct {
	WorkflowTemplateUuid string `json:"workflowTemplateUuid"`
	Version              string `json:"version"`
}

type EnableWorkFlowTemplateInput struct {
	WorkflowTemplateUuids []string `json:"workflowTemplateUuids" validate:"required,gt=0"`
}

func (input *EnableWorkFlowTemplateInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input) //需要根据类型来绑定
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type EnableWorkFlowTemplateOutput struct {
}

type DisableWorkFlowTemplateInput struct {
	WorkflowTemplateUuids []string `json:"workflowTemplateUuids" validate:"required,gt=0"`
}

func (input *DisableWorkFlowTemplateInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input) //需要根据类型来绑定
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type DisableWorkFlowTemplateOutput struct {
}

type EditWorkflowTagInput struct {
	WorkflowTemplate wtLogic.WorkflowTemplateInfo `json:"workflowTemplate" validate:"required"`
	TagIds           []int64                      `json:"tagIds" validate:"required"`
}

func (input *EditWorkflowTagInput) ParseInput(gctx *gin.Context) error {
	if err := gctx.ShouldBindJSON(input); err != nil {
		return err
	}
	if len(input.WorkflowTemplate.WorkflowTemplateUuid) == 0 {
		return common.ErrMissingAMandatoryParameterf("workflowTemplateUuid")
	}
	return common.Validator.Struct(input)
}

type StagingWorkFlowTemplateInput struct {
	TemplateUuid    string                       `json:"templateUuid" validate:"required,len=32"`
	Version         string                       `json:"version"`
	Publish         bool                         `json:"publish"` //是否保存草稿时同时发布
	GroupId         int64                        `json:"groupId"`
	Origin          common2.UserOriginEnum       `json:"origin"` //从哪些来源可以发起
	Staff           []*staffLogic.StaffInfo      `json:"staff"`  //哪些人可以使用该模板
	Name            common2.I18nString           `json:"name" validate:"required"`
	Description     common2.I18nString           `json:"description"`
	Nodes           []*wtLogic.NodeTemplateInput `json:"nodes" validate:"required,gt=0,dive"`
	Form            interface{}                  `json:"form"`
	RemoveDuplicate bool                         `json:"removeDuplicate"`
}

func (input *StagingWorkFlowTemplateInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input) //需要根据类型来绑定
	if err != nil {
		return err
	}
	if input.Name.MaxLength() == 0 {
		return common.ErrMissingAMandatoryParameterf("name")
	} else if input.Name.MaxLength() > common.MaxNameLength {
		return common.ErrInvalidFieldf("name")
	} else if err := input.Name.Validate(); err != nil {
		return err
	}
	if input.Description.MaxLength() > common.MaxDescLength {
		return common.ErrInvalidFieldf("description")
	} else if err := input.Description.Validate(); err != nil {
		return err
	}
	for _, n := range input.Nodes {
		if err = n.Validate(); err != nil {
			return err
		}
	}
	if input.GroupId == 0 {
		return common.ErrMissingAMandatoryParameterf("workflowTemplateGroupId")
	}
	//TODO 默认去重
	input.RemoveDuplicate = true
	if len(input.Origin) == 0 {
		input.Origin = common2.OriginBpm
	} else if input.Origin != common2.OriginBpm && input.Origin != common2.OriginInternalSystem {
		return common.ErrInvalidFieldf("origin")
	}
	if len(input.Staff) == 0 {
		input.Staff = append(input.Staff, &staffLogic.StaffInfo{
			Type: common2.STAFF_TYPE_ALL,
		})
	} else {
		for _, s := range input.Staff {
			if s.Type != common2.STAFF_TYPE_PERSON &&
				s.Type != common2.STAFF_TYPE_GROUP &&
				s.Type != common2.STAFF_TYPE_DEPARTMENT &&
				s.Type != common2.STAFF_TYPE_ALL {
				return common.ErrInvalidParameterOptionf("staff")
			} else if s.Type == common2.STAFF_TYPE_PERSON && s.UserId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.userId")
			} else if s.Type == common2.STAFF_TYPE_DEPARTMENT && s.DepartmentId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.departmentId")
			} else if s.Type == common2.STAFF_TYPE_GROUP && s.UserGroupId == 0 {
				return common.ErrMissingAMandatoryParameterf("staff.userGroupId")
			}
		}
	}

	return common.Validator.Struct(input)
}

type StagingWorkFlowTemplateOutput struct {
	UpdateWorkflowTemplateResp
}

type UpdateWorkflowTemplateResp struct {
	WorkflowTemplateUuid string `json:"workflowTemplateUuid"`
	Version              string `json:"version"`
}

type ListWorkFlowTemplateInput struct {
	Offset    int64                                 `json:"offset" validate:"min=0"`
	Limit     int64                                 `json:"limit" validate:"min=0"`
	NonPaged  bool                                  `json:"nonPaged"`
	Condition wtLogic.WorkflowTemplateListCondition `json:"condition"`
}

func (input *ListWorkFlowTemplateInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input) //需要根据类型来绑定
	if err != nil {
		return err
	}
	input.Condition.WorkflowTemplateName = strings.Replace(input.Condition.WorkflowTemplateName, "%", "\\%", -1)
	input.Condition.WorkflowTemplateName = strings.Replace(input.Condition.WorkflowTemplateName, "_", "\\_", -1)
	if input.NonPaged {
		input.Offset = 0
		input.Limit = 100000
	}
	return common.Validator.Struct(input)
}

type ListWorkFlowTemplateOutput struct {
	Count int64                           `json:"count"`
	Items []*wtLogic.WorkflowTemplateInfo `json:"items"`
}

type WorkflowTemplateDetailOutput struct {
	WorkflowTemplateUuid string                      `json:"workflowTemplateUuid"`
	GroupId              int64                       `json:"groupId"`
	Name                 common2.I18nString          `json:"name"`
	Description          common2.I18nString          `json:"description"`
	Nodes                []*wtLogic.NodeTemplateInfo `json:"nodes"`
	Form                 interface{}                 `json:"form"` //TODO 二期去掉这个全局字段
	CurrentVersion       string                      `json:"currentVersion"`
	Staff                []*staffLogic.StaffInfo     `json:"staff"`
	Versions             []string                    `json:"versions"`
	CreateTime           int64                       `json:"createTime"`
	UpdateTime           int64                       `json:"updateTime"`
}

type DeleteWorkFlowTemplateInput struct {
	WorkflowTemplateUuids []string `json:"workflowTemplateUuids" validate:"required,gt=0"`
	Staging               bool     `json:"staging"`
}

func (input *DeleteWorkFlowTemplateInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input) //需要根据类型来绑定
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type DeleteWorkFlowTemplateOutput struct {
	//WorkflowTemplates []tLogic.WorkflowTemplateInfo `json:"workflowTemplates"`
}

type CreateTemplateGroupInput struct {
	Name        common2.I18nString `json:"name" validate:"required"`
	Description common2.I18nString `json:"description" validate:"required"`
}

func (input *CreateTemplateGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input) //需要根据类型来绑定
	if err != nil {
		return err
	}
	if input.Name.MaxLength() == 0 {
		return common.ErrMissingAMandatoryParameterf("name")
	} else if input.Name.MaxLength() > common.MaxNameLength {
		return common.ErrInvalidFieldf("name")
	} else if err := input.Name.Validate(); err != nil {
		return err
	}
	if input.Description.MaxLength() > common.MaxDescLength {
		return common.ErrInvalidFieldf("description")
	} else if err := input.Description.Validate(); err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type CreateTemplateGroupOutput struct {
	WorkflowTemplateGroupId int64 `json:"workflowTemplateGroupId"`
}

type DeleteTemplateGroupInput struct {
	WorkflowTemplateGroupIds []int64 `json:"workflowTemplateGroupIds" validate:"required,gt=0"`
}

func (input *DeleteTemplateGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type DeleteTemplateGroupOutput struct {
}

type EnableTemplateGroupInput struct {
	WorkflowTemplateGroupIds []int64 `json:"workflowTemplateGroupIds" validate:"required,gt=0"`
}

func (input *EnableTemplateGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type EnableTemplateGroupOutput struct {
}

type DisableTemplateGroupInput struct {
	WorkflowTemplateGroupIds []int64 `json:"workflowTemplateGroupIds" validate:"required,gt=0"`
}

func (input *DisableTemplateGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type DisableTemplateGroupOutput struct {
}

type UpdateWorkflowTemplateGroupInput struct {
	WorkflowTemplateGroupId int64              `json:"workflowTemplateGroupId" validate:"required,gt=0"`
	Name                    common2.I18nString `json:"name"`
	Description             common2.I18nString `json:"description"`
	WorkflowTemplateUuids   []string           `json:"workflowTemplateUuids" validate:"gte=0,dive,len=32"`
}

func (input *UpdateWorkflowTemplateGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	if input.Name == nil {
		return common.ErrMissingAMandatoryParameterf("name")
	} else if input.Name.MaxLength() > common.MaxNameLength {
		return common.ErrInvalidFieldf("name")
	} else if input.Description.MaxLength() > common.MaxDescLength {
		return common.ErrInvalidFieldf("description")
	} else if err := input.Name.Validate(); err != nil {
		return err
	} else if err := input.Description.Validate(); err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type UpdateTemplateGroupOutput struct {
}

type ListTemplateGroupInput struct {
	Offset    int64                                      `json:"offset" validate:"min=0"`
	Limit     int64                                      `json:"limit" validate:"min=0"`
	Condition wtLogic.WorkflowTemplateGroupListCondition `json:"condition"`
}

func (input *ListTemplateGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	input.Condition.WorkflowTemplateGroupName = strings.Replace(input.Condition.WorkflowTemplateGroupName, "%", "\\%", -1)
	input.Condition.WorkflowTemplateGroupName = strings.Replace(input.Condition.WorkflowTemplateGroupName, "_", "\\_", -1)
	return common.Validator.Struct(input)
}

type ListTemplateGroupOutput struct {
	Count int64                                `json:"count"`
	Items []*wtLogic.WorkflowTemplateGroupInfo `json:"items"`
}

type GetTemplateGroupDetailInput struct {
	WorkflowTemplateGroupId int64 `json:"workflowTemplateGroupId" validate:"gt=0"`
}

func (input *GetTemplateGroupDetailInput) ParseInput(gctx *gin.Context) error {
	var err error
	input.WorkflowTemplateGroupId, err = strconv.ParseInt(gctx.Query("workflowTemplateGroupId"), 10, 64)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type ListWorkflowArchiveInput struct {
	Offset    int64                         `json:"offset" validate:"gte=0"`
	Limit     int64                         `json:"limit" validate:"gte=0"`
	Condition wrLogic.WorkflowListCondition `json:"condition"`
}

func (input *ListWorkflowArchiveInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	if len(input.Condition.State) > 0 {
		input.Condition.States = append(input.Condition.States, input.Condition.State)
	}
	if input.Condition.WorkflowTemplateGroupId > 0 {
		input.Condition.WorkflowTemplateGroupIds = append(input.Condition.WorkflowTemplateGroupIds, input.Condition.WorkflowTemplateGroupId)
	}
	return common.Validator.Struct(input)
}

type ListWorkflowArchiveOutput struct {
	Count int64                   `json:"count"`
	Items []*wrLogic.WorkflowInfo `json:"items"`
}

type WorkflowDetailArchiveInput struct {
	WorkflowUuid string `json:"workflowUuid" validate:"len=32"`
}

func (input *WorkflowDetailArchiveInput) ParseInput(gctx *gin.Context) error {
	input.WorkflowUuid = gctx.Query("workflowUuid")
	return common.Validator.Struct(input)
}

type GetTemplateDetailInput struct {
	WorkflowTemplateUuid string `json:"workflowTemplateUuid" validate:"gt=0"`
	Version              string `json:"version"`
	Staging              bool   `json:"staging"`
}

func (input *GetTemplateDetailInput) ParseInput(gctx *gin.Context) error {
	input.WorkflowTemplateUuid = gctx.Query("workflowTemplateUuid")
	input.Version = gctx.Query("version")
	input.Staging = gctx.Query("staging") == "true"
	return common.Validator.Struct(input)
}

type ValidateConditionInput struct {
	Condition string `json:"condition" validate:"gt=0"`
}

func (input *ValidateConditionInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type WorkflowArchiveHandoverInput struct {
	ProcessUuid    string                  `json:"processUuid" validate:"len=32"`
	Explain        string                  `json:"explain"`        //转交备注
	ExplainVisible bool                    `json:"explainVisible"` //转交是否对其他审批人可见
	Staff          []*staffLogic.StaffInfo `json:"staff"`          //要转交的用户信息
}

func (input *WorkflowArchiveHandoverInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *WorkflowArchiveHandoverInput) Validate() error {
	if len(input.ProcessUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	} else if len(input.Staff) == 0 {
		return common.ErrMissingAMandatoryParameterf("staff")
	} else if utf8.RuneCountInString(input.Explain) > common.MaxCommentLength {
		return common.ErrInvalidFieldf("explain")
	}
	if input.Staff[0].Type != common2.STAFF_TYPE_PERSON {
		return common.ErrInvalidParameterOptionf("staff.type")
	}
	return nil
}

type WorkflowArchiveHandoverOutput struct {
	ProcessUuid string `json:"processUuid,omitempty"`
}

type WorkflowArchiveRollbackInput struct {
	ProcessUuid string `json:"processUuid" validate:"len=32"`
	Comment     string `json:"comment"` //评论，最长一千字
	NodeUuid    string `json:"nodeUuid"`
	NodeName    string `json:"nodeName"`
}

func (input *WorkflowArchiveRollbackInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *WorkflowArchiveRollbackInput) Validate() error {
	if len(input.ProcessUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	} else if len(input.NodeUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("nodeUuid")
	} else if utf8.RuneCountInString(input.Comment) > common.MaxCommentLength {
		return common.ErrInvalidFieldf("comment")
	}
	return nil
}
