/**
 * @note
 * 流程归档
 *
 * <AUTHOR>
 * @date 	2020-04-26
 */
package back

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/internal/common"
	pLogic "gitlab.docsl.com/security/bpm/internal/logic/process"
	wrLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/runtime"
	wmService "gitlab.docsl.com/security/bpm/internal/srvs/workflow_message"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func ListWorkFlowArchive(gctx *gin.Context) {
	input := &ListWorkflowArchiveInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &ListWorkflowArchiveOutput{}
	ctx := GetHCtx(gctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	workflowInfos, cnt, err := workflowOperator.ListWorkFlow(input.Condition, input.Offset, input.Limit, true)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}
	output.Count = cnt

	output.Items = workflowInfos
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func GetWorkflowArchiveDetail(gctx *gin.Context) {
	input := &WorkflowDetailArchiveInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	detail, err := workflowOperator.QueryWorkflowDetail(input.WorkflowUuid, false)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(detail))
	return
}

func WorkflowArchiveHandover(gctx *gin.Context) {
	input := &WorkflowArchiveHandoverInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	//前置检查一下
	processTable, err := processOperator.QueryProcessRuntimeByUuid(input.ProcessUuid)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	} else if len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_NO_PERMISSION_OF_PORTAL_PROCESS, err))
		return
	} else if processTable.State != common.PROCESS_STATE_UNAPPROVED &&
		processTable.State != common.PROCESS_STATE_UNDONE {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS_STATE_NOT_CORRECT))
		return
	}
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	_, err = workflowOperator.QueryWorkflowTableByUuid(processTable.WorkflowUuid)
	if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	}
	//校验是否有权限
	newProcessUuid, err := processOperator.AddSubApprovalProcess(common.PROCESS_ACTION_HANDOVER,
		common.PROCESS_SUBTYPE_HANDOVER, processTable, StringEmpty, input.Explain, input.ExplainVisible, input.Staff)
	if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(WorkflowArchiveHandoverOutput{ProcessUuid: newProcessUuid}))
	messenger := wmService.NewWorkflowMessenger(ctx) //懒触发处理机制
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{
		WorkflowUuid: processTable.WorkflowUuid,
		NodeUuid:     processTable.NodeUuid,
		TraceId:      ctx.Log().GetTraceId(),
	})
	return
}

func WorkflowArchiveRollback(gctx *gin.Context) {
	input := &WorkflowArchiveRollbackInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	//前置检查一下
	processTable, err := processOperator.QueryProcessRuntimeByUuid(input.ProcessUuid)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_ROLLBACK_PROCESS, err))
		return
	} else if len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_NO_PERMISSION_OF_PORTAL_PROCESS, err))
		return
	} else if processTable.State != common.PROCESS_STATE_UNAPPROVED &&
		processTable.State != common.PROCESS_STATE_UNDONE {
		SetRet(gctx, NewError(ERR_CANT_DO_APPROVED_PROCESS))
		return
	}
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	_, err = workflowOperator.QueryWorkflowTableByUuid(processTable.WorkflowUuid)
	if err != nil {
		SetRet(gctx, NewError(ERR_ROLLBACK_PROCESS, err))
		return
	}
	err = processOperator.RollbackProcess(processTable, input.Comment, input.NodeUuid, input.NodeName)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err == ErrNoPermission {
		SetRet(gctx, NewError(ERR_NO_PERMISSION, err))
		return
	} else if err == common.ErrRollbackToSelf {
		SetRet(gctx, NewError(ERR_CANNOT_ROLLBACK_TO_SELF_NODE, err))
		return
	} else if err == common.ErrTransferFailed || err == common.ErrWorkflowState || err == common.ErrInvalidNodeState {
		SetRet(gctx, NewError(ERR_WORKFLOW_OCCUPIED, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_ROLLBACK_PROCESS, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
	messenger := wmService.NewWorkflowMessenger(ctx) //懒触发处理机制
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{
		WorkflowUuid: processTable.WorkflowUuid,
		NodeUuid:     input.NodeUuid,
		TraceId:      ctx.Log().GetTraceId(),
	})
	return
}
