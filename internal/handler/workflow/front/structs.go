/**
* @note
*
* @author: libi
* @date: 2019/12/06
 */

package front

import (
	"github.com/gin-gonic/gin"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wrLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/runtime"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"strings"
	"unicode/utf8"
)

type ListWorkflowInput struct {
	Offset    int64                         `json:"offset" validate:"gte=0"`
	Limit     int64                         `json:"limit" validate:"gte=0"`
	Condition wrLogic.WorkflowListCondition `json:"condition"`
}

func (input *ListWorkflowInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	if len(input.Condition.State) > 0 {
		input.Condition.States = append(input.Condition.States, input.Condition.State)
	}
	if input.Condition.WorkflowTemplateGroupId > 0 {
		input.Condition.WorkflowTemplateGroupIds = append(input.Condition.WorkflowTemplateGroupIds, input.Condition.WorkflowTemplateGroupId)
	}
	input.Condition.WorkflowName = strings.Replace(input.Condition.WorkflowName, "%", "\\%", -1)
	input.Condition.WorkflowName = strings.Replace(input.Condition.WorkflowName, "_", "\\_", -1)
	return common.Validator.Struct(input)
}

type ListWorkflowOutput struct {
	Count int64                   `json:"count"`
	Items []*wrLogic.WorkflowInfo `json:"items"`
}

type WithdrawWorkflowInput struct {
	WorkflowUuids []string `json:"workflowUuids"`
}

func (input *WithdrawWorkflowInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *WithdrawWorkflowInput) Validate() error {
	if len(input.WorkflowUuids) == 0 {
		return common.ErrMissingAMandatoryParameterf("WorkflowUuid")
	}
	for _, uuid := range input.WorkflowUuids {
		if len(uuid) != 32 {
			return common.ErrInvalidFieldf("WorkflowUuid")
		}
	}
	return nil
}

type WorkflowDetailInput struct {
	WorkflowUuid        string `json:"workflowUuid" validate:"len=32"`
	RelatedWorkflowUuid string `json:"relatedWorkflowUuid"`
}

func (input *WorkflowDetailInput) ParseInput(gctx *gin.Context) error {
	input.WorkflowUuid = gctx.Query("workflowUuid")
	input.RelatedWorkflowUuid = gctx.Query("relatedWorkflowUuid")
	if len(input.WorkflowUuid) == 0 {
		_ = gctx.ShouldBindJSON(input)
	}
	return common.Validator.Struct(input)
}

type WithdrawWorkFlowOutput struct{}

type CreateWorkflowInput struct {
	WorkflowTemplateUuid string                         `json:"workflowTemplateUuid" validate:"len=32"`
	WorkflowName         bpmCom.I18nString              `json:"workflowName"`
	Data                 interface{}                    `json:"data"`
	Approver             []*wrLogic.SelfSelectStaffInfo `json:"approver"`
	Notifier             []*wrLogic.SelfSelectStaffInfo `json:"notifier"`
	Transactor           []*wrLogic.SelfSelectStaffInfo `json:"transactor"`
}

func (input *CreateWorkflowInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	input.Approver = append(input.Approver, input.Transactor...)
	return input.Validate()
}

func (input *CreateWorkflowInput) Validate() error {
	if len(input.WorkflowTemplateUuid) == 0 {
		return common.ErrMissingAMandatoryParameterf("WorkflowTemplateUuid")
	} else if input.Data == nil {
		return common.ErrMissingAMandatoryParameterf("data")
	} else if input.WorkflowName.MaxLength() > common.MaxNameLength {
		return common.ErrInvalidFieldf("workflowName")
	}
	return nil
}

type CreateWorkflowOutput struct {
	WorkflowUuid     string            `json:"workflowUuid,omitempty"`
	WorkflowSerialId int64             `json:"workflowSerialId,omitempty"`
	WorkflowName     bpmCom.I18nString `json:"workflowName,omitempty"`
}

type WorkflowTemplateDetailInput struct {
	WorkflowTemplateUuid string `json:"workflowTemplateUuid" validate:"len=32"`
}

func (input *WorkflowTemplateDetailInput) ParseInput(gctx *gin.Context) error {
	input.WorkflowTemplateUuid = gctx.Query("workflowTemplateUuid")
	return common.Validator.Struct(input)
}

type PreHandleWorkflowInput CreateWorkflowInput

func (input *PreHandleWorkflowInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *PreHandleWorkflowInput) Validate() error {
	if _, ok := input.Data.(map[string]interface{}); !ok {
		return common.ErrMissingAMandatoryParameterf("data")
	} else {
		return (*CreateWorkflowInput)(input).Validate()
	}
}

type UrgeWorkflowInput struct {
	WorkflowUuid string `json:"workflowUuid" validate:"len=32"`
}

func (input *UrgeWorkflowInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type UrgeWorkflowOutput struct {
}

type ReCreateWorkflowInput struct {
	WorkflowUuid string      `json:"workflowUuid" validate:"len=32"`
	Data         interface{} `json:"data"`
}

func (input *ReCreateWorkflowInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *ReCreateWorkflowInput) Validate() error {
	if len(input.WorkflowUuid) == 0 {
		return common.ErrMissingAMandatoryParameterf("WorkflowTemplateUuid")
	} else if input.Data == nil {
		return common.ErrMissingAMandatoryParameterf("data")
	}
	return nil
}

type CompleteWorkflowInput struct {
	WorkflowUuid string                         `json:"workflowUuid" validate:"len=32"`
	Remark       string                         `json:"remark"`
	File         []*processModel.RemarkFileInfo `json:"file"`
	VisibleStaff []*staffLogic.StaffInfo        `json:"visibleStaff"`
}

func (input *CompleteWorkflowInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *CompleteWorkflowInput) Validate() error {
	if len(input.WorkflowUuid) == 0 {
		return common.ErrMissingAMandatoryParameterf("WorkflowTemplateUuid")
	} else if utf8.RuneCountInString(input.Remark) > common.MaxCommentLength {
		return common.ErrInvalidFieldf("remark")
	}
	return nil
}
