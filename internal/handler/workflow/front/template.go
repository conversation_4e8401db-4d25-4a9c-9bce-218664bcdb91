/**
 * @note
 * 前台查看模板列表
 *
 * <AUTHOR>
 * @date 	2019-12-08
 */
package front

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/internal/common"
	wtLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/template"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func ListAllWorkflowTemplate(gctx *gin.Context) {
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	result, err := tmplOperator.ListWorkFlowTemplateForC()
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TEMPLATE, err))
	} else {
		SetRet(gctx, NewError(ERR_OK, result))
	}
	return
}

func WorkflowTemplateDetail(gctx *gin.Context) {
	input := WorkflowTemplateDetailInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	detail, err := tmplOperator.GetWorkFlowTemplateDetailForC(input.WorkflowTemplateUuid, false, nil)
	if err != nil {
		if err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_NOT_EXIST, err))
		} else {
			SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TEMPLATE, err))
		}
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(detail))
	return
}

/* @note
 * 2020-2-11
 * 根据用户所填内容，对工作流进行剪枝（预处理）
 */
func PreHandleWorkflow(gctx *gin.Context) {
	input := &PreHandleWorkflowInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	detail, err := tmplOperator.GetWorkFlowTemplateDetailForC(input.WorkflowTemplateUuid, true, input.Data)
	if err != nil {
		if err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_WORKFLOW_TEMPLATE_NOT_EXIST, err))
		} else if err == common.ErrWorkflowImpossibleAfterCut {
			SetRet(gctx, NewError(ERR_WORKFLOW_IMPOSSIBLE_AFTER_CUT, err))
		} else {
			SetRet(gctx, NewError(ERR_PREHANDLE_WORKFLOW, err))
		}
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(detail))
	return
}
