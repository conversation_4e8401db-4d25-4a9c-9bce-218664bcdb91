/**
* @note
* workflow创建、查询、撤回等操作
*
*
* @author: libi
* @date: 2019/11/28
 */

package front

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/internal/common"
	pLogic "gitlab.docsl.com/security/bpm/internal/logic/process"
	wrLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/runtime"
	wmService "gitlab.docsl.com/security/bpm/internal/srvs/workflow_message"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func ListWorkFlow(gctx *gin.Context) {
	input := &ListWorkflowInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &ListWorkflowOutput{}
	ctx := GetHCtx(gctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	workflowInfos, cnt, err := workflowOperator.ListWorkFlow(input.Condition, input.Offset, input.Limit, false)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}
	output.Count = cnt

	output.Items = workflowInfos
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func WithdrawWorkFlow(gctx *gin.Context) {
	input := &WithdrawWorkflowInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	output := &WithdrawWorkFlowOutput{}
	ctx := GetHCtx(gctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	err := workflowOperator.WithdrawWorkflow(input.WorkflowUuids)
	if err == ErrNoPermission {
		SetRet(gctx, NewError(ERR_WITHDRAW_OTHERS_WORKFLOW, err))
		return
	} else if err == common.ErrWorkflowState {
		SetRet(gctx, NewError(ERR_WORKFLOW_STATE_NOT_CORRECT, err))
		return
	}
	if err != nil {
		SetRet(gctx, NewError(ERR_WITHDRAW_WORKFLOW, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func GetWorkflowDetail(gctx *gin.Context) {
	input := &WorkflowDetailInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	detail, err := workflowOperator.QueryWorkflowDetail(input.WorkflowUuid, false)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}
	if detail.User != nil && detail.User.UserId != ctx.User().UserId { //需要校验是否是有权限的关联审批单

		if len(input.RelatedWorkflowUuid) == 0 {
			input.RelatedWorkflowUuid = input.WorkflowUuid
			//SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
			//return
		}
		if err = workflowOperator.ValidateRelatedWorkflow(input.RelatedWorkflowUuid, input.WorkflowUuid); err != nil {
			SetRet(gctx, NewError(ERR_NO_PERMISSION, err))
			return
		}
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(detail))
	return
}

func CreateWorkflow(gctx *gin.Context) {
	input := &CreateWorkflowInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	output := &CreateWorkflowOutput{}
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	var err error
	output.WorkflowUuid, output.WorkflowSerialId, output.WorkflowName, err = workflowOperator.CreateWorkflow(input.WorkflowTemplateUuid, input.WorkflowName,
		input.Data, input.Approver, input.Notifier)
	if err == common.ErrWorkflowImpossibleAfterCut {
		SetRet(gctx, NewError(ERR_WORKFLOW_IMPOSSIBLE_AFTER_CUT, err))
		return
	} else if err == common.ErrCannotFindApprovers {
		SetRet(gctx, NewError(ERR_WORKFLOW_CANNOT_FIND_APPROVERS, err))
		return
	} else if err == common.ErrRelateWorkflowInputError {
		SetRet(gctx, NewError(ERR_RELATE_WORKFLOW_INPUT_ERROR, err))
		return
	} else if err == common.ErrMissingSelfSelectApprovers {
		SetRet(gctx, NewError(ERR_MISSING_SELF_SELECT_APPROVERS, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_WORKFLOW, err))
		return
	}
	messenger := wmService.NewWorkflowMessenger(ctx)
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{WorkflowUuid: output.WorkflowUuid, TraceId: ctx.Log().GetTraceId()})
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func ReCreateWorkflow(gctx *gin.Context) {
	input := &ReCreateWorkflowInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	var err error
	err = workflowOperator.ReCreateWorkflow(input.WorkflowUuid,
		input.Data)
	if err == ErrNoPermission {
		SetRet(gctx, NewError(ERR_NO_PERMISSION, err))
		return
	} else if err == common.ErrWorkflowState {
		SetRet(gctx, NewError(ERR_CANNOT_RECREATE_NON_RESET_WORKFLOW, err))
		return
	} else if err == common.ErrRelateWorkflowInputError {
		SetRet(gctx, NewError(ERR_RELATE_WORKFLOW_INPUT_ERROR, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_RECREATE_WORKFLOW, err))
		return
	}
	messenger := wmService.NewWorkflowMessenger(ctx)
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{WorkflowUuid: input.WorkflowUuid, TraceId: ctx.Log().GetTraceId()})
	SetRet(gctx, NewError(ERR_OK))
	return
}

func CompleteWorkflow(gctx *gin.Context) {
	input := &CompleteWorkflowInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	var err error
	err = workflowOperator.CompleteWorkflow(input.WorkflowUuid,
		input.Remark, input.File, input.VisibleStaff)
	if err == ErrNoPermission {
		SetRet(gctx, NewError(ERR_NO_PERMISSION, err))
		return
	} else if err == common.ErrWorkflowState {
		SetRet(gctx, NewError(ERR_CANNOT_COMPLETE_NON_NEED_COMPLETE_WORKFLOW, err))
		return
	} else if err == common.ErrRelateWorkflowInputError {
		SetRet(gctx, NewError(ERR_RELATE_WORKFLOW_INPUT_ERROR, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_COMPLETE_WORKFLOW, err))
		return
	}
	messenger := wmService.NewWorkflowMessenger(ctx)
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{WorkflowUuid: input.WorkflowUuid, TraceId: ctx.Log().GetTraceId()})
	SetRet(gctx, NewError(ERR_OK))
	return
}

func UrgeWorkflow(gctx *gin.Context) {
	input := &UrgeWorkflowInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	undoProcesses, err := processOperator.QueryProcessRuntimeByWorkflowUuid(input.WorkflowUuid)
	if err != nil {
		SetRet(gctx, NewError(ERR_URGE_WORKFLOW, err))
		return
	} else if len(undoProcesses) == 0 {
		SetRet(gctx, NewError(ERR_OK))
		return
	}
	output := &UrgeWorkflowOutput{}
	var processUuids []string
	for _, process := range undoProcesses {
		if process.State == common.PROCESS_STATE_UNAPPROVED ||
			process.State == common.PROCESS_STATE_UNDONE {
			processUuids = append(processUuids, process.Uuid)
		}
	}
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	err = workflowOperator.UrgeMyflow(processUuids)
	if err != nil {
		SetRet(gctx, NewError(ERR_URGE_WORKFLOW, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}
