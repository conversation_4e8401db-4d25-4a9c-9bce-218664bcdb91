/**
 * @note
 * template_group
 *
 * <AUTHOR>
 * @date 	2020-05-25
 */
package front

import (
	"github.com/gin-gonic/gin"
	wtLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/template"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func ListTemplateGroupForC(gctx *gin.Context) {
	ctx := GetHCtx(gctx)
	tmplOperator := wtLogic.NewWorkflowTmplOperator(ctx)
	templateGroupInfos, _, err := tmplOperator.ListTemplateGroup(wtLogic.WorkflowTemplateGroupListCondition{}, 0, 10000)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TEMPLATE_GROUP, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK, templateGroupInfos))
	return
}
