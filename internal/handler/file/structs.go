/**
 * @note
 * structs.go
 *
 * <AUTHOR>
 * @date 	2019-11-25
 */
package file

import (
	"regexp"

	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

var (
	filenameRegex = regexp.MustCompile("\\.(pdf|xls|xlsx|doc|docx|zip|csv|jpg|png|jpeg|bmp|txt|ppt|pptx|rar|msg)$")
)

type UploadInput struct {
}

type UploadOutput struct {
	S3ObjectName string `json:"s3ObjectName"`
	S3ObjectSize int64  `json:"s3ObjectSize"`
}

type GetPreSignInput struct {
	FileName string `json:"fileName"`
	Origin   string `json:"origin"`
}

func (input *GetPreSignInput) ParseInput(gctx *gin.Context) error {
	input.FileName = gctx.Query("fileName")
	input.Origin = gctx.Query("origin")
	if len(input.FileName) == 0 {
		err := gctx.ShouldBindJSON(input)
		if err != nil {
			return err
		}
	}
	return input.Validate()
}

func (input *GetPreSignInput) Validate() error {
	if len(input.FileName) == 0 {
		return common.ErrMissingAMandatoryParameterf("fileName")
	}
	return nil
}

type GetPreSignOutput struct {
	PreSignUrl  string `json:"preSignUrl"`
	ContentType string `json:"contentType"`
}
