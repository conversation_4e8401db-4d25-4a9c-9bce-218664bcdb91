package file

import (
	"errors"
	apiLogic "gitlab.docsl.com/security/bpm/internal/logic/api"
	"mime"
	"mime/multipart"
	"net/http"
	"path"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	. "gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/aws_s3"
)

/**
 * 上传文件到S3
 *
 */
func UploadFile(gctx *gin.Context) {
	file, header, err := gctx.Request.FormFile("uploadFile")
	if err != nil {
		SetRet(gctx, NewError(ERR_S3_UPLOAD_FAILED, err))
		return
	}
	defer file.Close()
	contentType, err := fileHeaderValidate(header)
	if err != nil {
		SetRet(gctx, NewError(ERR_S3_UPLOAD_FAILED, err))
		return
	}
	s3Client := aws_s3.NewS3Client(GetHCtx(gctx).Log())
	s3ObjectName, err := s3Client.Upload(file, header.Filename, contentType)
	if err != nil {
		SetRet(gctx, NewError(ERR_S3_UPLOAD_FAILED, err))
		return
	}
	output := &UploadOutput{
		S3ObjectName: s3ObjectName,
		S3ObjectSize: header.Size,
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
}

// 1. 文件后缀限制,只能为 pdf|xls|xlsx|doc|docx|zip|csv|jpg|png|jpeg|bmp|txt|ppt|rar|msg
// 2. MIME类型与文件后缀名必须匹配
func fileHeaderValidate(header *multipart.FileHeader) (contentType string, err error) {
	if !filenameRegex.MatchString(strings.ToLower(header.Filename)) {
		return StringEmpty, errors.New("文件名后缀无效")
	}
	fileNameSuffix := path.Ext(header.Filename)
	contentType = header.Header.Get("Content-Type")
	extensions, err := mime.ExtensionsByType(contentType)
	if err != nil {
		return StringEmpty, err
	}
	isContentTypeMatchFileSuffix := false
	for _, ext := range extensions {
		if fileNameSuffix == ext {
			isContentTypeMatchFileSuffix = true
		}
	}
	if !isContentTypeMatchFileSuffix {
		return StringEmpty, errors.New("MIME类型与文件后缀名不匹配")
	}
	return contentType, nil
}

func GetPreSignUrl(gctx *gin.Context) {
	input := &GetPreSignInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	var preSignUrl, contentType string
	var err error
	if input.Origin != StringEmpty { //表示来源于其他系统的S3
		externalS3Config, ok := aws_s3.GetConfig().External[input.Origin]
		if !ok {
			SetRet(gctx, NewError(ERR_QUERY_APP_AUTH_KEY))
			return
		}
		preSignUrl, err = getUrlFromExternalS3(ctx, externalS3Config.Host, externalS3Config.Url, input.FileName, input.Origin)
		if ext := path.Ext(input.FileName); ext != StringEmpty {
			if t := mime.TypeByExtension(ext); t != StringEmpty {
				contentType = t
			}
		}
	} else { //本系统的s3
		s3Client := aws_s3.NewS3Client(GetHCtx(gctx).Log())
		//先判断文件是否存在于S3
		if _, err := s3Client.IsObjectExist(input.FileName); err != nil {
			SetRet(gctx, NewError(ERR_S3_GET_FILE_PRESIGN_URL))
			return
		}
		//pre sign url
		preSignUrl, contentType, err = s3Client.GetPreSignUrl(input.FileName)
	}
	if err != nil {
		SetRet(gctx, NewError(ERR_S3_GET_FILE_PRESIGN_URL, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(&GetPreSignOutput{PreSignUrl: preSignUrl, ContentType: contentType}))
	return
}

func getUrlFromExternalS3(ctx HContextIface, host, url,
	fileName, origin string) (preSignUrl string, err error) {
	apiOperator := apiLogic.NewApiOperator(ctx)
	resp, code, err := apiOperator.SignAndDo(host+url, http.MethodPost,
		origin, map[string]interface{}{"filename": fileName}, nil, 10*time.Second, 3)
	if err != nil {
		return StringEmpty, err
	} else if code != http.StatusOK {
		return StringEmpty, ErrHttpResponse
	}
	var result apiLogic.ApiResult
	err = JsonStringDecode(resp, &result)
	if err != nil {
		return StringEmpty, err
	}
	if result.Code != http.StatusOK && result.Code != ERR_OK {
		return StringEmpty, ErrHttpResponse
	} else if preSignUrl, ok := result.Data.(string); !ok {
		return StringEmpty, ErrHttpResponse
	} else if len(preSignUrl) == 0 {
		return StringEmpty, ErrHttpResponse
	} else {
		return preSignUrl, nil
	}
}

func GetImageStream(gctx *gin.Context) {
	objectName := gctx.Query("fileName")
	if objectName == StringEmpty {
		SetRet(gctx, NewError(ERR_S3_GET_IMAGE_STREAM_URL))
		return
	}
	s3Client := aws_s3.NewS3Client(GetHCtx(gctx).Log())
	imageFile, contentType, err := s3Client.GetFileStream(objectName)
	if err != nil {
		return
	}
	gctx.Header("Content-Type", contentType)
	gctx.Writer.WriteString(string(imageFile))
}
