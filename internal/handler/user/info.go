package user

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

func GetUserInfo(gctx *gin.Context) {
	ctx := common.GetHCtx(gctx)
	user := ctx.User()
	common.SetRet(gctx, common.NewError(common.ERR_OK, user))
	return
}

func GetUserPermission(gctx *gin.Context) {
	/*
		isMenu := gctx.Query("isMenu")
		menu := false
		if isMenu == "true" {
			menu = true
		}
		ctx := common.GetHCtx(gctx)
		user := ctx.User()
		c, err := cas.NewCASOpenClient(ctx.Log())
		if err != nil {
			common.SetRet(gctx, common.NewError(common.ERR_SYS))
			return
		}
		r, err := c.UserPermissionList(user.UserId, menu)
		if err != nil {
			common.SetRet(gctx, common.NewError(common.ERR_SYS))
			return
		}
	*/
	common.SetRet(gctx, common.NewError(common.ERR_OK))
	return
}
