/**
 * @note
 * dept
 *
 * <AUTHOR>
 * @date 	2020-02-12
 */
package staff

import (
	"github.com/gin-gonic/gin"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func DepartmentTree(gctx *gin.Context) {
	ctx := GetHCtx(gctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	deptInfos, err := staffOperator.GetAllDeptInfo()
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_DEPARTMENT_TREE, err))
		return
	}
	outputs := make([]*DepartmentInfoOutput, 0)
	deptIdOutputMap := make(map[int64]*DepartmentInfoOutput)
	for _, deptInfo := range deptInfos {
		output := &DepartmentInfoOutput{
			DepartmentId:      deptInfo.DepartmentId,
			Name:              deptInfo.Name,
			SuperId:           deptInfo.SuperId,
			SuperName:         deptInfo.SuperName,
			Code:              deptInfo.Code,
			ManagerId:         deptInfo.ManagerId,
			ManagerEmployeeId: deptInfo.ManagerEmployeeId,
			ManagerAccount:    deptInfo.ManagerAccount,
			//ManagerEmail:      deptInfo.ManagerEmail,
			ManagerNameZh: deptInfo.ManagerNameZh,
			ManagerNameEn: deptInfo.ManagerNameEn,
		}
		if output.SuperId == 0 { //根节点
			outputs = append(outputs, output)
		}
		deptIdOutputMap[output.DepartmentId] = output
	}
	//拼树
	for id := range deptIdOutputMap {
		if superDept, ok := deptIdOutputMap[deptIdOutputMap[id].SuperId]; ok {
			superDept.SubDepartment = append(superDept.SubDepartment, deptIdOutputMap[id])
		}
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(outputs))
	return
}

func DepartmentInfo(gctx *gin.Context) {
	input := &DepartmentInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	deptInfo, err := staffOperator.GetDeptInfoByDeptId(input.DepartmentId)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_DEPARTMENT_INFO, err))
		return
	}
	output := &DepartmentInfoOutput{
		DepartmentId:      deptInfo.DepartmentId,
		Name:              deptInfo.Name,
		SuperId:           deptInfo.SuperId,
		SuperName:         deptInfo.SuperName,
		Code:              deptInfo.Code,
		ManagerId:         deptInfo.ManagerId,
		ManagerEmployeeId: deptInfo.ManagerEmployeeId,
		ManagerAccount:    deptInfo.ManagerAccount,
		//ManagerEmail:      deptInfo.ManagerEmail,
		ManagerNameZh: deptInfo.ManagerNameZh,
		ManagerNameEn: deptInfo.ManagerNameEn,
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}
