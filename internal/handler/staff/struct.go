/**
 * @note
 * 相关结构体
 *
 * <AUTHOR>
 * @date 	2019-12-10
 */
package staff

import (
	bpmCpm "gitlab.docsl.com/security/bpm/internal/common"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	"strconv"

	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type UserInput struct {
	UserId int64 `json:"userId"`
}

func (input *UserInput) ParseInput(gctx *gin.Context) error {
	userIdStr := gctx.Query("userId")
	var err error
	input.UserId, err = strconv.ParseInt(userIdStr, 10, 64)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *UserInput) Validate() error {
	if input.UserId == 0 {
		return common.ErrInvalidFieldf("userId")
	}
	return nil
}

type UserInfoOutput struct {
	UserId       int64  `json:"userId"`
	Account      string `json:"account"`
	NameZh       string `json:"nameZh"`
	NameEn       string `json:"nameEn"`
	NameDisplay  string `json:"nameDisplay"`
	DepartmentId int64  `json:"departmentId"`
	EmployeeId   string `json:"employeeId"`
}

type DepartmentInput struct {
	DepartmentId int64 `json:"departmentId"`
}

type DepartmentInfoOutput struct {
	DepartmentId      int64  `json:"departmentId"`
	Name              string `json:"name"`
	SuperId           int64  `json:"superId"`
	SuperName         string `json:"superName"`
	Code              string `json:"code"`
	ManagerId         int64  `json:"managerId"`
	ManagerEmployeeId string `json:"managerEmployeeId"`
	ManagerAccount    string `json:"managerAccount"`
	//ManagerEmail      string                  `json:"managerEmail"`
	ManagerNameZh string                  `json:"managerNameZh"`
	ManagerNameEn string                  `json:"managerNameEn"`
	SubDepartment []*DepartmentInfoOutput `json:"subDepartment,omitempty"` //子部门信息
}

func (input *DepartmentInput) ParseInput(gctx *gin.Context) error {
	departmentIdStr := gctx.Query("departmentId")
	var err error
	input.DepartmentId, err = strconv.ParseInt(departmentIdStr, 10, 64)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *DepartmentInput) Validate() error {
	if input.DepartmentId == 0 {
		return common.ErrInvalidFieldf("departmentId")
	}
	return nil
}

type StaffIntegrateInput struct {
	Staff []*staffLogic.StaffInfo `json:"staff"` //综合查询
}

func (input *StaffIntegrateInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *StaffIntegrateInput) Validate() error {
	if len(input.Staff) == 0 {
		return common.ErrMissingAMandatoryParameterf("staff")
	}
	for _, staff := range input.Staff {
		switch staff.Type {
		case bpmCpm.STAFF_TYPE_PERSON:
			if staff.UserId == 0 {
				return common.ErrMissingAMandatoryParameterf("userId")
			}
		case bpmCpm.STAFF_TYPE_RELATION:
			if len(staff.Relation) == 0 {
				return common.ErrMissingAMandatoryParameterf("relation")
			}
		case bpmCpm.STAFF_TYPE_DEPARTMENT:
			if staff.DepartmentId == 0 {
				return common.ErrMissingAMandatoryParameterf("departmentId")
			}
		case bpmCpm.STAFF_TYPE_GROUP:
			if staff.UserGroupId == 0 {
				return common.ErrMissingAMandatoryParameterf("userGroupId")
			}
		default:
			return common.ErrInvalidParameterOptionf("staff.type")
		}
	}
	return nil
}

type StaffUserSearchInput struct {
	Name string `json:"name" validate:"required,gt=0"`
}

func (input *StaffUserSearchInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}
