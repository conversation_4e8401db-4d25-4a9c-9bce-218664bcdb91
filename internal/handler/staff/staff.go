/**
 * @note
 * 查询用户相关逻辑
 *
 * <AUTHOR>
 * @date 	2019-12-10
 */
package staff

import (
	"github.com/gin-gonic/gin"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func DepartmentUsers(gctx *gin.Context) {
	input := &DepartmentInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	userInfos, err := staffOperator.GetUserInfosByDeptId(input.DepartmentId, false)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_DEPARTMENT_USERS, err))
		return
	}
	outputs := make([]*UserInfoOutput, 0)
	for _, userInfo := range userInfos {
		if userInfo.Type == staffLogic.USER_TYPE_LEAVED {
			continue
		}
		outputs = append(outputs, &UserInfoOutput{
			UserId:       userInfo.UserId,
			Account:      userInfo.Account,
			NameZh:       userInfo.NameZh,
			NameEn:       userInfo.NameEn,
			NameDisplay:  userInfo.NameDisplay,
			DepartmentId: userInfo.DepartmentId,
			EmployeeId:   userInfo.EmployeeId,
		})
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(outputs))
	return
}

func StaffUserInfo(gctx *gin.Context) {
	input := &UserInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	userInfo, err := staffOperator.GetUserInfoByUserId(input.UserId)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_STAFF_USER_INFO, err))
		return
	} else if userInfo.Type == staffLogic.USER_TYPE_LEAVED {
		SetRet(gctx, NewError(ERR_STAFF_USER_LEAVED, err))
		return
	}
	output := &UserInfoOutput{
		UserId:       userInfo.UserId,
		Account:      userInfo.Account,
		NameZh:       userInfo.NameZh,
		NameEn:       userInfo.NameEn,
		NameDisplay:  userInfo.NameDisplay,
		DepartmentId: userInfo.DepartmentId,
		EmployeeId:   userInfo.EmployeeId,
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func StaffUserInfoForTest(gctx *gin.Context) {
	input := &UserInput{}
	input.UserId = 103163

	ctx := GetHCtx(gctx)

	staffOperator := staffLogic.NewStaffOperator(ctx)
	userInfo, err := staffOperator.GetUserInfoByUserId(input.UserId)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_STAFF_USER_INFO, err))
		return
	} else if userInfo.Type == staffLogic.USER_TYPE_LEAVED {
		SetRet(gctx, NewError(ERR_STAFF_USER_LEAVED, err))
		return
	}
	output := &UserInfoOutput{
		UserId:       userInfo.UserId,
		Account:      userInfo.Account,
		NameZh:       userInfo.NameZh,
		NameEn:       userInfo.NameEn,
		NameDisplay:  userInfo.NameDisplay,
		DepartmentId: userInfo.DepartmentId,
		EmployeeId:   userInfo.EmployeeId,
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

// 综合查询
func StaffIntegratedInfo(gctx *gin.Context) {
	input := &StaffIntegrateInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	userInfos, err := staffOperator.GetStaffUserInfos(input.Staff)
	if err != nil && len(userInfos) == 0 {
		SetRet(gctx, NewError(ERR_QUERY_STAFF_USER_INFO, err))
		return
	}
	outputs := make([]*UserInfoOutput, 0)
	for _, userInfo := range userInfos {
		if userInfo.Type == staffLogic.USER_TYPE_LEAVED {
			continue
		}
		outputs = append(outputs, &UserInfoOutput{
			UserId:       userInfo.UserId,
			Account:      userInfo.Account,
			NameZh:       userInfo.NameZh,
			NameEn:       userInfo.NameEn,
			NameDisplay:  userInfo.NameDisplay,
			DepartmentId: userInfo.DepartmentId,
			EmployeeId:   userInfo.EmployeeId,
		})
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(outputs))
	return
}

// 根据用户名查询用户
func StaffUserSearch(gctx *gin.Context) {
	input := &StaffUserSearchInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	userInfos, err := staffOperator.GetUserByUserNameZhVague(input.Name)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_STAFF_USER_INFO, err))
		return
	}
	outputs := make([]*UserInfoOutput, 0)
	for _, userInfo := range userInfos {
		if userInfo.Type == staffLogic.USER_TYPE_LEAVED {
			continue
		}
		outputs = append(outputs, &UserInfoOutput{
			UserId:       userInfo.UserId,
			Account:      userInfo.Account,
			NameZh:       userInfo.NameZh,
			NameEn:       userInfo.NameEn,
			NameDisplay:  userInfo.NameDisplay,
			DepartmentId: userInfo.DepartmentId,
			EmployeeId:   userInfo.EmployeeId,
		})
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(outputs))
	return
}

// 根据用户id查询该用户可使用的APP列表
func StaffAppList(gctx *gin.Context) {
	ctx := GetHCtx(gctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	availableApps, err := staffOperator.GetUserAvailableAppList()
	if err != nil {
		SetRet(gctx, NewError(ERR_STAFF_USER_APPLIST, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(availableApps))
	return
}
