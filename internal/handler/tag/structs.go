package workflow_tag

import (
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type ListWorkflowTagOutPut struct {
	Count int64                  `json:"count"`
	Items []*ListWorkflowTagInfo `json:"items"`
}

type ListWorkflowTagInfo struct {
	TagId      int64  `json:"tagId"`
	TagName    string `json:"tagName"`
	CreateUser string `json:"createUser"`
}

type CreateTagInput struct {
	TagName string `json:"tagName" validate:"required"`
}

func (input *CreateTagInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	if utf8.RuneCountInString(input.TagName) > common.MaxTagLength {
		return common.ErrInvalidFieldf("tagName")
	}
	return common.Validator.Struct(input)
}

type CreateTagOutput struct {
	TagId int64 `json:"tagId"`
}

type DeleteTagInput struct {
	TagId int64 `json:"tagId" validate:"required,gt=0"`
}

func (input *DeleteTagInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type UpdateWorkflowTagInput struct {
	TagId   int64   `json:"tagId" validate:"required,gt=0"`
	TagName *string `json:"tagName" validate:"required"`
}

func (input *UpdateWorkflowTagInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)

	if err != nil {
		return err
	}

	if input.TagName != nil && utf8.RuneCountInString(*input.TagName) > common.MaxTagLength {
		return common.ErrInvalidFieldf("tagName")
	}

	return common.Validator.Struct(input)
}
