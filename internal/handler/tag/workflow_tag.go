package workflow_tag

import (
	"github.com/gin-gonic/gin"
	tagLogic "gitlab.docsl.com/security/bpm/internal/logic/tag"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func ListWorkflowTag(gctx *gin.Context) {
	output := &ListWorkflowTagOutPut{}
	ctx := GetHCtx(gctx)

	tagLogic.NewWorkflowTagOperator(ctx)

	workflowTagOperator := tagLogic.NewWorkflowTagOperator(ctx)
	workflowTagList, totalCnt, err := workflowTagOperator.QueryList()
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW_TAG, err))
		return
	}

	items := make([]*ListWorkflowTagInfo, totalCnt)

	for idx, workflowTag := range workflowTagList {
		items[idx] = &ListWorkflowTagInfo{
			TagId:      workflowTag.ID,
			TagName:    workflowTag.TagName,
			CreateUser: workflowTag.CreateUser,
		}
	}

	output.Items = items
	output.Count = totalCnt

	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func CreateWorkflowTag(gctx *gin.Context) {
	input := &CreateTagInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	output := &CreateTagOutput{}
	ctx := GetHCtx(gctx)

	workflowTagOperator := tagLogic.NewWorkflowTagOperator(ctx)
	tagId, err := workflowTagOperator.InsertTag(input.TagName)
	if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_WORKFLOW_TAG, err))
		return
	}

	output.TagId = tagId
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func DeleteWorkflowTag(gctx *gin.Context) {
	input := &DeleteTagInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	ctx := GetHCtx(gctx)
	workflowTagOperator := tagLogic.NewWorkflowTagOperator(ctx)
	if err := workflowTagOperator.DeleteTag(input.TagId); err != nil {
		SetRet(gctx, NewError(ERR_DELETE_WORKFLOW_TAG, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
	return
}

func UpdateWorkflowTag(gctx *gin.Context) {
	input := &UpdateWorkflowTagInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	ctx := GetHCtx(gctx)

	workflowTagOperator := tagLogic.NewWorkflowTagOperator(ctx)

	if _, err := workflowTagOperator.UpdateTag(input.TagId, input.TagName); err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_WORKFLOW_TAG, err))
		return
	}

	SetRet(gctx, NewError(ERR_OK))
}
