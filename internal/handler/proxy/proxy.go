/**
 * @note
 * proxy
 *
 * <AUTHOR>
 * @date 	2019-12-24
 */
package proxy

import (
	"github.com/gin-gonic/gin"
	apiLogic "gitlab.docsl.com/security/bpm/internal/logic/api"
	. "gitlab.docsl.com/security/bpm/pkg/common"
	"net/http"
	"time"
)

func Proxy(gctx *gin.Context) {
	input := &ProxyInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	ctx := GetHCtx(gctx)
	apiOperator := apiLogic.NewApiOperator(ctx)
	resp, code, err := apiOperator.SignAndDo(input.Url, input.Method, input.AppName, input.Data, nil, 10*time.Second, 0)
	if err != nil {
		SetRet(gctx, NewError(ERR_PROXY, err))
	} else if code != http.StatusOK {
		SetRet(gctx, NewError(ERR_PROXY))
	} else {
		SetRet(gctx, resp)
	}
	return
}
