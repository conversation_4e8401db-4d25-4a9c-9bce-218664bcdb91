/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2019-12-24
 */
package proxy

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type ProxyInput struct {
	Method  string      `json:"method" validate:"required"`
	Url     string      `json:"url" validate:"required"`
	AppName string      `json:"appName"`
	Data    interface{} `json:"data"`
}

func (input *ProxyInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}
