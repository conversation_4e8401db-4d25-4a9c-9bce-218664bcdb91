/**
 * @note
 * 用户组相关接口
 *
 * <AUTHOR>
 * @date 	2019-11-25
 */
package user_group

import (
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	ugLogic "gitlab.docsl.com/security/bpm/internal/logic/user_group"
	"strconv"

	"github.com/gin-gonic/gin"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func ListUserGroup(gctx *gin.Context) {
	input := &ListUserGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	output := &ListUserGroupOutput{}
	ctx := GetHCtx(gctx)

	userGroupOperator := ugLogic.NewUserGroupOperator(ctx)
	userGroupInfos, totalCnt, err := userGroupOperator.QueryUserGroupInfoByNameOrUserId(input.Condition.UserGroupName, input.Condition.UserId, input.Offset, input.Limit)

	if err != nil {
		SetRet(gctx, NewError(ERR_USER_GROUP_NOT_EXIST, err))
		return
	}
	// workaround for nil slice
	// https://github.com/golang/go/issues/27589
	items := make([]ListUserGroupInfo, len(userGroupInfos))

	for idx, userGroupInfo := range userGroupInfos {
		listUserGroupInfo := &ListUserGroupInfo{
			UserId:      userGroupInfo.UserId,
			Name:        userGroupInfo.Name,
			UserGroupId: userGroupInfo.ID,
			Description: userGroupInfo.Description,
			CreateTime:  userGroupInfo.CreateTime,
			UpdateTime:  userGroupInfo.UpdateTime,
			AllowEdit:   true,
			AllowDelete: true,
			AllowDetail: true,
		}
		items[idx] = *listUserGroupInfo
	}

	output.Items = items
	output.Count = totalCnt
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func CreateUserGroup(gctx *gin.Context) {
	input := &CreateUserGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	output := &CreateUserGroupOutput{}
	ctx := GetHCtx(gctx)

	//判断用户id是否存在
	staffOperator := staffLogic.NewStaffOperator(ctx)
	for _, userId := range input.UserIds {
		userInfo, err := staffOperator.GetUserInfoByUserId(userId)
		if userInfo == nil && err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_STAFF_USER_NOT_EXIST, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_QUERY_STAFF_USER_INFO, err))
			return
		}
	}

	userGroupOperator := ugLogic.NewUserGroupOperator(ctx)
	userGroupId, err := userGroupOperator.InsertUserGroupInfo(input.Name, input.Description, input.Users)
	if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_USER_GROUP, err))
		return
	}
	output.UserGroupId = userGroupId
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func DeleteUserGroup(gctx *gin.Context) {
	input := &DeleteUserGroupInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	output := &DeleteUserGroupOutput{}
	ctx := GetHCtx(gctx)
	userGroupOperator := ugLogic.NewUserGroupOperator(ctx)
	for _, userGroupId := range input.UserGroupIds {
		if _, err := userGroupOperator.QueryUserGroupInfoById(userGroupId); err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_USER_GROUP_NOT_EXIST, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_DELETE_USER_GROUP, err))
			return
		}
	}
	userGroupIds, err := userGroupOperator.DeleteUserGroupInfo(input.UserGroupIds)
	if err != nil {
		SetRet(gctx, NewError(ERR_DELETE_USER_GROUP, err))
		return
	}
	output.UserGroupIds = userGroupIds
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func GetUserGroupDetail(gctx *gin.Context) {
	userGroupId, errInput := strconv.ParseInt(gctx.Query("userGroupId"), 10, 64)
	if errInput != nil || userGroupId == 0 {
		SetRet(gctx, NewError(ERR_PARAM, errInput))
		return
	}

	ctx := GetHCtx(gctx)

	output := &GetUserGroupDetailOutput{}

	userGroupOperator := ugLogic.NewUserGroupOperator(ctx)
	userGroupInfo, err := userGroupOperator.QueryUserGroupInfoById(userGroupId)
	if err != nil {
		if err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_USER_GROUP_NOT_EXIST, err))
		} else {
			SetRet(gctx, NewError(ERR_QUERY_USER_GROUP, err))
		}
		return
	}
	userRelationInfos, err := userGroupOperator.QueryUserGroupRelationsByGroupId(userGroupId)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_USER_GROUP, err))
		return
	}

	staffOperator := staffLogic.NewStaffOperator(ctx)
	for _, info := range userRelationInfos {
		var u *UserInfo
		userInfo, err := staffOperator.GetUserInfoByUserId(info.UserId)
		if err != nil {
			ctx.Log().Errorln("query user info by user id error:", err)
			u = &UserInfo{
				UserId: info.UserId,
			}
		} else {
			u = &UserInfo{
				UserId:      userInfo.UserId,
				EmployeeId:  userInfo.EmployeeId,
				NameZh:      userInfo.NameZh,
				NameEn:      userInfo.NameEn,
				NameDisplay: userInfo.NameDisplay,
			}
		}
		if len(info.Detail) > 0 {
			err = JsonStringDecode(info.Detail, &u.Detail)
			if err != nil {
				ctx.Log().Errorln("user group relation info detail error:", err)
			}
		}
		output.Users = append(output.Users, u)
	}
	output.UserId = userGroupInfo.UserId
	output.UserGroupId = userGroupInfo.ID
	output.Name = userGroupInfo.Name
	output.Description = userGroupInfo.Description
	output.CreateTime = userGroupInfo.CreateTime
	output.UpdateTime = userGroupInfo.UpdateTime
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func AddUser(gctx *gin.Context) {
	input := &AddUserInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &AddUserOutput{}
	ctx := GetHCtx(gctx)
	//判断用户组是否存在
	userGroupOperator := ugLogic.NewUserGroupOperator(ctx)
	if t, err := userGroupOperator.QueryUserGroupInfoById(input.UserGroupId); err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_USER_GROUP_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_ADD_USER_TO_GROUP, err))
		return
	} else if t.UserId != ctx.User().UserId {
		SetRet(gctx, NewError(ERR_NOT_USER_GROUP_OWNER))
		return
	}
	//判断用户id是否存在
	staffOperator := staffLogic.NewStaffOperator(ctx)
	for _, userId := range input.UserIds {
		userInfo, err := staffOperator.GetUserInfoByUserId(userId)
		if userInfo == nil && err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_STAFF_USER_NOT_EXIST, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_QUERY_STAFF_USER_INFO, err))
			return
		}
	}

	err := userGroupOperator.AddUserToGroup(input.UserGroupId, input.Users)
	if err != nil {
		SetRet(gctx, NewError(ERR_ADD_USER_TO_GROUP, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func DeleteUser(gctx *gin.Context) {
	input := &DeleteUserInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &DeleteUserOutput{}
	ctx := GetHCtx(gctx)
	userGroupOperator := ugLogic.NewUserGroupOperator(ctx)
	if _, err := userGroupOperator.QueryUserGroupInfoById(input.UserGroupId); err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_USER_GROUP_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_DELETE_USER_FROM_GROUP, err))
		return
	}

	err := userGroupOperator.SoftDeleteUserFromGroup(input.UserGroupId, input.UserIds)
	if err != nil {
		SetRet(gctx, NewError(ERR_DELETE_USER_FROM_GROUP, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func UpdateUserGroupInfo(gctx *gin.Context) {
	input := &UpdateUserGroupInfoInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	output := &UpdateUserGroupOutput{}
	ctx := GetHCtx(gctx)

	//判断用户id是否存在
	staffOperator := staffLogic.NewStaffOperator(ctx)
	for _, userId := range input.UserIds {
		userInfo, err := staffOperator.GetUserInfoByUserId(userId)
		if userInfo == nil && err == ErrRecordNotFound {
			SetRet(gctx, NewError(ERR_STAFF_USER_NOT_EXIST, err))
			return
		} else if err != nil {
			SetRet(gctx, NewError(ERR_QUERY_STAFF_USER_INFO, err))
			return
		}
	}

	userGroupOperator := ugLogic.NewUserGroupOperator(ctx)
	if _, err := userGroupOperator.QueryUserGroupInfoById(input.UserGroupId); err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_USER_GROUP_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_USER_GROUP_INFO, err))
		return
	}
	_, err := userGroupOperator.UpdateUserGroupInfo(input.UserGroupId, input.Name, input.Description, input.Users)
	if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_USER_GROUP_INFO, err))
	} else {
		SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	}
}
