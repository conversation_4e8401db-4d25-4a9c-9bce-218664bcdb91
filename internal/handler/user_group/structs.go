/**
 * @note
 * 用户组接口的结构体
 *
 * <AUTHOR>
 * @date 	2019-11-25
 */
package user_group

import (
	ugLogic "gitlab.docsl.com/security/bpm/internal/logic/user_group"
	"strings"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type ListUserGroupInput struct {
	Offset    int                    `json:"offset" validate:"gte=0"`
	Limit     int                    `json:"limit" validate:"gte=0"`
	Condition ListUserGroupCondition `json:"condition"`
}

func (input *ListUserGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	input.Condition.UserGroupName = strings.Replace(input.Condition.UserGroupName, "%", "\\%", -1)
	input.Condition.UserGroupName = strings.Replace(input.Condition.UserGroupName, "_", "\\_", -1)
	return common.Validator.Struct(input)
}

type ListUserGroupCondition struct {
	UserGroupName string `json:"userGroupName"`
	UserId        int64  `json:"userId" validate:"gte=0"`
}

type ListUserGroupOutput struct {
	Count int64               `json:"count"`
	Items []ListUserGroupInfo `json:"items"`
}

type ListUserGroupInfo struct {
	UserId      int64  `json:"userId,omitempty"`
	Name        string `json:"name"`
	UserGroupId int64  `json:"userGroupId"`
	Description string `json:"description"`
	CreateTime  int64  `json:"createTime,omitempty"`
	UpdateTime  int64  `json:"updateTime,omitempty"`

	AllowEdit   bool `json:"allowEdit"`
	AllowDelete bool `json:"allowDelete"`
	AllowDetail bool `json:"allowDetail"`
}

type CreateUserGroupInput struct {
	Name        string                          `json:"name" validate:"required"`
	Description string                          `json:"description"`
	UserIds     []int64                         `json:"userIds"`
	Users       []ugLogic.UserGroupRelationInfo `json:"users"`
}

func (input *CreateUserGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	if utf8.RuneCountInString(input.Name) > common.MaxNameLength {
		return common.ErrInvalidFieldf("name")
	} else if utf8.RuneCountInString(input.Description) > common.MaxDescLength {
		return common.ErrInvalidFieldf("description")
	}
	if input.UserIds != nil {
		input.Users = make([]ugLogic.UserGroupRelationInfo, 0)
		for _, userId := range input.UserIds {
			input.Users = append(input.Users, ugLogic.UserGroupRelationInfo{UserId: userId})
		}
	}
	return common.Validator.Struct(input)
}

type CreateUserGroupOutput struct {
	UserGroupId int64 `json:"userGroupId"`
}

type DeleteUserGroupInput struct {
	UserGroupIds []int64 `json:"userGroupIds" validate:"required,gt=0"`
}

func (input *DeleteUserGroupInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type DeleteUserGroupOutput struct {
	UserGroupIds []int64 `json:"userGroupIds"`
}

type GetUserGroupDetailOutput struct {
	UserId      int64       `json:"userId,omitempty"`
	UserGroupId int64       `json:"userGroupId"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Users       []*UserInfo `json:"users,omitempty"`
	CreateTime  int64       `json:"createTime,omitempty"`
	UpdateTime  int64       `json:"updateTime,omitempty"`
}

type UserInfo struct {
	UserId      int64                           `json:"userId"`
	EmployeeId  string                          `json:"employeeId,omitempty"`
	NameZh      string                          `json:"nameZh,omitempty"`
	NameEn      string                          `json:"nameEn,omitempty"`
	NameDisplay string                          `json:"nameDisplay,omitempty"`
	Detail      ugLogic.UserGroupRelationDetail `json:"detail,omitempty"`
}

type AddUserInput struct {
	UserGroupId int64                           `json:"userGroupId" validate:"required,min=0"`
	UserIds     []int64                         `json:"userIds"`
	Users       []ugLogic.UserGroupRelationInfo `json:"users"`
}

func (input *AddUserInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	if input.UserIds != nil {
		input.Users = make([]ugLogic.UserGroupRelationInfo, 0)
		for _, userId := range input.UserIds {
			input.Users = append(input.Users, ugLogic.UserGroupRelationInfo{UserId: userId})
		}
	}
	return common.Validator.Struct(input)
}

type AddUserOutput struct {
}

type DeleteUserInput struct {
	UserGroupId int64   `json:"userGroupId" validate:"required,min=0"`
	UserIds     []int64 `json:"userIds" validate:"required,gt=0,dive,gt=0"`
}

func (input *DeleteUserInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type DeleteUserOutput struct {
}

type UpdateUserGroupInfoInput struct {
	UserGroupId int64                           `json:"userGroupId" validate:"required,gt=0"`
	Name        *string                         `json:"name"`
	Description *string                         `json:"description"`
	UserIds     []int64                         `json:"userIds"`
	Users       []ugLogic.UserGroupRelationInfo `json:"users"`
}

func (input *UpdateUserGroupInfoInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	if input.Name != nil && utf8.RuneCountInString(*input.Name) > common.MaxNameLength {
		return common.ErrInvalidFieldf("name")
	} else if input.Name != nil && len(*input.Name) == 0 {
		return common.ErrMissingAMandatoryParameterf("name")
	} else if input.Description != nil && utf8.RuneCountInString(*input.Description) > common.MaxDescLength {
		return common.ErrInvalidFieldf("description")
	}
	if input.UserIds != nil {
		input.Users = make([]ugLogic.UserGroupRelationInfo, 0)
		for _, userId := range input.UserIds {
			input.Users = append(input.Users, ugLogic.UserGroupRelationInfo{UserId: userId})
		}
	}
	return common.Validator.Struct(input)
}

type UpdateUserGroupOutput struct {
}
