/**
 * @note
 * lark
 *
 * <AUTHOR>
 * @date 	2025-08-05
 */
package lark

import (
	"context"
	"fmt"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher/callback"
	interCommon "gitlab.docsl.com/security/bpm/internal/common"
	processHandler "gitlab.docsl.com/security/bpm/internal/handler/process"
	bpmCommon "gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	uModel "gitlab.docsl.com/security/bpm/pkg/model/staff"
	"gitlab.docsl.com/security/common"
)

func GetLarkEventDispatcher() *dispatcher.EventDispatcher {
	return lark.WrapLarkEventDispatcher().OnP2CardActionTrigger(LarkCardTriggerHandler)
}

func LarkCardTriggerHandler(ctx context.Context, event *callback.CardActionTriggerEvent) (*callback.CardActionTriggerResponse, error) {
	// 目前只有process的操作用到callback
	processAction := &ProcessAction{}
	err := common.MapToStruct(event.Event.Action.Value, &processAction)
	if err != nil {
		return newCardActionTriggerResponse(toastTypeError, err.Error(), nil), err
	}
	// 填充User
	if event.Event.Operator.UserID == nil || *event.Event.Operator.UserID == common.StringEmpty {
		return newCardActionTriggerResponse(toastTypeError, "用户信息异常", nil), nil
	}
	userTable, err := uModel.GetUserByLarkUserID(ctx, *event.Event.Operator.UserID)
	if err != nil {
		return newCardActionTriggerResponse(toastTypeError, fmt.Sprintf("鉴权失败: %v", err), nil), nil
	}
	hctx := bpmCommon.GetHCtx(ctx)
	hctx.User().AccountName = userTable.UserName
	hctx.User().UserId = userTable.ID
	hctx.User().DisplayName = userTable.LarkName
	hctx.User().Email = userTable.Email
	hctx.User().EmployeeId = userTable.EmployeeNo
	larkMsg, err := processHandler.DealWithProcess(hctx, &processHandler.UpdateProcessInput{
		ProcessUuid: processAction.ProcessUuid,
		Action:      processAction.Action,
		Comment:     "Quick action via Lark card",
		Data:        nil,
	})
	switch err {
	case nil:
		return newCardActionTriggerResponse(toastTypeInfo, "成功", &callback.Card{
			Type: "template",
			Data: larkMsg.Interactive,
		}), nil
	case bpmCommon.ErrRecordNotFound:
		return newCardActionTriggerResponse(toastTypeError, bpmCommon.ErrnoDesc[bpmCommon.ERR_PROCESS_NOT_EXIST], nil), nil
	case interCommon.ErrProcessStateIncorrect:
		return newCardActionTriggerResponse(toastTypeError, bpmCommon.ErrnoDesc[bpmCommon.ERR_UPDATE_PROCESS_STATE_NOT_CORRECT], nil), nil
	case interCommon.ErrInvalidNodeType:
		return newCardActionTriggerResponse(toastTypeError, bpmCommon.ErrnoDesc[bpmCommon.ERR_UPDATE_PROCESS_INVALID_NODE_TYPE], nil), nil
	case interCommon.ErrInvalidNodeState:
		return newCardActionTriggerResponse(toastTypeError, bpmCommon.ErrnoDesc[bpmCommon.ERR_UPDATE_PROCESS_INVALID_NODE_STATE], nil), nil
	case interCommon.ErrProcessNeedComment:
		return newCardActionTriggerResponse(toastTypeError, bpmCommon.ErrnoDesc[bpmCommon.ERR_UPDATE_PROCESS_NEED_COMMENT], nil), nil
	case interCommon.ErrNoPermissionToModifyDataField:
		return newCardActionTriggerResponse(toastTypeError, bpmCommon.ErrnoDesc[bpmCommon.ERR_UPDATE_PROCESS_NO_PERMISSION_OF_DATA_FIELD], nil), nil
	default:
		return newCardActionTriggerResponse(toastTypeError, bpmCommon.ErrnoDesc[bpmCommon.ERR_UPDATE_PROCESS], nil), nil
	}
}

func newCardActionTriggerResponse(toastType, content string, card *callback.Card) *callback.CardActionTriggerResponse {
	return &callback.CardActionTriggerResponse{
		Card: card,
		Toast: &callback.Toast{
			Type:    toastType,
			Content: content,
		},
	}
}
