/**
 * @note
 *
 * @author: libi
 * @date: 2019/12/23
 */

package app_auth

import (
	"github.com/gin-gonic/gin"
	appAuthLogic "gitlab.docsl.com/security/bpm/internal/logic/app_auth"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func CreateAppAuth(gctx *gin.Context) {
	input := &CreateAppAuthInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &CreateAppAuthOutput{}
	ctx := GetHCtx(gctx)
	authAppOperator := appAuthLogic.NewAuthAppOperator(ctx)

	appId, appKey, err := authAppOperator.InsertAuthAppInfo(input.AppName, input.Desc, input.SsoAppId)
	if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_APP_AUTH_KEY, err))
		return
	}
	output.AppId = appId
	output.AppKey = appKey
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func DeleteAppAuth(gctx *gin.Context) {
	input := &DeleteAppAuthInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &DeleteAppAuthOutput{}
	ctx := GetHCtx(gctx)
	authAppOperator := appAuthLogic.NewAuthAppOperator(ctx)

	_, err := authAppOperator.DeleteAuthAppInfo(input.AppIds)
	if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_APP_AUTH_KEY, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func UpdateAppAuth(gctx *gin.Context) {
	input := &UpdateAppAuthInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &UpdateAppAuthOutput{}
	ctx := GetHCtx(gctx)
	authAppOperator := appAuthLogic.NewAuthAppOperator(ctx)

	_, err := authAppOperator.UpdateAuthApp(input.AppId, input.AppName, input.Desc)
	if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_APP_AUTH_KEY, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func GetAuthAppKey(gctx *gin.Context) {
	input := &GetAuthAppKeyInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &AppAuthOutput{}
	ctx := GetHCtx(gctx)
	authAppOperator := appAuthLogic.NewAuthAppOperator(ctx)

	authItem, err := authAppOperator.QueryAppKeyByAppId(input.AppId)
	if err != nil {
		SetRet(gctx, NewError(ERR_CREATE_APP_AUTH_KEY, err))
		return
	}
	output = &AppAuthOutput{
		AppName:     authItem.AppName,
		Description: authItem.Description,
		AppId:       authItem.AppId,
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func ListAppAuth(gctx *gin.Context) {
	output := []*AppAuthOutput{}
	ctx := GetHCtx(gctx)
	authAppOperator := appAuthLogic.NewAuthAppOperator(ctx)
	items, err := authAppOperator.ListAuthAppInfo(appAuthLogic.ListAppAuthCondition{})
	if err != nil {
		SetRet(gctx, NewError(ERR_LIST_APP_AUTH_INFO, err))
		return
	}
	for _, item := range items {
		output = append(output, &AppAuthOutput{
			AppName:     item.AppName,
			Description: item.Description,
			AppId:       item.AppId,
		})
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}
