/**
 * @note
 * structs.go
 *
 * <AUTHOR>
 * @date 	2019-12-23
 */
package app_auth

import (
	"github.com/gin-gonic/gin"
	appAuthLogic "gitlab.docsl.com/security/bpm/internal/logic/app_auth"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type CreateAppAuthInput struct {
	SsoAppId int64  `json:"ssoAppId"`
	AppName  string `json:"appName" validate:"required,lte=50"`
	Desc     string `json:"description" validate:"lte=200"`
}

func (input *CreateAppAuthInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type CreateAppAuthOutput struct {
	AppId  string `json:"appId"`
	AppKey string `json:"appKey"`
}

type DeleteAppAuthInput struct {
	AppIds []string `json:"appIds" validate:"required,gt=0"`
}

func (input *DeleteAppAuthInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type DeleteAppAuthOutput struct {
}

type UpdateAppAuthInput struct {
	AppId   string `json:"appId" validate:"required,lte=30"`
	AppName string `json:"appName" validate:"lte=50"`
	Desc    string `json:"desc" validate:"lte=200"`
}

func (input *UpdateAppAuthInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type UpdateAppAuthOutput struct {
}

type GetAuthAppKeyInput struct {
	AppId string `json:"appId" validate:"required,lte=30"`
}

func (input *GetAuthAppKeyInput) ParseInput(gctx *gin.Context) error {
	input.AppId = gctx.Query("appId")
	return common.Validator.Struct(input)
}

type AppAuthOutput struct {
	AppName     string `json:"appName"`
	Description string `json:"description"`
	AppId       string `json:"appId"`
}

type ListAppAuthInput struct {
	Condition appAuthLogic.ListAppAuthCondition `json:"condition"`
}

func (input *ListAppAuthInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return common.Validator.Struct(input)
}

type ListAppAuthOutput struct {
	Items []*appAuthLogic.AppAuthInfo `json:"items"`
}
