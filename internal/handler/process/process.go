package process

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/internal/common"
	pLogic "gitlab.docsl.com/security/bpm/internal/logic/process"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	"gitlab.docsl.com/security/bpm/internal/logic/workflow/engine"
	wrLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/runtime"
	"gitlab.docsl.com/security/bpm/internal/model/process"
	"gitlab.docsl.com/security/bpm/internal/model/workflow"
	wmService "gitlab.docsl.com/security/bpm/internal/srvs/workflow_message"
	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func ListProcess(gctx *gin.Context) {
	input := &ListProcessInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &ListProcessOutput{}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	var workflowUuids []string
	if len(input.Condition.UserName) > 0 ||
		len(input.Condition.WorkflowName) > 0 ||
		len(input.Condition.WorkflowTemplateUuids) > 0 ||
		len(input.Condition.WorkflowStates) > 0 ||
		len(input.Condition.WorkflowTemplateGroupIds) > 0 ||
		len(input.Condition.WorkflowUuids) > 0 ||
		len(input.Condition.WorkflowSerialIds) > 0 {
		var userIds []int64
		var err error
		if len(input.Condition.UserName) > 0 {
			userIds, err = staffOperator.GetUserIdsByUserNameZhVague(input.Condition.UserName)
			if err != nil {
				SetRet(gctx, NewError(ERR_QUERY_PROCESS, err))
				return
			} else if len(userIds) == 0 && !input.Condition.BothWorkflowAndUser { //短路
				SetRet(gctx, NewError(ERR_OK).SetDesc(output))
				return
			}
		}
		workflowUuids, err = workflowOperator.QueryWorkflowUuidsByNameVagueAndTmplUuidsAndUserIds(
			input.Condition.WorkflowName, input.Condition.WorkflowTemplateUuids,
			input.Condition.WorkflowStates, userIds, input.Condition.WorkflowTemplateGroupIds,
			input.Condition.BothWorkflowAndUser, input.Condition.WorkflowUuids,
			input.Condition.WorkflowSerialIds)
		if err != nil {
			SetRet(gctx, NewError(ERR_QUERY_PROCESS, err))
			return
		} else if len(workflowUuids) == 0 {
			SetRet(gctx, NewError(ERR_OK).SetDesc(output))
			return
		}
	}

	//先查任务表
	processes, err := processOperator.QueryUserProcessRuntimeExcludeSomeStatesBySeveralConditions(input.Condition,
		workflowUuids, input.Offset, input.Limit)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_PROCESS, err))
		return
	}

	//查任务count
	count, err := processOperator.QueryUserProcessRuntimeCntBySeveralConditions(input.Condition, workflowUuids)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_PROCESS, err))
		return
	}

	//查workflow信息
	var retWorkflowUuids []string
	var nodeUuids []string
	for _, proc := range processes {
		retWorkflowUuids = append(retWorkflowUuids, proc.WorkflowUuid)
		if proc.State == common.PROCESS_STATE_APPROVED_BY_OTHERS ||
			proc.State == common.PROCESS_STATE_REJECTED_BY_OTHERS ||
			proc.State == common.PROCESS_STATE_DONE_BY_OTHERS {
			nodeUuids = append(nodeUuids, proc.NodeUuid)
		}
	}

	retWorkflowInfos, err := workflowOperator.QueryWorkflowInfosByUuids(retWorkflowUuids)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}

	nodeRuntimeInfosCollection, err := workflowOperator.QueryNodeRuntimeByUuidsAndAssembleToCollection(nodeUuids)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}

	//拼装workflow信息
	workflowInfoMap := make(map[string]*wrLogic.WorkflowInfo)
	for _, info := range retWorkflowInfos {
		workflowInfoMap[info.WorkflowUuid] = info
	}

	for _, proc := range processes {
		processInfo := &ProcessInfo{
			ProcessUuid: proc.Uuid,
			NodeUuid:    proc.NodeUuid,
			Workflow:    workflowInfoMap[proc.WorkflowUuid],
			Type:        proc.Type,
			State:       proc.State,
			CreateTime:  proc.CreateTime,
			UpdateTime:  proc.UpdateTime,
		}
		if nodeRuntimeInfosCollection == nil {
		} else if nodeRuntimeObj := nodeRuntimeInfosCollection.First(func(item interface{}, key int) bool {
			return item.(*workflow.NodeTable).Uuid == proc.NodeUuid
		}); nodeRuntimeObj != nil {
			if nodeRuntime, err := nodeRuntimeObj.ToInterface(); err == nil {
				if v, ok := nodeRuntime.(*workflow.NodeTable); ok {
					if v.Type == common.NODE_TYPE_APPROVAL {
						approvalNodeDetail := &engine.ApprovalTaskDetail{}
						if err := JsonDecode([]byte(v.Detail), &approvalNodeDetail); err == nil {
							processInfo.OtherApproverUser = approvalNodeDetail.OtherApproverUser
						}
					} else if v.Type == common.NODE_TYPE_TRANSACTOR {
						transactorTaskDetail := &engine.TransactorTaskDetail{}
						if err := JsonDecode([]byte(v.Detail), &transactorTaskDetail); err == nil {
							processInfo.OtherApproverUser = transactorTaskDetail.OtherTransactorUser
						}
					}
				}
			}
		}

		output.Items = append(output.Items, processInfo)
	}
	output.Count = count
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func GroupQueryProcess(gctx *gin.Context) {

	input := &ListProcessInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &ListProcessOutput{}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	staffOperator := staffLogic.NewStaffOperator(ctx)
	var workflowUuids []string
	if len(input.Condition.UserName) > 0 ||
		len(input.Condition.WorkflowName) > 0 ||
		len(input.Condition.WorkflowTemplateUuids) > 0 ||
		len(input.Condition.WorkflowStates) > 0 ||
		len(input.Condition.WorkflowTemplateGroupIds) > 0 ||
		len(input.Condition.WorkflowUuids) > 0 ||
		len(input.Condition.WorkflowSerialIds) > 0 {
		var userIds []int64
		var err error
		if len(input.Condition.UserName) > 0 {
			userIds, err = staffOperator.GetUserIdsByUserNameZhVague(input.Condition.UserName)
			if err != nil {
				SetRet(gctx, NewError(ERR_QUERY_PROCESS, err))
				return
			} else if len(userIds) == 0 && !input.Condition.BothWorkflowAndUser { //短路
				SetRet(gctx, NewError(ERR_OK).SetDesc(output))
				return
			}
		}
		workflowUuids, err = workflowOperator.QueryWorkflowUuidsByNameVagueAndTmplUuidsAndUserIds(
			input.Condition.WorkflowName, input.Condition.WorkflowTemplateUuids,
			input.Condition.WorkflowStates, userIds, input.Condition.WorkflowTemplateGroupIds,
			input.Condition.BothWorkflowAndUser, input.Condition.WorkflowUuids,
			input.Condition.WorkflowSerialIds)
		if err != nil {
			SetRet(gctx, NewError(ERR_QUERY_PROCESS, err))
			return
		} else if len(workflowUuids) == 0 {
			SetRet(gctx, NewError(ERR_OK).SetDesc(output))
			return
		}
	}

	//先查任务表
	processes, count, err := processOperator.QueryUserDoneProcess(input.Condition,
		workflowUuids, input.Offset, input.Limit)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_PROCESS, err))
		return
	}

	//查workflow信息
	var retWorkflowUuids []string
	var nodeUuids []string
	for _, proc := range processes {
		retWorkflowUuids = append(retWorkflowUuids, proc.WorkflowUuid)
		if proc.State == common.PROCESS_STATE_APPROVED_BY_OTHERS ||
			proc.State == common.PROCESS_STATE_REJECTED_BY_OTHERS ||
			proc.State == common.PROCESS_STATE_DONE_BY_OTHERS {
			nodeUuids = append(nodeUuids, proc.NodeUuid)
		}
	}

	retWorkflowInfos, err := workflowOperator.QueryWorkflowInfosByUuids(retWorkflowUuids)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}

	nodeRuntimeInfosCollection, err := workflowOperator.QueryNodeRuntimeByUuidsAndAssembleToCollection(nodeUuids)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}

	//拼装workflow信息
	workflowInfoMap := make(map[string]*wrLogic.WorkflowInfo)
	for _, info := range retWorkflowInfos {
		workflowInfoMap[info.WorkflowUuid] = info
	}

	for _, proc := range processes {
		processInfo := &ProcessInfo{
			ProcessUuid: proc.Uuid,
			NodeUuid:    proc.NodeUuid,
			Workflow:    workflowInfoMap[proc.WorkflowUuid],
			Type:        proc.Type,
			State:       proc.State,
			CreateTime:  proc.CreateTime,
			UpdateTime:  proc.UpdateTime,
		}
		if nodeRuntimeInfosCollection == nil {
		} else if nodeRuntimeObj := nodeRuntimeInfosCollection.First(func(item interface{}, key int) bool {
			return item.(*workflow.NodeTable).Uuid == proc.NodeUuid
		}); nodeRuntimeObj != nil {
			if nodeRuntime, err := nodeRuntimeObj.ToInterface(); err == nil {
				if v, ok := nodeRuntime.(*workflow.NodeTable); ok {
					if v.Type == common.NODE_TYPE_APPROVAL {
						approvalNodeDetail := &engine.ApprovalTaskDetail{}
						if err := JsonDecode([]byte(v.Detail), &approvalNodeDetail); err == nil {
							processInfo.OtherApproverUser = approvalNodeDetail.OtherApproverUser
						}
					} else if v.Type == common.NODE_TYPE_TRANSACTOR {
						transactorTaskDetail := &engine.TransactorTaskDetail{}
						if err := JsonDecode([]byte(v.Detail), &transactorTaskDetail); err == nil {
							processInfo.OtherApproverUser = transactorTaskDetail.OtherTransactorUser
						}
					}
				}
			}
		}

		output.Items = append(output.Items, processInfo)
	}
	output.Count = count
	SetRet(gctx, NewError(ERR_OK).SetDesc(output))
	return
}

func UpdateProcess(gctx *gin.Context) {
	input := &UpdateProcessInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}

	output := &UpdateProcessOutput{}
	ctx := GetHCtx(gctx)

	processOperator := pLogic.NewProcessOperator(ctx)
	processTable, err := processOperator.QueryUserProcessRuntimeByUuid(input.ProcessUuid)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	} else if len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_NO_PERMISSION_OF_PORTAL_PROCESS, err))
		return
	}

	if processTable.Type == common.PROCESS_TYPE_APPROVAL {
		err = processOperator.UpdateApprovalProcess(processTable, input.Comment, input.Data, input.Action) //默认现在只有审批动作
	} else if processTable.Type == common.PROCESS_TYPE_TRANSACTOR {
		err = processOperator.UpdateTransactorProcess(processTable, input.Comment, input.Data, input.Action) //默认现在只有审批动作
	}

	switch err {
	case nil:
		SetRet(gctx, NewError(ERR_OK).SetDesc(output))
		messenger := wmService.NewWorkflowMessenger(ctx) //懒触发处理机制
		messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{
			WorkflowUuid: processTable.WorkflowUuid,
			NodeUuid:     processTable.NodeUuid,
			TraceId:      ctx.Log().GetTraceId(),
		})
	case common.ErrProcessStateIncorrect:
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS_STATE_NOT_CORRECT, err))
	case common.ErrInvalidNodeType:
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS_INVALID_NODE_TYPE, err))
	case common.ErrInvalidNodeState:
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS_INVALID_NODE_STATE, err))
	case common.ErrProcessNeedComment:
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS_NEED_COMMENT, err))
	case common.ErrNoPermissionToModifyDataField:
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS_NO_PERMISSION_OF_DATA_FIELD, err))
	default:
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
	}
	return
}

func ProcessDetail(gctx *gin.Context) {
	input := &ProcessDetailInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	processTable, err := processOperator.QueryUserProcessRuntimeByUuid(input.ProcessUuid)
	if err != nil || len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	}
	if processTable.Type == common.PROCESS_TYPE_NOTIFY && //如果是一个抄送任务，那么查看详情就代表已经看过了
		processTable.State == common.PROCESS_STATE_NOT_NOTIFIED {
		_, err := processOperator.UpdateUserProcessRuntimeStateByProcessUuid(processTable.Uuid, common.PROCESS_STATE_NOTIFIED)
		if err != nil {
			ctx.Log().Errorln(err)
		}
	}
	workflowOperator := wrLogic.NewWorkflowOperator(ctx)
	ret := &ProcessDetailInfo{
		ProcessUuid: processTable.Uuid,
		NodeUuid:    processTable.NodeUuid,
		Type:        processTable.Type,
		State:       processTable.State,
		CreateTime:  processTable.CreateTime,
		UpdateTime:  processTable.UpdateTime,
	}
	ret.Workflow, err = workflowOperator.QueryWorkflowDetail(processTable.WorkflowUuid, true)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_WORKFLOW, err))
		return
	}
	//拼一个data给前端
	for _, n := range ret.Workflow.Nodes {
		if n.Uuid == ret.NodeUuid {
			ret.Data = n.Data
		}
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(ret))
	return
}

func GetTodoCount(gctx *gin.Context) {
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	unApprovedCnt, err := processOperator.QueryUserProcessRuntimeCntBySeveralConditions(pLogic.ListProcessCondition{
		States: []string{string(common.PROCESS_STATE_UNAPPROVED), string(common.PROCESS_STATE_UNDONE)},
	}, nil)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_TODO_PROCESS_COUNT, err))
		return
	}
	unNotifiedCnt, err := processOperator.QueryUserProcessRuntimeCntBySeveralConditions(pLogic.ListProcessCondition{
		Type:   common.PROCESS_TYPE_NOTIFY,
		States: []string{string(common.PROCESS_STATE_NOT_NOTIFIED)},
	}, nil)
	if err != nil {
		SetRet(gctx, NewError(ERR_QUERY_TODO_PROCESS_COUNT, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(TodoProcessCountInfo{
		UnApprovedCount:  unApprovedCnt,
		NotNotifiedCount: unNotifiedCnt,
	}))
	return
}

////////////////////BPM V2////////////////////

func HandoverProcess(gctx *gin.Context) {
	input := &HandoverProcessInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	//前置检查一下
	processTable, err := processOperator.QueryUserProcessRuntimeByUuid(input.ProcessUuid)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	} else if len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_NO_PERMISSION_OF_PORTAL_PROCESS, err))
		return
	} else if input.Staff[0].UserId == ctx.User().UserId {
		SetRet(gctx, NewError(ERR_HANDOVER_PROCESS_TO_SELF))
		return
	} else if processTable.State != common.PROCESS_STATE_UNAPPROVED &&
		processTable.State != common.PROCESS_STATE_UNDONE {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS_STATE_NOT_CORRECT))
		return
	}
	newProcessUuid, err := processOperator.AddSubApprovalProcess(common.PROCESS_ACTION_HANDOVER,
		common.PROCESS_SUBTYPE_HANDOVER, processTable, StringEmpty, input.Explain, input.ExplainVisible, input.Staff)
	if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(HandoverProcessOutput{ProcessUuid: newProcessUuid}))
	messenger := wmService.NewWorkflowMessenger(ctx) //懒触发处理机制
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{
		WorkflowUuid: processTable.WorkflowUuid,
		NodeUuid:     processTable.NodeUuid,
		TraceId:      ctx.Log().GetTraceId(),
	})
	return
}

func CountersignProcess(gctx *gin.Context) {
	input := &CountersignProcessInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	//前置检查一下
	processTable, err := processOperator.QueryUserProcessRuntimeByUuid(input.ProcessUuid)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	} else if len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_NO_PERMISSION_OF_PORTAL_PROCESS, err))
		return
	} else if input.Staff[0].UserId == ctx.User().UserId {
		SetRet(gctx, NewError(ERR_COUNTERSIGN_PROCESS_TO_SELF))
		return
	} else if processTable.State != common.PROCESS_STATE_UNAPPROVED &&
		processTable.State != common.PROCESS_STATE_UNDONE {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS_STATE_NOT_CORRECT))
		return
	}
	var subType common.ProcessSubTypeEnum
	if input.CountersignMode == COUNTERSIGN_BEFORE {
		subType = common.PROCESS_SUBTYPE_COUNTERSIGN_BEFORE
	} else {
		subType = common.PROCESS_SUBTYPE_COUNTERSIGN_AFTER
	}
	//检查一遍是否已经有相同的加签任务了
	subProcesses, err := processOperator.QuerySubProcessRuntimeByParentUuid(processTable.NodeUuid, processTable.Uuid)
	if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	}
	if len(subProcesses) > 0 {
		for _, subProcess := range subProcesses {
			var d process.ApprovalProcessDetail
			err = JsonStringDecode(subProcess.Detail, &d)
			if err != nil {
				SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
				return
			} else if d.SubType == subType {
				SetRet(gctx, NewError(ERR_SAME_COUNTERSIGN_NOT_ALLOW, err))
				return
			}
		}
	}
	//执行
	newProcessUuid, err := processOperator.AddSubApprovalProcess(common.PROCESS_ACTION_COUNTERSIGN,
		subType, processTable, input.Comment, input.Explain, input.ExplainVisible, input.Staff)
	if err != nil {
		SetRet(gctx, NewError(ERR_UPDATE_PROCESS, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK).SetDesc(CountersignProcessOutput{ProcessUuid: newProcessUuid}))
	messenger := wmService.NewWorkflowMessenger(ctx) //懒触发处理机制
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{
		WorkflowUuid: processTable.WorkflowUuid,
		NodeUuid:     processTable.NodeUuid,
		TraceId:      ctx.Log().GetTraceId(),
	})
	return
}

func RemarkProcess(gctx *gin.Context) {
	input := &RemarkProcessInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	//前置检查一下
	processTable, err := processOperator.QueryUserProcessRuntimeByUuid(input.ProcessUuid)
	if err == ErrRecordNotFound || processTable == nil ||
		(processTable.Type != common.PROCESS_TYPE_APPROVAL && processTable.Type != common.PROCESS_TYPE_TRANSACTOR) {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_REMARK_PROCESS, err))
		return
	} else if len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_NO_PERMISSION_OF_PORTAL_PROCESS, err))
		return
	}
	err = processOperator.RemarkProcess(processTable, input.Remark, input.File, input.AtStaff, input.VisibleStaff, input.Notify)
	if err == ErrNoPermission {
		SetRet(gctx, NewError(ERR_NO_PERMISSION, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_REMARK_PROCESS, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
	return
}

func RollbackProcess(gctx *gin.Context) {
	input := &RollbackProcessInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	//前置检查一下
	processTable, err := processOperator.QueryUserProcessRuntimeByUuid(input.ProcessUuid)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_ROLLBACK_PROCESS, err))
		return
	} else if len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_NO_PERMISSION_OF_PORTAL_PROCESS, err))
		return
	} else if processTable.State != common.PROCESS_STATE_UNAPPROVED &&
		processTable.State != common.PROCESS_STATE_UNDONE {
		SetRet(gctx, NewError(ERR_CANT_DO_APPROVED_PROCESS))
		return
	}
	err = processOperator.RollbackProcess(processTable, input.Comment, input.NodeUuid, input.NodeName)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err == ErrNoPermission {
		SetRet(gctx, NewError(ERR_NO_PERMISSION, err))
		return
	} else if err == common.ErrRollbackToSelf {
		SetRet(gctx, NewError(ERR_CANNOT_ROLLBACK_TO_SELF_NODE, err))
		return
	} else if err == common.ErrTransferFailed || err == common.ErrWorkflowState || err == common.ErrInvalidNodeState {
		SetRet(gctx, NewError(ERR_WORKFLOW_OCCUPIED, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_ROLLBACK_PROCESS, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
	messenger := wmService.NewWorkflowMessenger(ctx) //懒触发处理机制
	messenger.NonBlockingSendWithRetry(&common.WorkflowProcessMsg{
		WorkflowUuid: processTable.WorkflowUuid,
		NodeUuid:     input.NodeUuid,
		TraceId:      ctx.Log().GetTraceId(),
	})
	return
}

func NeedCompleteProcess(gctx *gin.Context) {
	input := &NeedCompleteProcessInput{}
	if err := input.ParseInput(gctx); err != nil {
		SetRet(gctx, NewError(ERR_PARAM, err))
		return
	}
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	//前置检查一下
	processTable, err := processOperator.QueryUserProcessRuntimeByUuid(input.ProcessUuid)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_NEED_COMPLETE_PROCESS, err))
		return
	} else if len(processTable.WorkflowUuid) == 0 || len(processTable.NodeUuid) == 0 {
		SetRet(gctx, NewError(ERR_NO_PERMISSION_OF_PORTAL_PROCESS, err))
		return
	} else if processTable.State != common.PROCESS_STATE_UNAPPROVED &&
		processTable.State != common.PROCESS_STATE_UNDONE {
		SetRet(gctx, NewError(ERR_CANT_DO_APPROVED_PROCESS))
		return
	}
	err = processOperator.NeedCompleteProcess(processTable, input.Comment)
	if err == ErrRecordNotFound {
		SetRet(gctx, NewError(ERR_PROCESS_NOT_EXIST, err))
		return
	} else if err == ErrNoPermission {
		SetRet(gctx, NewError(ERR_NO_PERMISSION, err))
		return
	} else if err == common.ErrTransferFailed || err == common.ErrWorkflowState {
		SetRet(gctx, NewError(ERR_WORKFLOW_OCCUPIED, err))
		return
	} else if err != nil {
		SetRet(gctx, NewError(ERR_NEED_COMPLETE_PROCESS, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
	return
}

func MarkAllNotified(gctx *gin.Context) {
	ctx := GetHCtx(gctx)
	processOperator := pLogic.NewProcessOperator(ctx)
	err := processOperator.MarkAllNotifyAsRead()
	if err != nil {
		SetRet(gctx, NewError(ERR_MARK_ALL_NOTIFY_PROCESS, err))
		return
	}
	SetRet(gctx, NewError(ERR_OK))
	return
}
