/**
 * @note
 * structs.go
 *
 * <AUTHOR>
 * @date 	2019-11-21
 */
package process

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/internal/logic/notify"
	pLogic "gitlab.docsl.com/security/bpm/internal/logic/process"
	staffLogic "gitlab.docsl.com/security/bpm/internal/logic/staff"
	wrLogic "gitlab.docsl.com/security/bpm/internal/logic/workflow/runtime"
	processModel "gitlab.docsl.com/security/bpm/internal/model/process"
	"strings"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type ListProcessInput struct {
	Offset    int64                       `json:"offset" validate:"gte=0"`
	Limit     int64                       `json:"limit" validate:"gte=0"`
	Condition pLogic.ListProcessCondition `json:"condition"`
}

func (input *ListProcessInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	if len(input.Condition.State) > 0 {
		input.Condition.States = append(input.Condition.States, input.Condition.State)
	}
	if len(input.Condition.WorkflowState) > 0 {
		input.Condition.WorkflowStates = append(input.Condition.WorkflowStates, input.Condition.WorkflowState)
	}
	if input.Condition.WorkflowTemplateGroupId > 0 {
		input.Condition.WorkflowTemplateGroupIds = append(input.Condition.WorkflowTemplateGroupIds, input.Condition.WorkflowTemplateGroupId)
	}
	if len(input.Condition.WorkflowTemplateUuid) > 0 {
		input.Condition.WorkflowTemplateUuids = append(input.Condition.WorkflowTemplateUuids, input.Condition.WorkflowTemplateUuid)
	}
	if len(input.Condition.WorkflowOrUserName) > 0 {
		input.Condition.WorkflowName = input.Condition.WorkflowOrUserName
		input.Condition.UserName = input.Condition.WorkflowOrUserName
		input.Condition.BothWorkflowAndUser = true
	}
	input.Condition.WorkflowName = strings.Replace(input.Condition.WorkflowName, "%", "\\%", -1)
	input.Condition.WorkflowName = strings.Replace(input.Condition.WorkflowName, "_", "\\_", -1)
	return common.Validator.Struct(input)
}

type ListProcessOutput struct {
	Count int64          `json:"count"`
	Items []*ProcessInfo `json:"items"`
}

type ProcessInfo struct {
	ProcessUuid       string                  `json:"processUuid,omitempty"`
	NodeUuid          string                  `json:"nodeUuid,omitempty"`
	Workflow          *wrLogic.WorkflowInfo   `json:"workflow,omitempty"`
	OtherApproverUser *staffLogic.UserInfo    `json:"otherApproverUser,omitempty"`
	Type              bpmCom.ProcessTypeEnum  `json:"type,omitempty"`
	State             bpmCom.ProcessStateEnum `json:"state,omitempty"`
	CreateTime        int64                   `json:"createTime,omitempty"`
	UpdateTime        int64                   `json:"updateTime,omitempty"`
}

type UpdateProcessInput struct {
	ProcessUuid string                   `json:"processUuid" validate:"len=32"`
	Comment     string                   `json:"comment"`
	Data        interface{}              `json:"data"`
	Action      bpmCom.ProcessActionEnum `json:"action" validate:"required"`
}

func (input *UpdateProcessInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *UpdateProcessInput) Validate() error {
	if len(input.ProcessUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	} else if input.Action != bpmCom.PROCESS_ACTION_APPROVE &&
		input.Action != bpmCom.PROCESS_ACTION_REJECT &&
		input.Action != bpmCom.PROCESS_ACTION_DONE {
		return common.ErrInvalidParameterOptionf("action")
	}
	return nil
}

type UpdateProcessOutput struct{}

type ProcessDetailInput struct {
	ProcessUuid string `json:"processUuid"`
}

func (input *ProcessDetailInput) ParseInput(gctx *gin.Context) error {
	input.ProcessUuid = gctx.Query("processUuid")
	return input.Validate()
}

func (input *ProcessDetailInput) Validate() error {
	if len(input.ProcessUuid) == 0 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	}
	return nil
}

type ProcessDetailInfo struct {
	ProcessUuid string                      `json:"processUuid,omitempty"`
	NodeUuid    string                      `json:"nodeUuid,omitempty"`
	Data        interface{}                 `json:"data,omitempty"`
	Type        bpmCom.ProcessTypeEnum      `json:"type,omitempty"`
	State       bpmCom.ProcessStateEnum     `json:"state,omitempty"`
	Workflow    *wrLogic.WorkflowDetailInfo `json:"workflow,omitempty"`
	CreateTime  int64                       `json:"createTime,omitempty"`
	UpdateTime  int64                       `json:"updateTime,omitempty"`
}

type TodoProcessCountInfo struct {
	UnApprovedCount  int64 `json:"unApprovedCount"`
	NotNotifiedCount int64 `json:"notNotifiedCount"`
}

type HandoverProcessInput struct {
	ProcessUuid    string                  `json:"processUuid" validate:"len=32"`
	Explain        string                  `json:"explain"`        //转交备注
	ExplainVisible bool                    `json:"explainVisible"` //转交是否对其他审批人可见
	Staff          []*staffLogic.StaffInfo `json:"staff"`          //要转交的用户信息
}

func (input *HandoverProcessInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *HandoverProcessInput) Validate() error {
	if len(input.ProcessUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	} else if len(input.Staff) == 0 {
		return common.ErrMissingAMandatoryParameterf("staff")
	} else if utf8.RuneCountInString(input.Explain) > common.MaxCommentLength {
		return common.ErrInvalidFieldf("explain")
	}
	if input.Staff[0].Type != bpmCom.STAFF_TYPE_PERSON {
		return common.ErrInvalidParameterOptionf("staff.type")
	}
	return nil
}

type HandoverProcessOutput struct {
	ProcessUuid string `json:"processUuid,omitempty"`
}

const (
	COUNTERSIGN_BEFORE = "Before"
	COUNTERSIGN_AFTER  = "After"
)

type CountersignProcessInput struct {
	ProcessUuid     string                  `json:"processUuid" validate:"len=32"`
	Comment         string                  `json:"comment"`         //审批意见
	Explain         string                  `json:"explain"`         //转交备注
	ExplainVisible  bool                    `json:"explainVisible"`  //转交是否对其他审批人可见
	CountersignMode string                  `json:"countersignMode"` //加签人在我之前:"Before"，在我之后："After"
	Staff           []*staffLogic.StaffInfo `json:"staff"`           //要转交的用户信息
}

func (input *CountersignProcessInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *CountersignProcessInput) Validate() error {
	if len(input.ProcessUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	} else if len(input.Staff) == 0 {
		return common.ErrMissingAMandatoryParameterf("staff")
	} else if input.CountersignMode != COUNTERSIGN_BEFORE && input.CountersignMode != COUNTERSIGN_AFTER {
		return common.ErrInvalidParameterOptionf("countersignMode")
	} else if utf8.RuneCountInString(input.Explain) > common.MaxCommentLength {
		return common.ErrInvalidFieldf("explain")
	}
	if input.Staff[0].Type != bpmCom.STAFF_TYPE_PERSON {
		return common.ErrInvalidParameterOptionf("staff.type")
	}
	return nil
}

type CountersignProcessOutput struct {
	ProcessUuid string `json:"processUuid,omitempty"`
}

type RemarkProcessInput struct {
	ProcessUuid  string                         `json:"processUuid" validate:"len=32"`
	Remark       string                         `json:"remark"` //评论，最长一千字
	Notify       notify.NotifyConfig            `json:"notify"`
	File         []*processModel.RemarkFileInfo `json:"file"`
	AtStaff      []*staffLogic.StaffInfo        `json:"atStaff"`
	VisibleStaff []*staffLogic.StaffInfo        `json:"visibleStaff"`
}

func (input *RemarkProcessInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *RemarkProcessInput) Validate() error {
	if len(input.ProcessUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	} else if utf8.RuneCountInString(input.Remark) > common.MaxCommentLength {
		return common.ErrInvalidFieldf("remark")
	}
	return nil
}

type RollbackProcessInput struct {
	ProcessUuid string `json:"processUuid" validate:"len=32"`
	Comment     string `json:"comment"` //评论，最长一千字
	NodeUuid    string `json:"nodeUuid"`
	NodeName    string `json:"nodeName"`
}

func (input *RollbackProcessInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *RollbackProcessInput) Validate() error {
	if len(input.ProcessUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	} else if len(input.NodeUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("nodeUuid")
	} else if utf8.RuneCountInString(input.Comment) > common.MaxCommentLength {
		return common.ErrInvalidFieldf("comment")
	}
	return nil
}

type NeedCompleteProcessInput struct {
	ProcessUuid string `json:"processUuid" validate:"len=32"`
	Comment     string `json:"comment"` //评论，最长一千字
}

func (input *NeedCompleteProcessInput) ParseInput(gctx *gin.Context) error {
	err := gctx.ShouldBindJSON(input)
	if err != nil {
		return err
	}
	return input.Validate()
}

func (input *NeedCompleteProcessInput) Validate() error {
	if len(input.ProcessUuid) != 32 {
		return common.ErrMissingAMandatoryParameterf("processUuid")
	} else if utf8.RuneCountInString(input.Comment) > common.MaxCommentLength {
		return common.ErrInvalidFieldf("comment")
	}
	return nil
}
