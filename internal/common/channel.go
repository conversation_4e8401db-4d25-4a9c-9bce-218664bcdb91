/**
 * @note
 * 全局channel，用于通信
 *
 * <AUTHOR>
 * @date 	2019-10-31
 */
package common

var WorkflowProcessChan chan *WorkflowProcessMsg

type WorkflowProcessMsg struct {
	WorkflowUuid string `json:"workflowUuid"`
	NodeUuid     string `json:"nodeUuid"`
	TraceId      string `json:"traceId"`
	ErrChan      chan error
}

//init chan
func (wm *WorkflowProcessMsg) InitErrChanIfNilOrClosed() {
	if wm.ErrChan == nil {
		wm.ErrChan = make(chan error, 1)
		return
	}
	select {
	case _, ok := <-wm.ErrChan:
		if !ok {
			wm.ErrChan = make(chan error, 1)
		}
	default:
	}
}

func (wm *WorkflowProcessMsg) CloseChanIgnorePanic() {
	defer func() {
		if recover() != nil {
		}
	}()
	close(wm.<PERSON>rr<PERSON>han)
}
