/**
 * @note
 * hbpm公用的枚举常量
 *
 * <AUTHOR>
 * @date 	2019-10-30
 */
package common

// workflow, node的状态
type WorkflowStateEnum string

const (
	//workflow&node
	STATE_RESET         WorkflowStateEnum = "Reset"        //任务已被重置（驳回），等待完善后重新提交
	STATE_NEED_COMPLETE WorkflowStateEnum = "NeedComplete" //任务正等待发起人补充资料
	STATE_READY         WorkflowStateEnum = "Ready"        //任务信息已完善，等待执行
	STATE_EXECUTING     WorkflowStateEnum = "Executing"    //任务正在执行中
	STATE_PENDING       WorkflowStateEnum = "Pending"      //任务被挂起，不能自动继续的状态（审批中）
	STATE_FINISHED      WorkflowStateEnum = "Finished"     //任务已被完成
	STATE_FAILED        WorkflowStateEnum = "Failed"       //任务已失败
	STATE_DISCARD       WorkflowStateEnum = "Discard"      //任务已失效（撤回）
)

type WorkflowTemplateStateEnum string

const (
	//workflow_template
	STATE_ENABLED  WorkflowTemplateStateEnum = "Enabled"  //模板已启用
	STATE_STAGING  WorkflowTemplateStateEnum = "Staging"  //模板编辑中
	STATE_DISABLED WorkflowTemplateStateEnum = "Disabled" //模板已下线
	STATE_HISTORY  WorkflowTemplateStateEnum = "History"  //模板已成为历史
)

type WorkflowTmplGroupStateEnum string

const (
	//workflow_template_group
	GROUP_STATE_ENABLED  WorkflowTmplGroupStateEnum = "Enabled"  //模板组启用
	GROUP_STATE_DISABLED WorkflowTmplGroupStateEnum = "Disabled" //模板组禁用
)

type UserOriginEnum = string

const (
	OriginBpm            UserOriginEnum = "Hbpm"           //来源是Hbpm
	OriginInternalSystem UserOriginEnum = "InternalSystem" //来源是内部系统
	OriginAll            UserOriginEnum = "All"            //来源可以是所有系统
)

// 通用状态
type StatusEnum string

const (
	STATUS_NORMAL  StatusEnum = "Normal"  //正常状态
	STATUS_DELETED StatusEnum = "Deleted" //删除状态
)

type TargetUserEnum string

const (
	TARGET_PUBLIC   TargetUserEnum = "Public"   //公开的，对所有用户可见
	TARGET_PERSONAL TargetUserEnum = "Personal" //仅对个人可见
)

type TaskTagEnum string

const (
	PORTAL_BPM_TASK             TaskTagEnum = "HbpmTask"           //hbpm的代办任务，tag统一为"业务类"
	PORTAL_ADMIN_TASK           TaskTagEnum = "AdministrationTask" //三方app推送的任务,"行政类型"
	PORTAL_HUMAN_RESOURCE_TASK  TaskTagEnum = "HumanResourceTask"  //三方app推送的任务,"人事类型"
	PORTAL_STAMP_TASK           TaskTagEnum = "StampTask"          //三方app推送的任务,"用印类"
	PORTAL_User_Experience_TASK TaskTagEnum = "UserExperienceTask" //三方app推送的任务,"用户体验类型"
)

type TimeOrderType string

const (
	TIME_ORDER_ASC  TimeOrderType = "TimeAsc"
	TIME_ORDER_DESC TimeOrderType = "TimeDesc"
)

// 节点类型枚举
type NodeTypeEnum string

const (
	NODE_TYPE_EXAMPLE_TASK NodeTypeEnum = "ExampleTask"
	NODE_TYPE_START        NodeTypeEnum = "Start"
	NODE_TYPE_APPROVAL     NodeTypeEnum = "Approval"
	NODE_TYPE_TRANSACTOR   NodeTypeEnum = "Transactor"
	NODE_TYPE_INTERACT     NodeTypeEnum = "Interact"
	NODE_TYPE_NOTIFY       NodeTypeEnum = "Notify"
	NODE_TYPE_GATEWAY      NodeTypeEnum = "Gateway"
	NODE_TYPE_API          NodeTypeEnum = "Api" //api调用其他系统
	NODE_TYPE_END          NodeTypeEnum = "End"
)

type WorkflowTypeEnum string

const (
	WORKFLOW_TYPE_NORMAL WorkflowTypeEnum = "Normal"
)

type MergeModeEnum string

const (
	MERGE_MODE_OVERRIDE MergeModeEnum = "Override" //相同key情况下，后面的输入会覆盖前面的输入
	MERGE_MODE_COMBINE  MergeModeEnum = "Combine"  //相同key且是map的情况下，后面的输入与前面的输入相并
)

type GatewayTypeEnum string

const (
	GATEWAY_TYPE_OR       GatewayTypeEnum = "Or"
	GATEWAY_TYPE_AND      GatewayTypeEnum = "And"
	GATEWAY_TYPE_AT_LEAST GatewayTypeEnum = "AtLeast"
)

type ApprovalModeEnum string

const (
	APPROVAL_MODE_AND_SIGN    ApprovalModeEnum = "AndSign"
	APPROVAL_MODE_OR_SIGN     ApprovalModeEnum = "OrSign"
	APPROVAL_MODE_INTURN_SIGN ApprovalModeEnum = "InTurn"
)

type ProcessSubTypeEnum string

const (
	PROCESS_SUBTYPE_HANDOVER           ProcessSubTypeEnum = "Handover"
	PROCESS_SUBTYPE_COUNTERSIGN_BEFORE ProcessSubTypeEnum = "CountersignBefore"
	PROCESS_SUBTYPE_COUNTERSIGN_AFTER  ProcessSubTypeEnum = "CountersignAfter"
)

// 人员类型（可以是审批者，抄送者，发起者）
type StaffTypeEnum string

const (
	STAFF_TYPE_ALL        StaffTypeEnum = "All"        //所有人
	STAFF_TYPE_PERSON     StaffTypeEnum = "User"       //指定个人
	STAFF_TYPE_RELATION   StaffTypeEnum = "Relation"   //一种关系
	STAFF_TYPE_DEPARTMENT StaffTypeEnum = "Department" //部门
	STAFF_TYPE_GROUP      StaffTypeEnum = "UserGroup"  //自定义的组
)

type ProcessStateEnum string

const (
	//Approval Process的状态："Initialized", "UnApproved", "Approved", "ApprovedByOthers", "Rejected", "RejectedByOthers", "Handovered", "Rollbacked", "NeedComplete", "Expired"
	//Notify Process的状态："Initialized", "NotNotified", "Notified"
	//Interact Process的状态："Initialized", "NeedCallback", "Finished", "Failed"
	//Transactor Process的状态："Initialized", "UnDone", "Done", "DoneByOthers", "Handovered", "Rollbacked", "NeedComplete"
	PROCESS_STATE_INITIALIZED        ProcessStateEnum = "Initialized"      //任务仅仅初始化
	PROCESS_STATE_UNAPPROVED         ProcessStateEnum = "UnApproved"       //待审批状态
	PROCESS_STATE_APPROVED           ProcessStateEnum = "Approved"         //已审批状态
	PROCESS_STATE_APPROVED_BY_OTHERS ProcessStateEnum = "ApprovedByOthers" //已被他人审批状态
	PROCESS_STATE_REJECTED           ProcessStateEnum = "Rejected"         //已拒绝状态
	PROCESS_STATE_REJECTED_BY_OTHERS ProcessStateEnum = "RejectedByOthers" //已被他人拒绝状态
	PROCESS_STATE_HANDOVERED         ProcessStateEnum = "Handovered"       //已转交给他人
	PROCESS_STATE_ROLLBACKED         ProcessStateEnum = "Rollbacked"       //已退回
	PROCESS_STATE_NEED_COMPLETE      ProcessStateEnum = "NeedComplete"     //已让发起人补充资料
	PROCESS_STATE_EXPIRED            ProcessStateEnum = "Expired"          //过期了

	PROCESS_STATE_NOT_NOTIFIED ProcessStateEnum = "NotNotified" //抄送人未阅的状态
	PROCESS_STATE_NOTIFIED     ProcessStateEnum = "Notified"    //抄送人已阅的状态

	PROCESS_STATE_POSTED   ProcessStateEnum = "NeedCallback" //Interact节点，已经将数据推送给三方系统，并需要返回的情况
	PROCESS_STATE_FINISHED ProcessStateEnum = "Finished"     //Interact节点完成状态
	PROCESS_STATE_FAILED   ProcessStateEnum = "Failed"       //Interact节点失败状态

	PROCESS_STATE_UNDONE         ProcessStateEnum = "UnDone"       //未办理
	PROCESS_STATE_DONE           ProcessStateEnum = "Done"         //已办理
	PROCESS_STATE_DONE_BY_OTHERS ProcessStateEnum = "DoneByOthers" //已被他人办理
)

type ProcessTypeEnum string

const (
	PROCESS_TYPE_NOTIFY     ProcessTypeEnum = "Notify"     //抄送
	PROCESS_TYPE_APPROVAL   ProcessTypeEnum = "Approval"   //审批
	PROCESS_TYPE_INTERACT   ProcessTypeEnum = "Interact"   //系统交互节点
	PROCESS_TYPE_TRANSACTOR ProcessTypeEnum = "Transactor" //办理人
)

type ProcessActionEnum string

const (
	PROCESS_ACTION_APPROVE      ProcessActionEnum = "Approve" //审批通过
	PROCESS_ACTION_REJECT       ProcessActionEnum = "Reject"  //审批拒绝
	PROCESS_ACTION_HANDOVER     ProcessActionEnum = "Handover"
	PROCESS_ACTION_COUNTERSIGN  ProcessActionEnum = "Countersign"
	PROCESS_ACTION_REMARK       ProcessActionEnum = "Remark"
	PROCESS_ACTION_ROLLBACK     ProcessActionEnum = "Rollback"
	PROCESS_ACTION_NEEDCOMPLETE ProcessActionEnum = "NeedComplete" //让发起人补充资料
	PROCESS_ACTION_DONE         ProcessActionEnum = "Done"         //办理人办理了流程
)

type NotifyTypeEnum string

const (
	NotifyTypeLark  NotifyTypeEnum = "Lark"
	NotifyTypeEmail NotifyTypeEnum = "Email"
)

const (
	OPEN_TYPE_ON_PC_BROWSER = iota + 1
	OPEN_TYPE_ON_PHONE_BROWSER
	OPEN_TYPE_ON_DINGDING
)

// 用户状态
type UserStatusEnum string

const (
	StatusActivated UserStatusEnum = "Activated" //激活状态
	StatusFrozen    UserStatusEnum = "Frozen"    //冻结状态
	StatusResigned  UserStatusEnum = "Resigned"  //离职状态
	StatusExited    UserStatusEnum = "Exited"    //主动退出状态
	StatusUnJoin    UserStatusEnum = "UnJoin"    //还未加入状态
)
