/**
 * @note
 * bpm模块的配置项
 *
 * <AUTHOR>
 * @date 	2019-11-06
 */
package common

type Config struct {
	ToBAddr             string
	ToCAddr             string
	PulseInterval       int64
	WaitInterval        int64
	PulseTimeout        int64
	WaitTimeout         int64
	WaitRetryTimes      int
	ProcessorChanLen    int
	ProcessorNum        int
	ProcessorRetryTimes int
}

var (
	config Config
)

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}
