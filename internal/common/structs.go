package common

import "encoding/json"

// fix cycle import
type StaffInfo struct {
	Type         StaffTypeEnum   `json:"type,omitempty"`
	UserId       int64           `json:"userId,omitempty"`
	DepartmentId int64           `json:"departmentId,omitempty"`
	Relation     string          `json:"relation,omitempty"` //如果传了UserId或者Department则以此为relation，如果只传了Relation，以ctx中的User为Relation
	UserGroupId  int64           `json:"userGroupId,omitempty"`
	All          bool            `json:"all,omitempty"`   //当为Relation类型时，如三级领导，如果传all，代表选择一级二级三级领导共三个人
	Level        int             `json:"level,omitempty"` //当为Relation类型时，表示当前关系的级数，如三级领导/四级领导
	Name         json.RawMessage `json:"name,omitempty"`
}

type UserInfo struct {
	UserId         int64           `json:"userId,omitempty"`
	Account        string          `json:"account,omitempty"`
	NameZh         string          `json:"nameZh,omitempty"`
	NameEn         string          `json:"nameEn,omitempty"`
	NameDisplay    string          `json:"nameDisplay,omitempty"`
	Email          string          `json:"email,omitempty"`
	Phone          string          `json:"phone,omitempty"`
	EmployeeId     string          `json:"employeeId,omitempty"`
	DepartmentName string          `json:"departmentName,omitempty"`
	DepartmentId   int64           `json:"departmentId,omitempty"`
	ParentDept     *ParentDeptInfo `json:"parentDept,omitempty"`
	Type           int             `json:"type,omitempty"`
	Status         int             `json:"status,omitempty"`
}

type ParentDeptInfo struct {
	DepartmentId int64           `json:"departmentId"`
	Name         string          `json:"name"`
	ParentDept   *ParentDeptInfo `json:"parentDept,omitempty"`
}

type DepartmentInfo struct {
	DepartmentId      int64  `json:"departmentId"`
	Name              string `json:"name"`
	SuperId           int64  `json:"superId"`
	SuperName         string `json:"superName"`
	Code              string `json:"code"`
	ManagerId         int64  `json:"managerId"`
	ManagerEmployeeId string `json:"managerEmployeeId"`
	ManagerAccount    string `json:"managerAccount"`
	ManagerEmail      string `json:"managerEmail"`
	ManagerNameZh     string `json:"managerNameZh"`
	ManagerNameEn     string `json:"managerNameEn"`
}

// 在具体流程中提交上来的关联审批单数据
type RelateWorkflowData struct {
	WorkflowTemplateUuid string    `json:"workflowTemplateUuid"`
	WorkflowUuid         string    `json:"workflowUuid"`
	User                 *UserInfo `json:"user"`
	CreateTime           int64     `json:"createTime"`
	UpdateTime           int64     `json:"updateTime"`
}

// 关联审批单取字段控件
type RelateWorkflowFieldData struct {
	RelateWorkflowData
	Field       string      `json:"field"`
	FieldValue  interface{} `json:"fieldValue"`
	FieldConfig interface{} `json:"fieldConfig"`
}
