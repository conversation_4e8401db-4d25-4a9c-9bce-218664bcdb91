/**
 * @note
 * i18n字符串结构体定义
 *
 * <AUTHOR>
 * @date 	2019-12-01
 */
package common

import (
	"gitlab.docsl.com/security/bpm/pkg/common"
	"regexp"
	"unicode/utf8"
)

const (
	I18N_ZH_CN = "zh-cn"
	I18N_EN_US = "en-us"
)

// 按照优先级排个序
var (
	I18nLanSlice = []string{
		I18N_ZH_CN,
		I18N_EN_US,
	}
	I18nKeyRegex = regexp.MustCompile(`^[a-zA-Z]{2}-[a-zA-Z]{2}$`)
)

// 国际化字符串
type I18nString map[string]string

func (s *I18nString) Marshal() string {
	if s == nil {
		return common.StringJsonEmpty
	}
	str, _ := common.JsonStringEncode(s)
	return str
}

func (s *I18nString) MaxLength() int {
	if s == nil {
		return 0
	}
	var maxLen int
	for _, v := range *s {
		if l := utf8.RuneCountInString(v); l > maxLen {
			maxLen = l
		}
	}
	return maxLen
}

// 根据语言优先级，取出最先不为空的一个语言项
func (s *I18nString) GetOneByOrder() string {
	if s == nil || len(*s) == 0 {
		return common.StringEmpty
	}
	for _, lan := range I18nLanSlice {
		if len((*s)[lan]) > 0 {
			return (*s)[lan]
		}
	}
	for _, v := range *s {
		return v
	}
	return common.StringEmpty
}

func (s *I18nString) Validate() error {
	for k := range *s {
		if !I18nKeyRegex.MatchString(k) {
			return common.ErrInvalidFieldf("i18nString")
		}
	}
	return nil
}

func NewI18nString(s string) (I18nString, error) {
	ret := make(map[string]string)
	if err := common.JsonDecode([]byte(s), &ret); err != nil {
		return nil, err
	} else {
		return ret, nil
	}
}
