/**
 * @note
 * error
 *
 * <AUTHOR>
 * @date 	2019-11-07
 */
package common

import (
	"errors"
)

var (
	//node
	ErrLoadNode                = errors.New("load node failed")
	ErrTransferFailed          = errors.New("transfer state failed, maybe occupied by another goroutine")
	ErrPersistFailed           = errors.New("persist failed, maybe occupied by another goroutine")
	ErrWaitTimeout             = errors.New("wait timeout")
	ErrNodeNotExist            = errors.New("node not exist in workflow")
	ErrGatewayTypeInvalid      = errors.New("gateway type invalid")
	ErrGatewayImpossibleToPass = errors.New("gateway impossible to pass")
	ErrGatewayNoInput          = errors.New("gateway no input")
	ErrGatewayNoOutput         = errors.New("gateway no output")
	ErrInvalidGatewayCondition = errors.New("invalid gateway condition")
	ErrInvalidNodeType         = errors.New("invalid node type")
	ErrInvalidNodeState        = errors.New("invalid node state")
	ErrNodeInvalidInput        = errors.New("invalid node input")
	ErrNodeTopology            = errors.New("invalid node topology")
	ErrInvalidNodeDetail       = errors.New("invalid node detail")
	ErrGetVariableNameFromExpr = errors.New("failed to get variable names from expression")

	//workflow
	ErrWorkflowTmplNotStaging     = errors.New("workflow template is not staging")
	ErrWorkflowState              = errors.New("workflow state is err")
	ErrWorkflowTmplOrigin         = errors.New("workflow template origin is not allowed")
	ErrWorkflowImpossibleAfterCut = errors.New("workflow impossible to finish after cut")
	ErrRelateWorkflowInputError   = errors.New("relate workflow input error")
	ErrNotRelateWorkflow          = errors.New("not relate workflow")

	//approval
	ErrInvalidApprovalMode        = errors.New("invalid approval type")
	ErrApproverRejected           = errors.New("rejected by approver(s)")
	ErrCannotFindApprovers        = errors.New("cannot find approvers")
	ErrMissingSelfSelectApprovers = errors.New("missing self select approvers")

	//interact
	ErrInteractRejected = errors.New("rejected by interact system")

	//workflow template group
	ErrDelTmplGroupHasTmpls = errors.New("template group has templates, can't delete")

	//workflow process
	ErrSendWorkflowProcessMsg        = errors.New("send workflow process msg failed")
	ErrProcessNeedComment            = errors.New("process need comment to update")
	ErrNoPermissionToModifyDataField = errors.New("no permission to modify data field")
	ErrRollbackThroughMultiBranch    = errors.New("can't rollback through multi branch")
	ErrRollbackToSelf                = errors.New("can't rollback to self node")
	ErrAlreadyDone                   = errors.New("rowsAffected = 0, maybe done by others")

	//process
	ErrProcessStateIncorrect = errors.New("process state is incorrect")
	ErrProcessNotExist       = errors.New("process not exist")

	//User
	ErrUserStatusNotNormal = errors.New("user status not normal")
	ErrDeptStatusNotNormal = errors.New("department status not normal")
	ErrDeptManagerNotExist = errors.New("department manager not exist")
	ErrDeptNotExist        = errors.New("department not exist")
	ErrUserLeaved          = errors.New("user leaved")
	ErrUserContext         = errors.New("user context is wrong")

	//proxy
	ErrMethodNotSupported = errors.New("http method not supported")

	//other
	ErrInvalidTimeInput     = errors.New("time invalid input")
	ErrUrgeWorkflow         = errors.New("forbidden to urge workflow created by others")
	ErrUrgeWorkflowFinished = errors.New("the process is already finished, should not urge it")
	ErrNotWorkflowProcess   = errors.New("the process is not in the workflow")
	ErrDirtyData            = errors.New("there are some dirty data,processing immediately")
	ErrRecordNotFound       = errors.New("record not found")
)
