/**
 * @note
 * log.go
 *
 * <AUTHOR>
 * @date 	2020-04-01
 */
package proxy

import (
	"bytes"
	"compress/gzip"
	"github.com/sirupsen/logrus"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"io/ioutil"
	"net/http"
)

func logRecordFunc(field logrus.Fields, ctx common.HContextIface, req *http.Request, res *http.Response, err error) {
	var (
		resBodyBytes []byte
		e            error
		code         = http.StatusGatewayTimeout
		header       http.Header
	)
	if req == nil {
		return
	}
	if res != nil {
		header = res.Header
		code = res.StatusCode
	}
	if res != nil && res.Body != nil && res.Header.Get("Content-Type") != "application/proto" {
		if res.Header.Get("Content-Encoding") == "gzip" {
			gzipReader, _ := gzip.NewReader(res.Body)
			defer gzipReader.Close()
			if resBodyBytes, e = ioutil.ReadAll(gzipReader); e == nil {
				res.Header.Del("Content-Encoding")
				res.Body = ioutil.NopCloser(bytes.NewReader(resBodyBytes))
			}
		} else if resBodyBytes, e = ioutil.ReadAll(res.Body); e == nil {
			res.Body = ioutil.NopCloser(bytes.NewReader(resBodyBytes))
		}
	}

	f := logrus.Fields{
		"api":       req.Method + req.URL.Path,
		"raw_api":   req.URL,
		"host":      req.Host,
		"get":       req.URL.RawQuery,
		"out":       string(resBodyBytes),
		"inHeader":  req.Header,
		"outHeader": header,
		"method":    req.Method,
		"code":      code,
	}
	common.PrintLogWithError(err, ctx.Log(), field, f)
}
