/**
 * @note
 * rules.go
 *
 * <AUTHOR>
 * @date 	2020-03-30
 */
package proxy

import (
	"context"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"
)

type RouteMatch int

type Route struct {
	Cond []RouteCond
	Conf *RouteConf
}

type RouteConf struct {
	Server        string
	Timeout       time.Duration
	Retry         int
	RewriteRegexp []string
}

func (r *Route) match(req *http.Request, ctx common.HContextIface) RouteMatch {
	var match RouteMatch
	for _, c := range r.Cond {
		if m := c(req, ctx); m > match {
			match = m
		}
	}
	return match
}

func (r *ReqRule) handle(req *http.Request, ctx common.HContextIface) (*http.Request, *http.Response) {
	for _, c := range r.Cond {
		if !c(req, ctx) {
			return req, nil
		}
	}
	var resp *http.Response
	for _, h := range r.Handler { //short circuit
		req, resp = h(req, ctx)
		if resp != nil {
			return req, resp
		}
	}
	return req, resp
}

func (r *RespRule) handle(resp *http.Response, ctx common.HContextIface) *http.Response {
	for _, c := range r.Cond {
		if !c(resp, ctx) {
			return resp
		}
	}
	for _, h := range r.Handler { //short circuit
		resp = h(resp, ctx)
	}
	return resp
}

type ProxyRouter struct {
	mu        sync.RWMutex
	ReqRules  []ReqRule
	RespRules []RespRule
	Routes    []Route
}

func (pr *ProxyRouter) getReqRules() []ReqRule {
	pr.mu.RLock()
	defer pr.mu.RUnlock()
	return pr.ReqRules
}

func (pr *ProxyRouter) getRespRules() []RespRule {
	pr.mu.RLock()
	defer pr.mu.RUnlock()
	return pr.RespRules
}

func (pr *ProxyRouter) getRoutes() []Route {
	pr.mu.RLock()
	defer pr.mu.RUnlock()
	return pr.Routes
}

func (pr *ProxyRouter) SetReqRules(reqRules []ReqRule) {
	pr.mu.RLock()
	defer pr.mu.RUnlock()
	pr.ReqRules = reqRules
}

func (pr *ProxyRouter) SetRespRules(respRules []RespRule) {
	pr.mu.Lock()
	defer pr.mu.Unlock()
	pr.RespRules = respRules
}

func (pr *ProxyRouter) SetRoutes(routes []Route) {
	pr.mu.Lock()
	defer pr.mu.Unlock()
	pr.Routes = routes
}

// if any reqRule return non-nil http.Response, make it short circuit and return immediately
func (pr *ProxyRouter) handleReq(req *http.Request, ctx common.HContextIface) (*http.Request, *http.Response) {
	reqRules := pr.getReqRules()
	var resp *http.Response
	for _, r := range reqRules {
		req, resp = r.handle(req, ctx)
		if resp != nil {
			return req, resp
		}
	}
	return req, resp
}

func (pr *ProxyRouter) handleResp(resp *http.Response, ctx common.HContextIface) *http.Response {
	respRules := pr.getRespRules()
	for _, r := range respRules {
		resp = r.handle(resp, ctx)
	}
	return resp
}

func (pr *ProxyRouter) matchRoute(req *http.Request, ctx common.HContextIface) *RouteConf {
	routes := pr.getRoutes()
	var match RouteMatch
	var rc *RouteConf
	for _, r := range routes {
		if m := r.match(req, ctx); m > match {
			match = m
			rc = r.Conf
		}
	}
	return rc
}

func (rc *RouteConf) director(req *http.Request, ctx common.HContextIface) *http.Request {
	//step 1、modify host
	target, err := url.Parse(rc.Server)
	if err != nil {
		ctx.Log().Errorln("parse host error: " + rc.Server)
		return req
	}
	if len(rc.RewriteRegexp) > 0 { //replaced path by regexp
		replaced := false
		for _, value := range rc.RewriteRegexp {
			req.URL.Path, replaced = replaceUriByRegexp(req.URL.Path, value)
			if replaced {
				break
			}
		}
	}
	req.Host = target.Host
	req.URL.Host = target.Host
	req.URL.Scheme = target.Scheme
	req.URL.Path = singleJoiningSlash(target.Path, req.URL.Path)
	if _, ok := req.Header["User-Agent"]; !ok {
		req.Header.Set("User-Agent", common.DefaultUserAgent())
	}
	//step 2、add trace
	ctx.Log().AddHttpTrace(req)
	//step 3、add forward addr
	addrs := requestIPHops(req)
	req.Header.Set("X-Forwarded-For", addrs)
	//step 4、add timeout
	if rc.Timeout > 0 {
		c, _ := context.WithTimeout(req.Context(), rc.Timeout)
		req = req.WithContext(c)
	}
	return req
}

func singleJoiningSlash(a, b string) string {
	a = strings.TrimRight(a, "/")
	b = strings.TrimLeft(b, "/")
	if len(b) > 0 {
		return a + "/" + b
	}
	return a
}

func requestIPHops(r *http.Request) string {
	clientIP, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return ""
	}
	if prior, ok := r.Header["X-Forwarded-For"]; ok {
		clientIP = strings.Join(prior, ", ") + ", " + clientIP
	}
	return clientIP
}

func replaceUriByRegexp(uri string, regexpString string) (string, bool) {
	stringArray := strings.Split(regexpString, "@")
	if len(stringArray) != 2 {
		//if the conf is not correct, do nothing
		return uri, false
	}
	pat := stringArray[0]
	replaceString := stringArray[1]
	if re, err := regexp.Compile(pat); err != nil {
		return uri, false
	} else {
		newUri := re.ReplaceAllString(uri, replaceString)
		if newUri == uri { //no replace happened
			return newUri, false
		}
		return newUri, true
	}
}
