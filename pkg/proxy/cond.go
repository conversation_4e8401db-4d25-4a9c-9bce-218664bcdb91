/**
 * @note
 * cond
 *
 * <AUTHOR>
 * @date 	2020-03-30
 */
package proxy

import (
	"gitlab.docsl.com/security/bpm/pkg/common"
	"math"
	"net/http"
	"regexp"
	"strings"
)

type ReqCond func(req *http.Request, ctx common.HContextIface) bool
type RespCond func(resp *http.Response, ctx common.HContextIface) bool
type RouteCond func(req *http.Request, ctx common.HContextIface) RouteMatch

const (
	NoMatch       RouteMatch = 0
	OverloadMatch RouteMatch = 1
	RegexMatch    RouteMatch = math.MaxInt16
	ExactMatch    RouteMatch = math.MaxInt16
)

// //////RouteCond
func UrlHasPrefixR(prefix string) RouteCond {
	return func(req *http.Request, ctx common.HContextIface) RouteMatch {
		if strings.HasPrefix(req.URL.Path, prefix) ||
			strings.HasPrefix(req.URL.Host+req.URL.Path, prefix) ||
			strings.HasPrefix(req.URL.Scheme+req.URL.Host+req.URL.Path, prefix) {
			return RouteMatch(len(prefix))
		} else {
			return NoMatch
		}
	}
}

func UrlIsR(urls ...string) RouteCond {
	urlSet := make(map[string]bool)
	for _, u := range urls {
		urlSet[u] = true
	}
	return func(req *http.Request, ctx common.HContextIface) RouteMatch {
		_, pathOk := urlSet[req.URL.Path]
		_, hostAndOk := urlSet[req.URL.Host+req.URL.Path]
		if pathOk || hostAndOk {
			return ExactMatch
		} else {
			return NoMatch
		}
	}
}

func UrlMatchesR(re *regexp.Regexp) RouteCond {
	return func(req *http.Request, ctx common.HContextIface) RouteMatch {
		if re.MatchString(req.URL.Path) ||
			re.MatchString(req.URL.Host+req.URL.Path) {
			return RegexMatch
		} else {
			return NoMatch
		}
	}
}

////////ReqCond

// UrlHasPrefix returns a ReqCondition checking whether the destination URL the proxy client has requested
// has the given prefix, with or without the host.
// For example UrlHasPrefix("host/x") will match requests of the form 'GET host/x', and will match
// requests to url 'http://host/x'
func UrlHasPrefix(prefix string) ReqCond {
	return func(req *http.Request, ctx common.HContextIface) bool {
		return strings.HasPrefix(req.URL.Path, prefix) ||
			strings.HasPrefix(req.URL.Host+req.URL.Path, prefix) ||
			strings.HasPrefix(req.URL.Scheme+req.URL.Host+req.URL.Path, prefix)
	}
}

// UrlIs returns a ReqCondition, testing whether or not the request URL is one of the given strings
// with or without the host prefix.
// UrlIs("google.com/","foo") will match requests 'GET /' to 'google.com', requests `'GET google.com/' to
// any host, and requests of the form 'GET foo'.
func UrlIs(urls ...string) ReqCond {
	urlSet := make(map[string]bool)
	for _, u := range urls {
		urlSet[u] = true
	}
	return func(req *http.Request, ctx common.HContextIface) bool {
		_, pathOk := urlSet[req.URL.Path]
		_, hostAndOk := urlSet[req.URL.Host+req.URL.Path]
		return pathOk || hostAndOk
	}
}

// ReqHostMatches returns a ReqCondition, testing whether the host to which the request was directed to matches
// any of the given regular expressions.
func ReqHostMatches(regexps ...*regexp.Regexp) ReqCond {
	return func(req *http.Request, ctx common.HContextIface) bool {
		for _, re := range regexps {
			if re.MatchString(req.Host) {
				return true
			}
		}
		return false
	}
}

// ReqHostIs returns a ReqCondition, testing whether the host to which the request is directed to equal
// to one of the given strings
func ReqHostIs(hosts ...string) ReqCond {
	hostSet := make(map[string]bool)
	for _, h := range hosts {
		hostSet[h] = true
	}
	return func(req *http.Request, ctx common.HContextIface) bool {
		_, ok := hostSet[req.URL.Host]
		return ok
	}
}

var localHostIpv4 = regexp.MustCompile(`127\.0\.0\.\d+`)

// IsLocalHost checks whether the destination host is explicitly local host
// (buggy, there can be IPv6 addresses it doesn't catch)
var IsLocalHost ReqCond = func(req *http.Request, ctx common.HContextIface) bool {
	return req.URL.Host == "::1" ||
		req.URL.Host == "0:0:0:0:0:0:0:1" ||
		localHostIpv4.MatchString(req.URL.Host) ||
		req.URL.Host == "localhost"
}

// UrlMatches returns a ReqCondition testing whether the destination URL
// of the request matches the given regexp, with or without prefix
func UrlMatches(re *regexp.Regexp) ReqCond {
	return func(req *http.Request, ctx common.HContextIface) bool {
		return re.MatchString(req.URL.Path) ||
			re.MatchString(req.URL.Host+req.URL.Path)
	}
}

// DstHostIs returns a ReqCondition testing whether the host in the request url is the given string
func DstHostIs(host string) ReqCond {
	return func(req *http.Request, ctx common.HContextIface) bool {
		return req.URL.Host == host
	}
}

// SrcIpIs returns a ReqCondition testing whether the source IP of the request is one of the given strings
func SrcIpIs(ips ...string) ReqCond {
	return func(req *http.Request, ctx common.HContextIface) bool {
		for _, ip := range ips {
			if strings.HasPrefix(req.RemoteAddr, ip+":") {
				return true
			}
		}
		return false
	}
}

// Not returns a ReqCondition negating the given ReqCondition
func Not(r ReqCond) ReqCond {
	return func(req *http.Request, ctx common.HContextIface) bool {
		return !r(req, ctx)
	}
}

////////RespCond

// ContentTypeIs returns a RespCondition testing whether the HTTP response has Content-Type header equal
// to one of the given strings.
func ContentTypeIs(typ string, types ...string) RespCond {
	types = append(types, typ)
	return func(resp *http.Response, ctx common.HContextIface) bool {
		if resp == nil {
			return false
		}
		contentType := resp.Header.Get("Content-Type")
		for _, typ := range types {
			if contentType == typ || strings.HasPrefix(contentType, typ+";") {
				return true
			}
		}
		return false
	}
}
