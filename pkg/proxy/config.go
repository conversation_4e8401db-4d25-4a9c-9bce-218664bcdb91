/**
 * @note
 * config.go
 *
 * <AUTHOR>
 * @date 	2020-04-06
 */
package proxy

import (
	"regexp"
	"strings"
	"time"
)

type RouteConfig struct {
	Cond          []RouteCondConfig
	Server        string
	Timeout       time.Duration
	Retry         int
	RewriteRegexp []string
}

type RouteCondConfig struct {
	Func   string
	Values []string
}

type Config struct {
	Route []RouteConfig
}

var (
	config Config
)

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}

func ParseConfig(p *Proxy, c Config) {
	routes := make([]Route, len(c.Route))
	for idx, r := range c.Route {
		routes[idx].Conf = &RouteConf{
			Server:        r.Server,
			Timeout:       r.Timeout,
			Retry:         r.Retry,
			RewriteRegexp: r.RewriteRegexp,
		}
		for _, c := range r.Cond {
			if f := getRouteCondByConf(c); f != nil {
				routes[idx].Cond = append(routes[idx].Cond, f)
			}
		}
	}
	p.Router.SetRoutes(routes)
}

func getRouteCondByConf(conf RouteCondConfig) RouteCond {
	switch strings.ToLower(conf.Func) {
	case "urlis":
		if len(conf.Values) == 0 {
			return nil
		} else {
			return UrlIsR(conf.Values...)
		}
	case "urlhasprefix":
		if len(conf.Values) == 0 {
			return nil
		} else {
			return UrlHasPrefixR(conf.Values[0])
		}
	case "urlmatches":
		if len(conf.Values) == 0 {
			return nil
		} else if re, err := regexp.Compile(conf.Values[0]); err != nil {
			return nil
		} else {
			return UrlMatchesR(re)
		}
	}
	return nil
}
