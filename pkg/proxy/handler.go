/**
 * @note
 * handler
 *
 * <AUTHOR>
 * @date 	2020-03-31
 */
package proxy

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"io/ioutil"
	"net/http"
	"strings"
)

type ReqRule struct {
	Cond    []ReqCond
	Handler []ReqHandler
}

type RespRule struct {
	Cond    []RespCond
	Handler []RespHandler
}

type ReqHandler func(req *http.Request, ctx common.HContextIface) (*http.Request, *http.Response)
type RespHandler func(resp *http.Response, ctx common.HContextIface) *http.Response

func RespFilterHeader(headers ...string) RespHandler {
	m := make(map[string]bool)
	for _, h := range headers {
		m[strings.ToLower(h)] = true
	}
	return func(resp *http.Response, ctx common.HContextIface) *http.Response {
		if resp == nil {
			return resp
		}
		for k := range resp.Header {
			if m[strings.ToLower(k)] {
				resp.Header.Del(k)
			}
		}
		return resp
	}
}

func RespUnZip() RespHandler {
	return func(resp *http.Response, ctx common.HContextIface) *http.Response {
		if resp == nil {
			return resp
		}
		var resBodyBytes []byte
		var err error
		switch resp.Header.Get("Content-Encoding") {
		case "gzip":
			gzipReader, errGzip := gzip.NewReader(resp.Body)
			defer gzipReader.Close()
			err = errGzip
			resBodyBytes, err = ioutil.ReadAll(gzipReader)
			resp.Header.Del("Content-Encoding")
		case "deflate":
			flateReader := flate.NewReader(resp.Body)
			defer flateReader.Close()
			resBodyBytes, err = ioutil.ReadAll(flateReader)
			resp.Header.Del("Content-Encoding")
		default:
			return resp
		}
		if err != nil {
			ctx.Log().Errorln(err)
			return resp
		}
		resp.Body = ioutil.NopCloser(bytes.NewReader(resBodyBytes))
		return resp
	}
}

func ReqFilterHeader(headers ...string) ReqHandler {
	m := make(map[string]bool)
	for _, h := range headers {
		m[strings.ToLower(h)] = true
	}
	return func(req *http.Request, ctx common.HContextIface) (*http.Request, *http.Response) {
		if req == nil {
			return req, nil
		}
		for k := range req.Header {
			if m[strings.ToLower(k)] {
				req.Header.Del(k)
			}
		}
		return req, nil
	}
}
