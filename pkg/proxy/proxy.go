/**
 * @note
 * proxy.go
 *
 * <AUTHOR>
 * @date 	2020-03-25
 */
package proxy

import (
	"bytes"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"github.com/tmsong/hlog"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"time"
)

var tlsClientSkipVerify = &tls.Config{InsecureSkipVerify: true}

const (
	DefaultDialTimeout         = 5 * time.Second
	DefaultDialKeepAlive       = 60 * time.Second
	DefaultMaxIdleConns        = 500
	DefaultMaxIdleConnsPerHost = 100
	DefaultTLSHandshakeTimeout = 2 * time.Second
	DefaultRetry               = 2
)

type ProxyResult []byte

type Proxy struct {
	Router *ProxyRouter
	Tr     *http.Transport
}

func NewProxy() *Proxy {
	return &Proxy{
		Router: &ProxyRouter{},
		Tr: &http.Transport{
			TLSClientConfig: tlsClientSkipVerify,
			Proxy:           http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   DefaultDialTimeout,
				KeepAlive: DefaultDialKeepAlive,
			}).DialContext,
			MaxIdleConns:        DefaultMaxIdleConns,
			MaxIdleConnsPerHost: DefaultMaxIdleConnsPerHost, // default is 100
			TLSHandshakeTimeout: DefaultTLSHandshakeTimeout,
		},
	}
}

func (p *Proxy) Handle(req *http.Request, ctx common.HContextIface) {
	var err error
	gctx := ctx.GinCtx()
	w := gctx.Writer //http.responseWriter
	originReqCtx := req.Context()
	req, resp := p.Router.handleReq(req, ctx) //modify request

	if resp == nil && isWebSocketRequest(req) {
		ctx.Log().Println("Request looks like websocket upgrade.")
		p.serveWebsocket(ctx, w, req)
		return
	}

	routeConf := p.Router.matchRoute(req, ctx)

	if resp == nil && routeConf != nil {
		for try := routeConf.Retry + 1; try > 0; try-- {
			outreq, reqBody, errCopy := copyRequest(req)
			if errCopy != nil { //strange error, stop immediately
				resp = nil
				err = errCopy
				break
			}
			outreq = outreq.WithContext(originReqCtx) //in case the client cancel the request
			outreq = routeConf.director(outreq, ctx)  //director
			resp, err = p.Tr.RoundTrip(outreq)        //roundTrip
			field := hlog.GetLogField(common.LogtagRequestOk)
			field["post"] = string(reqBody)
			field["retry"] = routeConf.Retry - try + 1
			logRecordFunc(field, ctx, outreq, resp, err)
			if err != nil {
				resp = nil
			} else { //success, break
				break
			}
		}
	}
	resp = p.Router.handleResp(resp, ctx) //modify response
	if resp == nil {                      //short circuit
		if routeConf == nil {
			return
		}
		if err != nil {
			ctx.Log().Errorln("error read response " + req.URL.Host + " : " + err.Error())
		} else {
			err = errors.New("error read response " + req.URL.Host)
			ctx.Log().Errorln(err.Error())
		}
		common.SetRet(gctx, common.NewError(common.ERR_PROXY, err))
		return
	}
	origBody := resp.Body
	defer origBody.Close()
	resp.Header.Del("Content-Length")
	copyHeaders(w.Header(), resp.Header)
	w.WriteHeader(resp.StatusCode)
	if bodyBytes, errReadBody := ioutil.ReadAll(resp.Body); errReadBody != nil {
		ctx.Log().Errorf("Can't read response body %v", errReadBody)
		common.SetRet(gctx, common.NewError(common.ERR_PROXY, err))
	} else {
		common.SetRet(ctx.GinCtx(), ProxyResult(bodyBytes))
	}
	if errCloseBody := resp.Body.Close(); errCloseBody != nil {
		ctx.Log().Warningf("Can't close response body %v", errCloseBody)
	}
	return
}

func (p *Proxy) dial(network, addr string) (c net.Conn, err error) {
	if p.Tr.DialContext != nil {
		return p.Tr.DialContext(context.Background(), network, addr)
	}
	return net.Dial(network, addr)
}

func copyHeaders(dst, src http.Header) {
	for k, vs := range src {
		for _, v := range vs {
			dst.Add(k, v)
		}
	}
}

func copyRequest(req *http.Request) (newReq *http.Request, body []byte, err error) {
	newReq = new(http.Request)
	newURL := new(url.URL)
	*newReq = *req
	*newURL = *req.URL
	newReq.URL = newURL
	newReq.Header = make(http.Header)
	copyHeaders(newReq.Header, req.Header)

	if req.Body != nil {
		if body, err = ioutil.ReadAll(req.Body); err != nil {
			return nil, nil, err
		} else {
			copyRequestBody(req, body)
			copyRequestBody(newReq, body)
		}
	}
	if hostHeader := req.Header.Get("Host"); hostHeader != common.StringEmpty {
		newReq.Host = hostHeader
	}
	return newReq, body, nil
}

func copyRequestBody(req *http.Request, body []byte) {
	req.Body = ioutil.NopCloser(bytes.NewReader(body))
	req.ContentLength = int64(len(body))
	req.Header.Set("Content-Length", fmt.Sprintf("%d", req.ContentLength))
	if req.ContentLength == 0 {
		req.Body = nil // Issue 16036: nil Body for http.Transport retries
	}
}
