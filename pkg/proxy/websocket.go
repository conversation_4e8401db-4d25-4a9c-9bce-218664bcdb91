/**
 * @note
 * websocket
 *
 * <AUTHOR>
 * @date 	2020-03-30
 */
package proxy

import (
	"bufio"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"io"
	"net/http"
	"net/url"
	"strings"
)

func headerContains(header http.Header, name string, value string) bool {
	for _, v := range header[name] {
		for _, s := range strings.Split(v, ",") {
			if strings.EqualFold(value, strings.TrimSpace(s)) {
				return true
			}
		}
	}
	return false
}

func isWebSocketRequest(req *http.Request) bool {
	return headerContains(req.Header, "Connection", "upgrade") &&
		headerContains(req.Header, "Upgrade", "websocket")
}

func (p *Proxy) serveWebsocket(ctx common.HContextIface, w http.ResponseWriter, req *http.Request) {
	targetURL := url.URL{Scheme: "ws", Host: req.URL.Host, Path: req.URL.Path}

	targetConn, err := p.dial("tcp", targetURL.Host)
	if err != nil {
		ctx.Log().Warningf("Error dialing target site: %v", err)
		return
	}
	defer targetConn.Close()

	// Connect to Client
	hj, ok := w.(http.Hijacker)
	if !ok {
		panic("httpserver does not support hijacking")
	}
	clientConn, _, err := hj.Hijack()
	if err != nil {
		ctx.Log().Warningf("Hijack error: %v", err)
		return
	}

	// Perform handshake
	if err := p.websocketHandshake(ctx, req, targetConn, clientConn); err != nil {
		ctx.Log().Warningf("Websocket handshake error: %v", err)
		return
	}

	// Proxy ws connection
	p.proxyWebsocket(ctx, targetConn, clientConn)
}

func (p *Proxy) websocketHandshake(ctx common.HContextIface, req *http.Request, targetSiteConn io.ReadWriter, clientConn io.ReadWriter) error {
	// write handshake request to target
	err := req.Write(targetSiteConn)
	if err != nil {
		ctx.Log().Warningf("Error writing upgrade request: %v", err)
		return err
	}

	targetTLSReader := bufio.NewReader(targetSiteConn)

	// Read handshake response from target
	resp, err := http.ReadResponse(targetTLSReader, req)
	if err != nil {
		ctx.Log().Warningf("Error reading handshake response  %v", err)
		return err
	}

	// Run response through handlers
	resp = p.Router.handleResp(resp, ctx)

	// Proxy handshake back to client
	err = resp.Write(clientConn)
	if err != nil {
		ctx.Log().Warningf("Error writing handshake response: %v", err)
		return err
	}
	return nil
}

func (p *Proxy) proxyWebsocket(ctx common.HContextIface, dest io.ReadWriter, source io.ReadWriter) {
	errChan := make(chan error, 2)
	cp := func(dst io.Writer, src io.Reader) {
		_, err := io.Copy(dst, src)
		ctx.Log().Warningf("Websocket error: %v", err)
		errChan <- err
	}

	// Start proxying websocket data
	go cp(dest, source)
	go cp(source, dest)
	<-errChan
}
