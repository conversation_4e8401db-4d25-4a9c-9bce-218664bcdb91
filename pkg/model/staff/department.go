package staff

import (
	"context"
	"gitlab.docsl.com/security/bpm/internal/common"
	bpmCom "gitlab.docsl.com/security/bpm/pkg/common"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// Department related public functions
func CreateOrUpdateDepartments(ctx context.Context, departments []*DepartmentTable) (err error) {
	return DefaultService.CreateOrUpdateDepartments(ctx, departments)
}

func GetDepartmentByLarkDepartmentID(ctx context.Context, larkDepartmentID string) (tb *DepartmentTable, err error) {
	depts, err := DefaultService.GetDepartmentByLarkDepartmentIDs(ctx, []string{larkDepartmentID})
	if err != nil {
		return nil, err
	} else if len(depts) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	return depts[0], nil
}

func GetDepartmentsByLarkParentDepartmentID(ctx context.Context, larkParentDepartmentID string) (tbs []*DepartmentTable, err error) {
	return DefaultService.GetDepartmentByLarkLarkDepartmentID(ctx, larkParentDepartmentID)
}

func GetAllDepartments(ctx context.Context) (tbs []*DepartmentTable, err error) {
	return DefaultService.GetAllDepartments(ctx)
}

func GetDepartmentByID(ctx context.Context, departmentID int64) (tb *DepartmentTable, err error) {
	depts, err := DefaultService.GetDepartmentByIDs(ctx, []int64{departmentID})
	if err != nil {
		return nil, err
	} else if len(depts) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	return depts[0], nil
}

func GetPrimaryDepartmentByLarkUserID(ctx context.Context, larkUserID string) (tb *DepartmentTable, err error) {
	rels, err := GetLarkDepartmentRelationsByLarkUserID(ctx, larkUserID)
	if err != nil {
		return nil, err
	}
	for _, rel := range rels {
		if rel.IsPrimaryDept {
			depts, err := DefaultService.GetDepartmentByLarkDepartmentIDs(ctx, []string{rel.LarkDepartmentID})
			if err != nil {
				return nil, err
			} else if len(depts) == 0 {
				return nil, gorm.ErrRecordNotFound
			}
			return depts[0], nil
		}
	}
	return nil, gorm.ErrRecordNotFound
}

// GetLarkDepartmentRelationsByLarkUserID related public functions
func GetLarkDepartmentRelationsByLarkUserID(ctx context.Context, larkUserID string) ([]*UserDepartmentTableRelation, error) {
	return DefaultService.GetLarkDepartmentRelationsByLarkUserID(ctx, larkUserID)
}

func GetLarkDepartmentRelationsByLarkDepartmentID(ctx context.Context, larkDepartmentID string) ([]*UserDepartmentTableRelation, error) {
	return DefaultService.GetLarkDepartmentRelationsByLarkDepartmentID(ctx, larkDepartmentID)
}

func GetUsersInDepartmentByLarkDepartmentID(ctx context.Context, larkDepartmentID string) ([]*UserTable, error) {
	rels, err := DefaultService.GetLarkDepartmentRelationsByLarkDepartmentID(ctx, larkDepartmentID)
	if err != nil {
		return nil, err
	}
	if len(rels) == 0 {
		return nil, nil
	}
	larkUserIDs := make([]string, 0)
	for _, rel := range rels {
		larkUserIDs = append(larkUserIDs, rel.LarkUserID)
	}
	users, err := DefaultService.GetUsersByLarkUserIDs(ctx, larkUserIDs)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func CreateUserDepartmentRelation(ctx context.Context, relations []*UserDepartmentTableRelation) (err error) {
	return DefaultService.CreateUserDepartmentRelation(ctx, relations)
}

func UpdateUserDepartmentRelation(ctx context.Context, larkUserID, larkDepartmentID string, userOrder, departmentOrder *int, isPrimaryDept *bool) (err error) {
	return DefaultService.UpdateUserDepartmentRelation(ctx, larkUserID, larkDepartmentID, userOrder, departmentOrder, isPrimaryDept)
}

func HardDeleteUserDepartmentRelations(ctx context.Context, larkUserID, larkDepartmentID *string) (err error) {
	return DefaultService.HardDeleteUserDepartmentRelations(ctx, larkUserID, larkDepartmentID)
}

func TruncateUserDepartmentRelations(ctx context.Context) (err error) {
	return DefaultService.TruncateUserDepartmentRelations(ctx)
}

// Department related implementation methods
func (m *StaffModelImpl) CreateOrUpdateDepartments(ctx context.Context, tbs []*DepartmentTable) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	t := &DepartmentTable{}
	db = db.Model(&DepartmentTable{})
	// 这里使用自增ID
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "lark_department_id"}}, // 使用lark_department_id作为唯一键冲突检测
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()), // 冲突时更新这些字段
	}).CreateInBatches(tbs, 100).Error
	return err
}

func (m *StaffModelImpl) GetAllDepartments(ctx context.Context) ([]*DepartmentTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&DepartmentTable{})
	ret := make([]*DepartmentTable, 0)
	for pageSize, offset := 500, 0; ; {
		batch := make([]*DepartmentTable, 0)
		result := db.Where("status = ?", common.STATUS_NORMAL).Limit(pageSize).Offset(offset).Find(&batch)
		if result.Error != nil {
			return ret, result.Error
		}
		ret = append(ret, batch...)
		if len(batch) < pageSize {
			break
		}
		offset += pageSize
	}
	return ret, nil
}

func (m *StaffModelImpl) GetDepartmentByLarkDepartmentIDs(ctx context.Context, larkDepartmentIDs []string) ([]*DepartmentTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&DepartmentTable{})
	ret := make([]*DepartmentTable, 0)
	db = db.Where("lark_department_id in (?) and status = ?", larkDepartmentIDs, common.STATUS_NORMAL).Find(&ret)
	return ret, db.Error
}

func (m *StaffModelImpl) GetDepartmentByLarkLarkDepartmentID(ctx context.Context, larkParentDepartmentID string) ([]*DepartmentTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&DepartmentTable{})
	ret := make([]*DepartmentTable, 0)
	db = db.Where("lark_parent_department_id = ? and status = ?", larkParentDepartmentID, common.STATUS_NORMAL).Find(&ret)
	return ret, db.Error
}

func (m *StaffModelImpl) GetDepartmentByIDs(ctx context.Context, ids []int64) ([]*DepartmentTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&DepartmentTable{})
	ret := make([]*DepartmentTable, 0)
	db = db.Where("id in (?) and status = ?", ids, common.STATUS_NORMAL).Find(&ret)
	return ret, db.Error
}

// GetLarkDepartmentsOfUserByUserID related implementation methods
func (m *StaffModelImpl) GetLarkDepartmentRelationsByLarkUserID(ctx context.Context, larkUserID string) ([]*UserDepartmentTableRelation, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}

	var rels []*UserDepartmentTableRelation
	err = db.Model(&UserDepartmentTableRelation{}).
		Where("lark_user_id = ?", larkUserID).
		Find(&rels).Error

	return rels, err
}

func (m *StaffModelImpl) GetLarkDepartmentRelationsByLarkDepartmentID(ctx context.Context, larkDepartmentID string) ([]*UserDepartmentTableRelation, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}

	var rels []*UserDepartmentTableRelation
	err = db.Model(&UserDepartmentTableRelation{}).
		Where("lark_department_id = ?", larkDepartmentID).
		Find(&rels).Error

	return rels, err
}

func (m *StaffModelImpl) CreateUserDepartmentRelation(ctx context.Context, tbs []*UserDepartmentTableRelation) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	t := &UserDepartmentTableRelation{}
	db = db.Model(&UserDepartmentTableRelation{})
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "lark_user_id"}, {Name: "lark_department_id"}}, // 使用用户ID和部门ID作为联合唯一键
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()),                         // 冲突时更新这些字段
	}).CreateInBatches(tbs, 100).Error
	return err
}

func (m *StaffModelImpl) UpdateUserDepartmentRelation(ctx context.Context, larkUserID, larkDepartmentID string, userOrder, departmentOrder *int, isPrimaryDept *bool) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&UserDepartmentTableRelation{})
	updateMap := make(map[string]interface{})

	if userOrder != nil {
		updateMap["user_order"] = *userOrder
	}
	if departmentOrder != nil {
		updateMap["department_order"] = *departmentOrder
	}
	if isPrimaryDept != nil {
		updateMap["is_primary_dept"] = *isPrimaryDept
	}

	if len(updateMap) == 0 {
		return nil // 没有需要更新的字段
	}

	return db.Where("lark_user_id = ? AND lark_department_id = ?", larkUserID, larkDepartmentID).Updates(updateMap).Error
}

func (m *StaffModelImpl) HardDeleteUserDepartmentRelations(ctx context.Context, larkUserID, larkDepartmentID *string) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&UserDepartmentTableRelation{})

	// 构建删除条件
	if larkUserID != nil && larkDepartmentID != nil {
		// 删除特定用户在特定部门的关系
		return db.Where("lark_user_id = ? AND lark_department_id = ?", *larkUserID, *larkDepartmentID).Unscoped().Delete(&UserDepartmentTableRelation{}).Error
	} else if larkUserID != nil {
		// 删除特定用户的所有部门关系
		return db.Where("lark_user_id = ?", *larkUserID).Unscoped().Delete(&UserDepartmentTableRelation{}).Error
	} else if larkDepartmentID != nil {
		// 删除特定部门的所有用户关系
		return db.Where("lark_department_id = ?", *larkDepartmentID).Unscoped().Delete(&UserDepartmentTableRelation{}).Error
	}

	// 如果两个参数都为nil，不执行删除操作
	return nil
}

func (m *StaffModelImpl) TruncateUserDepartmentRelations(ctx context.Context) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&UserDepartmentTableRelation{}).Exec("TRUNCATE TABLE " + bpmCom.UserDepartmentRelationTableName)
	return db.Error
}
