/**
 * @note
 * Http Server相关功能封装
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"gitlab.docsl.com/security/common"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"

	. "gitlab.docsl.com/security/bpm/pkg/common"
)

func getAllowOrigins() []string {
	if IsDev() || IsTest() {
		return []string{}
	} else {
		return GetConfig().Domain
	}
}

func getAllowOriginFunc() func(string) bool {
	if IsDev() || IsTest() {
		return func(s string) bool { return true }
	} else {
		return nil
	}
}

type HttpServer struct {
	server *http.Server
	*gin.Engine
}

/* @note
 * 起http服务，接受可选的addr，如果不传则使用默认端口
 */
func (s *HttpServer) Run(addr ...string) {
	if len(addr) > 0 {
		s.server.Addr = addr[0]
	}
	err := s.server.ListenAndServe()
	if err != nil && err != http.ErrServerClosed {
		panic(err)
	}
}

/* @note
 * 优雅退出
 */
func GracefulExit(servers ...*HttpServer) {
	sig := <-SignalChan
	fmt.Printf("got a signal [%v]\n", sig)
	now := time.Now()
	wg := WaitGroupWrapper{}
	ctx, cancel := context.WithTimeout(context.Background(), GracefulTimeout)
	defer cancel()
	for idx := range servers {
		i := idx
		wg.Wrap(func() {
			err := servers[i].server.Shutdown(ctx)
			if err != nil {
				fmt.Printf("shutdown error [%v]", err)
			}
		})
	}
	wg.Wait()
	fmt.Println("all http server exited, use time", time.Since(now))
	close(QuitChan)
}

/* @note
 * 包装一个HttpServer，包含一个go的server和gin的router
 */
func Custom(l *hlog.Logger, addr string) *HttpServer {
	g := gin.New()
	g.Use(cors.New(cors.Config{
		AllowOrigins:     getAllowOrigins(),
		AllowOriginFunc:  getAllowOriginFunc(),
		AllowMethods:     AllowHttpMethods,
		AllowCredentials: true,
		AllowHeaders:     AllowHttpHeaders,
		ExposeHeaders:    ExposeHttpHeaders,
	}))
	g.Use(AccessLogger(l)) //日志初始化
	g.Use(Recovery())      //recover
	s := &http.Server{Addr: addr, Handler: g}
	return &HttpServer{
		Engine: g,
		server: s,
	}
}

/* @note
 * 用于记录入参和出参
 */
func AccessLogger(l *hlog.Logger) gin.HandlerFunc {
	if l == nil {
		panic("nil logger")
	}
	return func(ctx *gin.Context) {
		field := hlog.GetLogField(LogtagAccessOut)
		logid := GenerateId()
		newLogger := l.Clone(logid)
		// 将请求中的requestID和logger中的traceID同步
		if requestID := ctx.GetHeader(common.XRequestIDHeaderKey); requestID != "" {
			newLogger.SetTraceId(requestID)
		} else {
			ctx.Set(common.XRequestIDHeaderKey, newLogger.GetTraceId())
			ctx.Writer.Header().Set(common.XRequestIDHeaderKey, newLogger.GetTraceId())
		}
		SetHCtx(ctx, NewContext(newLogger, ctx))
		getInput := ctx.Request.URL.Query()
		ctx.Request.ParseForm()
		postInput := ctx.Request.Form
		addr := ctx.Request.Header.Get("X-Real-IP")
		if addr == "" {
			addr = ctx.Request.Header.Get("X-Forwarded-For")
			if addr == "" {
				addr = ctx.Request.RemoteAddr
			}
		}
		body := make([]byte, 0)
		if strings.Contains(ctx.Request.Header.Get("Content-Type"), "application/json") {
			safe := &io.LimitedReader{R: ctx.Request.Body, N: MaxMemory}
			body, _ = ioutil.ReadAll(safe)
			ctx.Request.Body.Close()
			bf := bytes.NewBuffer(body)
			ctx.Request.Body = http.MaxBytesReader(ctx.Writer, ioutil.NopCloser(bf), MaxMemory)
		}
		f := logrus.Fields{
			"tag":    LogtagAccessIn,
			"url":    ctx.Request.URL.Path,
			"get":    getInput,
			"post":   postInput,
			"body":   strings.Replace(string(body), "\n", "", -1),
			"method": ctx.Request.Method,
			"host":   addr,
			"header": ctx.Request.Header,
			"domain": ctx.Request.Host,
		}
		PrintLogWithError(nil, newLogger, f)
		////////////////////////////////////////////////////////
		ctx.Next()
		////////////////////////////////////////////////////////
		ret := GetRet(ctx)
		api := ctx.Request.Method + ctx.Request.URL.Path
		if ret == nil {
			return
		}
		var eSys *Error
		var outputWithErrDesc []byte
		switch ret := ret.(type) {
		case *Error:
			outputWithErrDesc = ret.Json()
			eSys = ret
			RenderError(ctx, ret)
		case Error:
			outputWithErrDesc = ret.Json()
			eSys = &ret
			RenderError(ctx, &ret)
		case []byte:
			RenderBytes(ctx, ret)
		case string:
			RenderBytes(ctx, []byte(ret))
		default:
			desc := fmt.Sprintf("uri is unknown")
			eSys = NewError(ERR_SYS, desc)
			outputWithErrDesc = eSys.Json()
			RenderError(ctx, eSys)
		}
		//print log
		f = logrus.Fields{
			"url":    ctx.Request.URL.Path,
			"get":    getInput,
			"post":   postInput,
			"body":   string(body),
			"method": ctx.Request.Method,
			"out":    string(outputWithErrDesc),
			"host":   addr,
			"uri":    api,
			"header": ctx.Writer.Header(),
			"domain": ctx.Request.Host,
		}
		PrintLogWithError(eSys, newLogger, field, f)
	}
}

func RenderJSON(ctx *gin.Context, output, outputWithErrDesc interface{}) {
	if IsProd() {
		ctx.JSON(http.StatusOK, output)
	} else {
		ctx.JSON(http.StatusOK, outputWithErrDesc)
	}
}

// 渲染公共定义的Error结构体
func RenderError(ctx *gin.Context, output *Error) {
	if IsProd() {
		ctx.JSON(http.StatusOK, output.ExcludeStack())
	} else {
		ctx.JSON(http.StatusOK, output)
	}
}

func RenderBytes(ctx *gin.Context, output []byte) {
	if json.Valid(output) {
		ctx.Data(http.StatusOK, "application/json; charset=utf-8", output)
	} else {
		ctx.Data(http.StatusOK, "text/plain; charset=utf-8", output)
	}
}

// 用于中间件出错时，渲染并直接返回
func RenderJsonAndAbort(ctx *gin.Context, output, outputWithErrDesc interface{}) {
	if IsProd() {
		ctx.AbortWithStatusJSON(http.StatusOK, output)
	} else {
		ctx.AbortWithStatusJSON(http.StatusOK, outputWithErrDesc)
	}
}

// 用于中间件出错时，渲染并直接返回
func RenderBytesAndAbort(ctx *gin.Context, output []byte) {
	if json.Valid(output) {
		ctx.Data(http.StatusOK, "application/json; charset=utf-8", output)
	} else {
		ctx.Data(http.StatusOK, "text/plain; charset=utf-8", output)
	}
	ctx.Abort()
}

// 用于中间件出错时，渲染并直接返回
func RenderErrorAndAbort(ctx *gin.Context, output *Error) {
	if IsProd() {
		ctx.AbortWithStatusJSON(http.StatusOK, output.ExcludeStack())
	} else {
		ctx.AbortWithStatusJSON(http.StatusOK, output)
	}
}

/* @note
 * 从panic中恢复的中间件
 */
func Recovery() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				stack := IdentifyPanic()
				var output *Error
				switch e := err.(type) {
				case *Error:
					output = e.AppendStack(stack)
				default:
					output = NewError(ERR_UNKNOWN).AppendStack(err).AppendStack(stack)
				}
				SetRet(ctx, output)
				content, _ := ToPrettyJsonString(map[string]interface{}{
					"env":   GetEnv(),
					"trace": GetHCtx(ctx).Log().GetTrace(),
					"content": map[string]interface{}{
						"panic": err,
						"stack": stack,
					},
					"host": func() string {
						host, _ := os.Hostname()
						return host
					}(),
				})
				_ = lark.SendLarkRobotAlarmMessage(ctx, content)
				return
			}
		}()
		ctx.Next()
	}
}
