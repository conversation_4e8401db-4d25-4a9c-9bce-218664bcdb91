/**
 * @note
 * proxy.go
 *
 * <AUTHOR>
 * @date 	2020-04-03
 */
package middleware

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/proxy"
	common2 "gitlab.docsl.com/security/common"
	"strconv"
	"strings"
)

func Proxy() gin.HandlerFunc {
	p := proxy.NewProxy()
	proxy.ParseConfig(p, proxy.GetConfig()) //temp way, optimize after
	p.Router.SetReqRules([]proxy.ReqRule{{
		Handler: []proxy.ReqHandler{
			proxy.ReqFilterHeader("Origin",
				"Referer",
			), //filter req header
		},
	}})
	p.Router.SetRespRules([]proxy.RespRule{{ //filter resp header
		Handler: []proxy.RespHandler{
			proxy.RespFilterHeader("<PERSON>-<PERSON><PERSON>",
				common2.XRequestIDHeaderKey,
				"Access-Control-Allow-Credentials",
				"Access-Control-Allow-Origin",
				"Access-Control-Expose-Headers",
				"Vary",
			), //filter req header
		},
	}})
	return func(gctx *gin.Context) {
		ctx := common.GetHCtx(gctx)
		ctx.Log().Debugf("proxy: handling proxy for %s:%v", gctx.Request.Method, gctx.Request.URL)
		//set user info
		originDisplayName := ctx.User().DisplayName
		ctx.User().DisplayName = strings.Trim(strconv.QuoteToASCII(ctx.User().DisplayName), "\"") //displayName to ascii convert
		userInfo, err := common.JsonEncode(ctx.User())
		userInfo = []byte(strings.ReplaceAll(string(userInfo), "\\\\", "\\"))
		ctx.User().DisplayName = originDisplayName
		if err != nil {
			ctx.Log().Errorf("proxy: package user info error %v", err)
			common.SetRet(gctx, common.NewError(common.ERR_SYS, err))
			gctx.Abort()
			return
		}
		//handle it to proxy
		gctx.Request.Header.Set(common.HuobiSsoUser, string(userInfo))
		p.Handle(gctx.Request, ctx)
	}
}
