/**
 * @note
 * csrf-token
 *
 * <AUTHOR>
 * @date 	2020-02-05
 */
package middleware

import (
	"github.com/gin-gonic/gin"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"strings"
)

var NoCsrfCheckUrls = map[string]bool{
	"/info":            true,
	"/actuator/health": true,
}

const TEST_HEADER = "handsomefe"

func ValidateCsrf() gin.HandlerFunc {
	return func(gctx *gin.Context) {
		ctx := common.GetHCtx(gctx)
		ctx.Log().Debugf("csrf: handling authentication for %s:%v", gctx.Request.Method, gctx.Request.URL)
		if NoCsrfCheckUrls[gctx.Request.URL.Path] {
			ctx.Log().Debugln("no check url, pass")
			return
		} else if strings.Contains(gctx.Request.URL.Path, "/file/") || strings.Contains(gctx.Request.URL.Path, "/cas/") {
			ctx.Log().Debugln("url is about file operation, pass")
			return
		}
		if len(gctx.GetHeader(common.HuobiHeaderAuthToken)) > 0 { //说明是sdk的auth_token登录，不校验csrf
			ctx.Log().Debugln("csrf: sdk auth token, skip")
			return
		}
		//取出header的csrfToken
		headerCsrfToken := gctx.GetHeader(common.HuobiHeaderCsrfToken)
		ctx.Log().Debugln("csrf: header:", headerCsrfToken)
		if !common.IsProd() && strings.ToLower(headerCsrfToken) == TEST_HEADER {
			ctx.Log().Debugln("csrf: header is for test, skip")
			return
		}
		if len(headerCsrfToken) == 0 {
			RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR))
			return
		}
		//取出cookie的csrfToken
		cookieCsrfToken, err := gctx.Cookie(common.CsrfCookieName)
		ctx.Log().Debugln("csrf: cookie:", cookieCsrfToken)
		if err != nil {
			RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR, err))
			return
		} else if len(cookieCsrfToken) == 0 {
			RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR))
			return
		} else if headerCsrfToken != cookieCsrfToken {
			RenderErrorAndAbort(gctx, common.NewError(common.ERR_AUTHERR))
			return
		}
	}
}

// 生成csrfToken并种cookie
func SetCsrf(gctx *gin.Context) {
	csrfToken := common.RandAuthStr(32)
	gctx.SetCookie(common.CsrfCookieName,
		csrfToken,
		3600*6,
		"/",
		common.GetConfig().CsrfCookieDomain,
		false,
		false)
	RenderError(gctx, common.NewError(common.ERR_OK))
	return
}
