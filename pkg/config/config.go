/**
 * @note
 * 总体的配置加载模块，读取toml配置后，再将对应配置下发给各个模块
 *
 * <AUTHOR>
 * @date 	2019-10-29
 */
package config

import (
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/lark"
	"gitlab.docsl.com/security/common/aws"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
	"os"
	"path/filepath"

	"github.com/tmsong/toml"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/aws_s3"
	"gitlab.docsl.com/security/bpm/pkg/helper/email"
	"gitlab.docsl.com/security/bpm/pkg/helper/tmpl_parser"
	"gitlab.docsl.com/security/common/cas"
	"gitlab.docsl.com/security/common/mysql"
	"gitlab.docsl.com/security/common/redis"
)

// 执行环境
type Env struct {
	BinDir  string
	RootDir string
	LogDir  string
	ConfDir string
	TmplDir string
}

type Config struct {
	Common common.Config
	Mysql  mysql.Config
	Redis  redis.Config
	Bpm    bpmCom.Config
	Lark   lark.Config
	Aws    aws.Config
	S3     aws_s3.Config
	Email  email.Config
	Cas    cas.Config
	Log    logger.Config
}

var (
	AppEnv    Env
	AppConfig Config
)

// 初始化执行环境
func init() {
	var err error
	AppEnv.BinDir, err = filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		panic(err)
	}
	AppEnv.RootDir = filepath.Dir(AppEnv.BinDir)
	AppEnv.LogDir = filepath.Join(AppEnv.RootDir, "/log")
	AppEnv.ConfDir = filepath.Join(AppEnv.RootDir, "/conf")
	AppEnv.TmplDir = filepath.Join(AppEnv.ConfDir, "/tmpl")
}

// 加载配置信息
func ReplaceAndLoad(f string, decrypt bool) (string, error) {
	//读取配置文件
	_, err := toml.DecodeFile(f, &AppConfig)
	if err != nil {
		return f, err
	}
	if decrypt {
		AppConfig, err = cryptConfig(AppConfig)
		if err != nil {
			return f, err
		}
	}
	//配置各个模块
	common.SetupConfig(AppConfig.Common)
	mysql.SetupConfig(AppConfig.Mysql)
	redis.SetupConfig(AppConfig.Redis)
	bpmCom.SetupConfig(AppConfig.Bpm)
	aws_s3.SetupConfig(AppConfig.S3)
	email.SetupConfig(AppConfig.Email)
	cas.SetupConfig(AppConfig.Cas)
	lark.SetupConfig(AppConfig.Lark,decrypt)
	aws.SetupConfig(AppConfig.Aws)
	logger.SetupConfig(AppConfig.Log)
	tmpl_parser.LoadTmpl(AppEnv.TmplDir)
	return f, nil
}

func cryptConfig(c Config) (Config, error) {
	appConfigIface, err := masker.PropCrypt(c)
	if err != nil {
		return c, err
	}
	return appConfigIface.(Config), nil
}

func OutputConfigToFile(file string, encrypt bool) (err error) {
	f, err := os.OpenFile(file, os.O_RDWR|os.O_CREATE, 0666)
	defer f.Close()
	if err != nil {
		return err
	}
	encoder := toml.NewEncoder(f)
	out := AppConfig
	if encrypt {
		masker.SetIsEncrypt(true)
		out, err = cryptConfig(AppConfig)
		if err != nil {
			return err
		}
		masker.SetIsEncrypt(false)
	}

	if err = encoder.Encode(out); err != nil {
		return err
	}
	return
}
