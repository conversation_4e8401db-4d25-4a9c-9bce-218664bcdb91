/**
 * @note
 * 火币UC消息中心 邮件配置
 *
 * <AUTHOR>
 * @date 	2019-11-22
 */
package email

var config Config

type Config struct {
	Host         string
	Url          string
	AppId        string `mask:"crypt"`
	AppKey       string `mask:"crypt"`
	SendStrategy string
	ExchangeId   int
	Scene        string
	BusinessType string
	Version      int
	EmailTitle   string
}

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}
