package email

import (
	"net/http"
	"strconv"

	"gitlab.docsl.com/security/bpm/pkg/common"
	"gitlab.docsl.com/security/bpm/pkg/helper/httpclient"
	"gopkg.in/bufio.v1"
)

/**
 * @note
 * 调用公司UC接口发送邮件通知
 * 参考"http://phabricator.huobidev.com/w/back_end_team/old_huobi_team/msg_center/inter/#post-mc-inter-message-se"
 *
 * @param ctx HContextIface 通用context
 * @param appName 发送到哪个配置的钉钉机器人
 * @param content 能够进行json编码的发送内容
 *
 * @return error
 */
func SendEmail(ctx common.HContextIface, emailAddr string, title string, content string) error {
	return nil
	client := httpclient.NewHttpClient([]string{config.Host}, "", ctx.Log())
	data := map[string]interface{}{
		"version":       2,
		"send_strategy": config.SendStrategy,
		"exchange_id":   strconv.Itoa(config.ExchangeId),
		"email":         emailAddr,
		"scene":         config.Scene,
		"business_type": config.BusinessType,
		"email_title":   title,
		"email_content": content,
	}
	bodyBytes, err := common.JsonEncode(data)
	if err != nil {
		return err
	}
	if req, err := http.NewRequest("POST", config.Host+config.Url, bufio.NewBuffer(bodyBytes)); err != nil {
		return err
	} else if req, err = common.SignReq(req, config.AppId, config.AppKey); err != nil {
		return err
	} else if _, err = client.Do(req); err != nil {
		return common.ErrHttpResponse
	}
	return nil
}
