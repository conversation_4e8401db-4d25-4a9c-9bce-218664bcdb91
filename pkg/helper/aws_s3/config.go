/**
 * @note
 * aws s3对象存储配置
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */
package aws_s3

var config Config

type Config struct {
	Url        string
	BucketName string
	AccessKey  string `mask:"crypt"`
	SecretKey  string `mask:"crypt"`
	BlockSize  int
	PathStyle  bool
	External   map[string]ExternalS3
}

type ExternalS3 struct {
	Host string
	Url  string
}

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}
