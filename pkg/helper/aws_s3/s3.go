/**
 * @note
 * AWS-S3作为对象存储
 * 对文件进行上传和下载
 *
 * <AUTHOR>
 * @date 	2019-11-20
 */
package aws_s3

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/endpoints"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/tmsong/hlog"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

type S3Provider struct{}

func (m *S3Provider) Retrieve() (credentials.Value, error) {
	return credentials.Value{
		AccessKeyID:     config.AccessKey,
		SecretAccessKey: config.<PERSON><PERSON><PERSON>,
	}, nil
}

func (m *S3Provider) IsExpired() bool { return false }

type S3Client struct {
	*s3.S3
	logger *hlog.Logger
}

func NewS3Client(logger *hlog.Logger) *S3Client {
	return &S3Client{
		S3:     getS3Client(),
		logger: logger,
	}
}

/**
 * @note
 * 连接aws s3服务
 *
 * @return *s3.S3  S3的客户端对象
 * @return err     发生的错误
 */
func getS3Client() *s3.S3 {
	sess := session.Must(session.NewSessionWithOptions(session.Options{
		Config: aws.Config{
			Region:           aws.String(endpoints.ApNortheast1RegionID), //所在Region
			Endpoint:         &config.Url,
			S3ForcePathStyle: &config.PathStyle,
			Credentials:      credentials.NewCredentials(&S3Provider{}),
		},
	}))
	return s3.New(sess)
}

/**
 * @note
 * 上传文件到S3
 *
 * @param srcName 本地文件的路径
 * @param dstName 上传到s3后的文件名称
 *
 *
 * @return string  上传成功后返回该文件在S3的存储路径
 * @return err     发生的错误
 */
func (c *S3Client) Upload(f multipart.File, dstName, contentType string) (string, error) {
	dstName = c.md5FileName(f, dstName)
	inputs := &s3.CreateMultipartUploadInput{
		Bucket:      aws.String(config.BucketName),
		Key:         aws.String(dstName),
		ContentType: &contentType,
	}

	outputs, err := c.CreateMultipartUpload(inputs)
	if err != nil {
		fmt.Println("failed to upload file due to param error.", err.Error())
		return "", err
	}
	uploadId := outputs.UploadId

	reader := bufio.NewReader(f)
	buf := make([]byte, config.BlockSize)

	var partNum int64 = 0
	var completes []*s3.CompletedPart

	for {
		n, err := reader.Read(buf)
		if err == io.EOF {
			fmt.Println("read data finished")
			break
		}
		if n != config.BlockSize {
			data := make([]byte, n)
			data = buf[0:n]
			buf = data
		}

		partNum++ //每次的序号唯一且递增
		param := &s3.UploadPartInput{
			Bucket:        aws.String(config.BucketName),
			Key:           aws.String(dstName),
			PartNumber:    aws.Int64(partNum),
			UploadId:      uploadId,
			Body:          bytes.NewReader(buf),
			ContentLength: aws.Int64(int64(n)),
		}

		resp, err := c.UploadPart(param)
		if err != nil {
			fmt.Printf("failed to upload the segment[%d] of file[%s]", partNum, dstName)
			return "", err
		}

		var c s3.CompletedPart
		c.PartNumber = aws.Int64(partNum) // Required Etag对应的PartNumber, 上一步返回的
		c.ETag = resp.ETag                // Required 上传分片时返回的值 Etag
		completes = append(completes, &c)
	}

	// 结束上传
	params := &s3.CompleteMultipartUploadInput{
		Bucket:   aws.String(config.BucketName),
		Key:      aws.String(dstName),
		UploadId: uploadId,
		MultipartUpload: &s3.CompletedMultipartUpload{
			Parts: completes,
		},
	}

	resResponse, err := c.CompleteMultipartUpload(params)
	if err != nil {
		fmt.Printf("upload entire file failed")
		return "", err
	}
	//preSignUrl := c.GetPreSignUrl(*resResponse.Key)
	return *resResponse.Key, nil
}

/**
 * @note
 * 从S3下载文件
 * 只是提供了这样的功能，实际上可能不会使用，而是从浏览器直接下载
 *
 * @param srcName s3上的文件路径
 * @param dstName 本地文件路径
 *
 *
 * @return err     发生的错误
 */
func (c *S3Client) Download(srcName, dstName string) error {
	resp, err := c.GetObject(&s3.GetObjectInput{
		Bucket: &config.BucketName,
		Key:    &srcName,
	})
	if err != nil {
		fmt.Println("failed to download file from s3", err.Error())
		return err
	}

	out, err := os.OpenFile(dstName, os.O_CREATE|os.O_RDWR, 0666)
	if out == nil {
		fmt.Println("Open fail")
		return err
	}
	num, err := io.Copy(out, resp.Body)
	fmt.Printf("\n write %d err %v \n", num, err)
	return nil
}

/**
 * @note
 * 从S3获取图片的流数据
 *
 *
 * @param srcName 文件名称
 *
 *
 * @return err     发生的错误
 */
func (c *S3Client) GetFileStream(objName string) (imageData []byte, contentType string, err error) {
	resp, err := c.GetObject(&s3.GetObjectInput{
		Bucket: &config.BucketName,
		Key:    &objName,
	})
	if err != nil {
		fmt.Println("failed to download file from s3", err.Error())
		return nil, contentType, err
	}
	if resp.ContentType != nil {
		contentType = *resp.ContentType
	}

	defer resp.Body.Close()
	imgData, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, contentType, err
	}
	return imgData, contentType, nil
}

/**
 * @note
 * 判断S3对象是否存在
 *
 * @param key S3对象的文件名
 *
 *
 * @return bool
 * @return error
 */
func (c *S3Client) IsObjectExist(objectName string) (bool, error) {
	_, err := c.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(config.BucketName),
		Key:    aws.String(objectName)})
	if err != nil {
		return false, err
	} else {
		return true, nil
	}
}

/**
 * @note
 * 对S3对象的url进行preSign
 * 有了preSign url，浏览器可以直接从S3下载文件
 *
 * @param key S3对象的文件名
 *
 *
 * @return string     返回signed URL
 */
func (c *S3Client) GetPreSignUrl(key string) (preSignUrl string, contentType string, err error) {

	fileNameArr := strings.Split(key, ".")
	fileSegCount := len(fileNameArr)

	fileName := key

	if fileSegCount > 2 {
		fileName = strings.Join(fileNameArr[1:fileSegCount], ".")
	}
	objAttr, err := c.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(config.BucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return common.StringEmpty, common.StringEmpty, err
	}
	if objAttr.ContentType != nil {
		contentType = *objAttr.ContentType
	}
	req, _ := c.GetObjectRequest(&s3.GetObjectInput{
		Bucket:                     aws.String(config.BucketName),
		Key:                        aws.String(key),
		ResponseContentEncoding:    aws.String("UTF-8"),
		ResponseContentDisposition: aws.String(fmt.Sprintf("attachment; filename*=utf8''%s", strings.Replace(url.QueryEscape(fileName), "+", "%20", -1))),
	})
	preSignUrl, err = req.Presign(5 * time.Minute)
	return
}

/**
 * @note
 * 对文件名加32位uuid前缀
 *
 *
 * @param key 文件名
 *
 *
 * @return string     返回md5.fileName
 */
func (c *S3Client) md5FileName(file multipart.File, fileName string) string {
	return common.NewUuid(common.RESOURCE_TYPE_S3_OBJECT) + "." + fileName
	//hash := md5.New()
	//io.Copy(hash, file)
	//return hex.EncodeToString(hash.Sum(nil)) + "." + fileName
}
