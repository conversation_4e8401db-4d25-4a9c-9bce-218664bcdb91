/**
 * @note
 * 一些相关函数
 *
 * <AUTHOR>
 * @date 	2019-12-25
 */
package tmpl_parser

import (
	"fmt"
	"html/template"
)

var funcMap = template.FuncMap{
	"NumberToString": NumberToString,
}

func NumberToString(num interface{}) interface{} {
	switch num.(type) {
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", num)
	case float32, float64:
		return fmt.Sprintf("%.2f", num)
	}
	return num
}
