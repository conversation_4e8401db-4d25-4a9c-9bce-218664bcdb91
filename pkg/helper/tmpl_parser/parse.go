/**
 * @note
 * tmpl_parser
 *
 * <AUTHOR>
 * @date 	2019-12-25
 */
package tmpl_parser

import (
	"bytes"
	"fmt"
	"html/template"
	"path/filepath"

	"gitlab.docsl.com/security/bpm/pkg/common"
)

var templates map[string]*template.Template

func LoadTmpl(tmplDir string) error {
	if templates == nil {
		templates = make(map[string]*template.Template)
	}
	files, err := filepath.Glob(tmplDir + "/*")
	if err != nil {
		panic(err)
	}
	for _, file := range files {
		templates[filepath.Base(file)] = template.Must(template.New(filepath.Base(file)).Funcs(funcMap).ParseFiles(file))
	}
	return nil
}

func Parse(name string, data interface{}) (out string, err error) {
	tmpl, ok := templates[name]
	if !ok {
		return common.StringEmpty, fmt.Errorf("template [%s] not exist", name)
	}
	var doc bytes.Buffer
	err = tmpl.ExecuteTemplate(&doc, name, data)
	if err != nil {
		return
	}
	out = doc.String()
	return
}
