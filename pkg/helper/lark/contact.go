/**
 * @note
 * contact
 *
 * <AUTHOR>
 * @date 	2025-08-01
 */
package lark

import (
	"context"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	bpmCom "gitlab.docsl.com/security/bpm/internal/common"
	"gitlab.docsl.com/security/common"
)

func GetDepartmentChildren(ctx context.Context, cli *lark.Client, departmentID string, fetchChild bool, pageSize int) (
	items []*larkcontact.Department, err error) {
	if pageSize > MaxPageSize || pageSize <= 0 {
		return nil, common.ErrInvalidFieldf("pageSize")
	}
	items = make([]*larkcontact.Department, 0)
	var pageToken string
	for hasMore := true; hasMore; hasMore = pageToken != common.StringEmpty {
		childrenResp, err := cli.Contact.Department.Children(ctx,
			larkcontact.NewChildrenDepartmentReqBuilder().
				DepartmentId(departmentID).
				UserIdType("user_id").
				DepartmentIdType("department_id").
				FetchChild(fetchChild).
				PageSize(pageSize).PageToken(pageToken).Build())
		if err != nil {
			return items, err
		}
		if childrenResp.Data.PageToken == nil {
			pageToken = common.StringEmpty
		} else {
			pageToken = *childrenResp.Data.PageToken
		}
		items = append(items, childrenResp.Data.Items...)
	}
	return items, nil
}

func GetDepartmentUsers(ctx context.Context, cli *lark.Client, departmentID string, pageSize int) (
	items []*larkcontact.User, err error) {
	items = make([]*larkcontact.User, 0)
	var pageToken string
	for hasMore := true; hasMore; hasMore = pageToken != common.StringEmpty {
		usersResp, err := cli.Contact.User.FindByDepartment(ctx,
			larkcontact.NewFindByDepartmentUserReqBuilder().
				DepartmentId(departmentID).
				UserIdType("user_id").
				DepartmentIdType("department_id").
				PageSize(pageSize).Build())
		if err != nil {
			return nil, err
		}
		if usersResp.Data.PageToken == nil {
			pageToken = common.StringEmpty
		} else {
			pageToken = *usersResp.Data.PageToken
		}
		items = append(items, usersResp.Data.Items...)
	}
	return items, nil
}

func GetBpmComUserStatusByLarkUserStatus(larkUserStatus *larkcontact.UserStatus) bpmCom.UserStatusEnum {
	if larkUserStatus == nil {
		return bpmCom.StatusActivated
	}
	if larkUserStatus.IsActivated != nil && *larkUserStatus.IsActivated {
		return bpmCom.StatusActivated
	}
	if larkUserStatus.IsResigned != nil && *larkUserStatus.IsResigned {
		return bpmCom.StatusResigned
	}
	if larkUserStatus.IsExited != nil && *larkUserStatus.IsExited {
		return bpmCom.StatusExited
	}
	if larkUserStatus.IsFrozen != nil && *larkUserStatus.IsFrozen {
		return bpmCom.StatusFrozen
	}
	if larkUserStatus.IsUnjoin != nil && *larkUserStatus.IsUnjoin {
		return bpmCom.StatusUnJoin
	}
	return bpmCom.StatusActivated
}
