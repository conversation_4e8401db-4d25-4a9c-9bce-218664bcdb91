/**
 * @note
 * callback_handler
 *
 * <AUTHOR>
 * @date 	2025-08-05
 */
package lark

import (
	"github.com/gin-gonic/gin"
	larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	"gitlab.docsl.com/security/common"
	"net/http"
)

func WrapLarkEventDispatcher() *dispatcher.EventDispatcher {
	return dispatcher.NewEventDispatcher(GetConfig().VerificationToken, GetConfig().EventEncryptKey)
}

func NewEventHandlerFunc(eventDispatcher *dispatcher.EventDispatcher, options ...larkevent.OptionFunc) gin.HandlerFunc {
	eventDispatcher.InitConfig(options...)
	return func(gctx *gin.Context) {
		rawBody, err := gctx.GetRawData()
		if err != nil {
			gctx.String(http.StatusInternalServerError, err.Error()) // 给个500
			return
		}
		eventReq := &larkevent.EventReq{
			Header:     gctx.Request.Header,
			Body:       rawBody,
			RequestURI: gctx.Request.RequestURI,
		}
		// 处理请求
		eventResp := eventDispatcher.Handle(gctx, eventReq)

		// 回写结果
		gctx.Writer.WriteHeader(eventResp.StatusCode)
		for k, vs := range eventResp.Header {
			for _, v := range vs {
				gctx.Writer.Header().Add(k, v)
			}
		}

		if len(eventResp.Body) > 0 {
			_, err = gctx.Writer.Write(eventResp.Body)
			if err != nil {
				common.GetLogger(gctx).Errorf("lark event handler write resp result error:%v", err)
			}
		}
	}
}
