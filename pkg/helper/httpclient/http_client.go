package httpclient

import (
	"bytes"
	"errors"
	"github.com/tmsong/hlog"
	"gitlab.docsl.com/security/bpm/pkg/common"
	"io"
	"math/rand"
	"net/http"
	"reflect"
	"sync"
	"time"
)

var rnd *rand.Rand

func init() {
	rnd = rand.New(rand.NewSource(time.Now().UnixNano()))
}

var DefaultDisabledTime int64 = 3

// 暂时不支持cookie
type HttpClient struct {
	client       *http.Client
	Servers      map[string]int64
	Prefix       string
	logger       *hlog.Logger
	DisabledTime int64
	mu           sync.RWMutex // TODO 此处是否需要加锁？
	Timeout      time.Duration
	HideLog      bool
}

// 直接支持url形式,默认只能设置三个参数
func NewHttpClient(servers []string, prefix string, l *hlog.Logger) *HttpClient {
	return &HttpClient{
		Servers: func(servers []string) map[string]int64 {
			srvs := make(map[string]int64)
			for _, server := range servers {
				srvs[server] = time.Now().Unix()
			}
			return srvs
		}(servers),
		Prefix:       prefix,
		DisabledTime: DefaultDisabledTime,
		logger:       l,
		client:       &http.Client{},
	}
}

// POST请求，支持application和multipart两种传输方式，默认是application
func (this *HttpClient) Post(
	uri string,
	param interface{}, headParam map[string]interface{},
	timeout time.Duration,
	attemptsRemaining int,
) (string, int, error) {
	server := this.pickServer()
	url := server + this.Prefix + uri
	//定义重试函数
	retryFunc := func(s string, code int, err error) (string, int, error) {
		if attemptsRemaining > 0 {
			return this.Post(uri, param, headParam, timeout, attemptsRemaining-1)
		}
		return s, code, err
	}
	if s, code, err := PostNativeWithHideLogOption(url, param, headParam, timeout, false, false, this.logger, this.HideLog); err == nil {
		return s, code, err
	} else {
		this.setServerDisabled(server)
		return retryFunc(s, code, err)
	}
}

// POST请求，支持application和multipart两种传输方式，默认是application
func (this *HttpClient) PostWithRetryIntervals(
	uri string,
	param, headParam map[string]interface{},
	timeout time.Duration,
	retryCnt int, retryIntervals []time.Duration,
) (s string, code int, err error) {
	if len(retryIntervals) == 0 {
		retryIntervals = []time.Duration{0}
	}
	for idx := 0; idx <= retryCnt; idx++ {
		server := this.pickServer()
		url := server + this.Prefix + uri
		if s, code, err = PostNativeWithHideLogOption(url, param, headParam, timeout, false, false, this.logger, this.HideLog); err == nil && code == http.StatusOK {
			return s, code, err
		} else {
			this.setServerDisabled(server)
			interval := retryIntervals[common.MinInt(idx, len(retryIntervals)-1)]
			time.Sleep(interval)
		}
	}
	return
}

func (this *HttpClient) SetTimeout(timeout time.Duration) {
	this.client = &http.Client{Timeout: timeout}
}

func (this *HttpClient) Do(req *http.Request) (resp *http.Response, err error) {
	if req == nil {
		return nil, errors.New("request must not be nil")
	}
	return (&HttpRequest{
		Client:  this.client,
		HideLog: this.HideLog,
	}).ExecHttpRequest(req, this.logger)
}

func (this *HttpClient) DoWithTimeout(req *http.Request, timeout time.Duration) (resp *http.Response, err error) {
	if req == nil {
		return nil, errors.New("request must not be nil")
	}
	return (&HttpRequest{
		Client:  this.client,
		HideLog: this.HideLog,
	}).ExecHttpRequest(req, this.logger)
}

func convertToKv(p map[string]interface{}) (kvs [][2]string) {
	for k, v := range p {
		if vStr, err := convertToString(v); err == nil {
			kvs = append(kvs, [2]string{k, vStr})
		} else if vMap, ok := v.(map[string]interface{}); ok { //如果还是个map，递归
			kvs = append(kvs, convertToKv(vMap)...)
		} else if vStr, err = common.JsonStringEncode(v); err == nil { //不行就json encode
			kvs = append(kvs, [2]string{k, vStr})
		}
	}
	return kvs
}

// 处理各类相关方法的请求
func (this *HttpClient) DoWithParam(uri, method string, getParam map[string]interface{}, postParam interface{}, headParam map[string]interface{},
	timeout time.Duration, attemptsRemaining int) (string, int, error) {
	server := this.pickServer()
	url := server + this.Prefix + uri
	h := NewHttpRequest(url, method, timeout)
	h.HeadParam = headParam
	h.HideLog = this.HideLog
	if getParam != nil {
		h.Url = h.Url + "?"
		kvs := convertToKv(getParam)
		for _, kv := range kvs {
			h.Url = h.Url + kv[0] + "=" + kv[1] + "&"
		}
	}

	if postParam != nil {
		paramStr := ""
		v := reflect.ValueOf(postParam)
		t := v.Type()
		switch t.Kind() {
		case reflect.String:
			temp, _ := postParam.(string)
			h.ReqBody = bytes.NewBufferString(temp)
			break
		case reflect.Map:
			paramMap, _ := postParam.(map[string]interface{})
			for k, v := range paramMap {
				vStr, convErr := convertToString(v)
				if convErr != nil {
					return "", 0, convErr
				}
				paramStr = paramStr + k + "=" + vStr + "&"
			}
			h.ReqBody = bytes.NewBufferString(paramStr)
		}
	}

	//定义重试函数
	retryFunc := func(s string, code int, err error) (string, int, error) {
		if attemptsRemaining > 0 {
			return this.DoWithParam(uri, method, getParam, postParam, headParam, timeout, attemptsRemaining-1)
		}
		return s, code, err
	}
	if err := h.Exec(method, h.Url, this.logger); err == nil {
		return h.RespBody, h.RespCode, nil
	} else {
		this.setServerDisabled(server)
		return retryFunc("", 0, err)
	}
}

func (this *HttpClient) DoWithParamJSON(uri, method string, getParam map[string]interface{}, postParam []byte, headParam map[string]interface{},
	timeout time.Duration, attemptsRemaining int) (string, int, error) {
	server := this.pickServer()
	url := server + this.Prefix + uri
	h := NewHttpRequest(url, method, timeout)
	h.HeadParam = headParam
	h.HideLog = this.HideLog
	h.SetMultipart()
	h.ContentType = JsonContentType
	if getParam != nil {
		h.Url = h.Url + "?"
		kvs := convertToKv(getParam)
		for _, kv := range kvs {
			h.Url = h.Url + kv[0] + "=" + kv[1] + "&"
		}
	}

	if postParam != nil {
		h.ReqBody = bytes.NewBuffer(postParam)
	}

	//定义重试函数
	retryFunc := func(s string, code int, err error) (string, int, error) {
		if attemptsRemaining > 0 {
			return this.DoWithParamJSON(uri, method, getParam, postParam, headParam, timeout, attemptsRemaining-1)
		}
		return s, code, err
	}
	if err := h.Exec(method, h.Url, this.logger); err == nil {
		return h.RespBody, h.RespCode, nil
	} else {
		this.setServerDisabled(server)
		return retryFunc("", 0, err)
	}
}

func (this *HttpClient) DoWithPostFile(fileField, fileName string, file io.Reader, uri string, paramMap map[string]interface{},
	timeout time.Duration, attemptsRemaining int, l *hlog.Logger) (string, int, error) {
	server := this.pickServer()
	url := server + this.Prefix + uri
	//定义重试函数
	retryFunc := func(s string, code int, err error) (string, int, error) {
		if attemptsRemaining > 0 {
			return this.DoWithPostFile(fileField, fileName, file, uri, paramMap, timeout, attemptsRemaining-1, l)
		}
		return s, code, err
	}
	if response, err := PostFileWithOutUrlencode(fileField, fileName, file, url, timeout, paramMap, l); err == nil {
		return response, 0, nil
	} else {
		this.setServerDisabled(server)
		return retryFunc("", 0, err)
	}
}

func (this *HttpClient) SetServers(newServers []string) {
	this.Servers = func(servers []string) map[string]int64 {
		srvs := make(map[string]int64)
		for _, server := range servers {
			srvs[server] = time.Now().Unix()
		}
		return srvs
	}(newServers)
}

func (this *HttpClient) pickServer() string {
	if len(this.Servers) <= 0 {
		panic("servers cannot be error")
	}
	//取得可用的servers
	tNow := time.Now().Unix()
	this.mu.RLock()
	defer this.mu.RUnlock()
	var enabledServers []string
	var disabledServers []string
	for srv, t := range this.Servers {
		if t > tNow {
			disabledServers = append(disabledServers, srv)
			continue
		}
		enabledServers = append(enabledServers, srv)
	}
	//随机获取一个server返回
	if len(enabledServers) <= 0 {
		return disabledServers[rnd.Intn(len(disabledServers))]
	}
	return enabledServers[rnd.Intn(len(enabledServers))]
}

func (this *HttpClient) setServerDisabled(server string) error {
	if _, ok := this.Servers[server]; !ok {
		return errors.New("server not exist: " + server)
	}
	this.mu.Lock()
	defer this.mu.Unlock()
	this.Servers[server] = time.Now().Unix() + this.DisabledTime
	return nil
}

// 设置DisabledTime
func (this *HttpClient) SetDisabledTime(t int64) {
	this.DisabledTime = t
}
