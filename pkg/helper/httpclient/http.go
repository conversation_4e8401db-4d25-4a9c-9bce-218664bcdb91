package httpclient

import (
	"bytes"
	"github.com/tmsong/hlog"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	URL "net/url"
	"reflect"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.docsl.com/security/bpm/pkg/common"
)

const (
	APPLICATION_JSON                  = `application/json; charset=utf-8`
	APPLICATION_X_WWW_FORM_URLENCODED = `application/x-www-form-urlencoded; charset=utf-8`
)

func GetPostWithHeader(
	url string,
	getParam map[string]interface{},
	postParam map[string]interface{},
	headParam map[string]interface{},
	timeout time.Duration,
	l *hlog.Logger,
) (string, int, error) {
	h := NewHttpRequest(url, "POST", timeout)
	h.HeadParam = headParam

	if getParam != nil {
		h.Url = h.Url + "?"
		for k, v := range getParam {
			vStr, convErr := convertToString(v)
			if convErr != nil {
				return "", 0, convErr
			}
			h.Url = h.Url + k + "=" + vStr + "&"
		}
	}

	paramStr := ""
	for k, v := range postParam {
		vStr, convErr := convertToString(v)
		if convErr != nil {
			return "", 0, convErr
		}
		paramStr = paramStr + k + "=" + vStr + "&"
	}
	h.ReqBody = bytes.NewBufferString(paramStr)

	if err := h.Exec("POST", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil

}

// get参数放到请求的url中，post则直接post
func GetPost(
	url string,
	getParam map[string]interface{},
	postParam map[string]interface{},
	timeout time.Duration,
	l *hlog.Logger,
) (string, int, error) {
	h := NewHttpRequest(url, "POST", timeout)
	if getParam != nil {
		h.Url = h.Url + "?"
		for k, v := range getParam {
			vStr, convErr := convertToString(v)
			if convErr != nil {
				return "", 0, convErr
			}
			h.Url = h.Url + k + "=" + vStr + "&"
		}
	}

	paramStr := ""
	for k, v := range postParam {
		vStr, convErr := convertToString(v)
		if convErr != nil {
			return "", 0, convErr
		}
		paramStr = paramStr + k + "=" + vStr + "&"
	}
	h.ReqBody = bytes.NewBufferString(paramStr)

	if err := h.Exec("POST", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

// 只用于下载文件
func GetFile(url string, timeout time.Duration, l *hlog.Logger) (retBody []byte, retErr error) {
	cli := http.Client{
		Timeout: timeout,
	}
	field := hlog.GetLogField(common.LogtagRequestOk)
	defer func() {
		common.PrintLogWithError(retErr, l, field, logrus.Fields{
			"tag": "GetFile",
			"url": url,
		})
	}()
	response, err := cli.Get(url)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	retBody, retErr = ioutil.ReadAll(response.Body)
	return
}

// 对外的Get请求，此处是否需要加上keepalive
func Get(url string, param map[string]interface{}, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(url, "GET", timeout)
	if param != nil {
		h.Url = h.Url + "?"
		for k, v := range param {
			vStr, convErr := convertToString(v)
			if convErr != nil {
				return "", 0, convErr
			}
			h.Url = h.Url + k + "=" + vStr + "&"
		}
	}
	if err := h.Exec("GET", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func GetWithAliasUrl(url string, aliasUrl string, param map[string]interface{}, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(url, "GET", timeout)
	if param != nil {
		h.Url = h.Url + "?"
		for k, v := range param {
			vStr, convErr := convertToString(v)
			if convErr != nil {
				return "", 0, convErr
			}
			h.Url = h.Url + k + "=" + vStr + "&"
		}
	}
	if err := h.ExecWithAliasUrl("GET", h.Url, aliasUrl, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func GetWithKeepAlive(
	url string,
	param map[string]interface{},
	timeout time.Duration,
	keepAliveTimeout time.Duration,
	l *hlog.Logger,
) (string, int, error) {
	h := NewHttpRequest(url, "GET", timeout)
	h.SetKeepAlive(keepAliveTimeout)
	if param != nil {
		h.Url = h.Url + "?"
		for k, v := range param {
			vStr, convErr := convertToString(v)
			if convErr != nil {
				return "", 0, convErr
			}
			h.Url = h.Url + k + "=" + vStr + "&"
		}
	}
	if err := h.Exec("GET", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func GetWithoutKeepAlive(url string, param map[string]interface{}, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(url, "GET", timeout)
	h.DisableKeepAlive()
	if param != nil {
		h.Url = h.Url + "?"
		for k, v := range param {
			vStr, convErr := convertToString(v)
			if convErr != nil {
				return "", 0, convErr
			}
			h.Url = h.Url + k + "=" + vStr + "&"
		}
	}
	if err := h.Exec("GET", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func GetWithHeader(url string, param map[string]interface{}, headParam map[string]interface{}, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(url, "GET", timeout)
	h.HeadParam = headParam
	if param != nil {
		h.Url = h.Url + "?"
		for k, v := range param {
			vStr, convErr := convertToString(v)
			if convErr != nil {
				return "", 0, convErr
			}
			h.Url = h.Url + k + "=" + vStr + "&"
		}
	}
	if err := h.Exec("GET", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func PostFileWithOutUrlencode(
	fileField, fileName string,
	file io.Reader,
	url string,
	timeout time.Duration,
	paramMap map[string]interface{},
	l *hlog.Logger,
) (retBody string, retErr error) {
	bodyBuf := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuf)
	fileWriter, err := bodyWriter.CreateFormFile(fileField, fileName)
	if err != nil {
		return "", err
	}
	_, err = io.Copy(fileWriter, file)
	if err != nil {
		return "", err
	}
	for k, v := range paramMap {
		vStr, convErr := convertToStringWithOutUrlencode(v)
		if convErr != nil {
			return "", convErr
		}
		bodyWriter.WriteField(k, vStr)
	}
	bodyWriter.Close()
	req, err := http.NewRequest("POST", url, bodyBuf)
	u, _ := URL.Parse(req.URL.Path)
	l.AddHttpTrace(req)
	req.Header.Set("Content-Type", bodyWriter.FormDataContentType())
	var proc float64 = 0
	startTime := time.Now()
	defer func() {
		common.PrintLogWithError(retErr, l, logrus.Fields{
			"api":       u.Path,
			"tag":       "PostFile",
			"header":    req.Header,
			"url":       url,
			"post":      paramMap,
			"out":       retBody,
			"proc_time": proc,
		})
	}()
	client := &http.Client{
		Timeout: timeout,
	}
	// fmt.Printf("body:%+v req:%+v", bodyBuf, req)
	response, err := client.Do(req)
	proc = float64(time.Now().Sub(startTime).Nanoseconds()) / 1000000.0
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	retByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return "", err
	}
	retBody = string(retByte)
	return
}

func PostFile(
	fileField, fileName string,
	file io.Reader,
	url string,
	timeout time.Duration,
	paramMap map[string]interface{},
	l *hlog.Logger,
) (retBody string, retErr error) {
	bodyBuf := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuf)
	fileWriter, err := bodyWriter.CreateFormFile(fileField, fileName)
	defer func() {
		common.PrintLogWithError(retErr, l, logrus.Fields{
			"tag":     "PostFile",
			"url":     url,
			"post":    paramMap,
			"out":     retBody,
			"timeout": timeout,
		})
	}()
	if err != nil {
		return "", err
	}
	_, err = io.Copy(fileWriter, file)
	if err != nil {
		return "", err
	}
	for k, v := range paramMap {
		vStr, convErr := convertToString(v)
		if convErr != nil {
			return "", convErr
		}
		bodyWriter.WriteField(k, vStr)
	}
	bodyWriter.Close()
	req, err := http.NewRequest("POST", url, bodyBuf)
	req.Header.Set("Content-Type", bodyWriter.FormDataContentType())
	client := &http.Client{
		Timeout: timeout,
	}
	// fmt.Printf("body:%+v req:%+v", bodyBuf, req)
	response, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	retByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return "", err
	}
	retBody = string(retByte)
	return
}

func PostFileWithAliasUrl(fileField, fileName string, file io.Reader, url string, aliasUrl string, timeout time.Duration,
	paramMap map[string]interface{}, l *hlog.Logger,
) (retBody string, retErr error) {
	bodyBuf := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuf)
	fileWriter, err := bodyWriter.CreateFormFile(fileField, fileName)
	defer func() {
		u, _ := URL.Parse(url)
		var apiLogValue string = u.Path
		if aliasUrl != "" {
			apiLogValue = aliasUrl
		}
		common.PrintLogWithError(retErr, l, logrus.Fields{
			"tag":     "PostFile",
			"api":     apiLogValue,
			"url":     url,
			"post":    paramMap,
			"out":     retBody,
			"timeout": timeout,
		})
	}()
	if err != nil {
		return "", err
	}
	_, err = io.Copy(fileWriter, file)
	if err != nil {
		return "", err
	}
	for k, v := range paramMap {
		vStr, convErr := convertToString(v)
		if convErr != nil {
			return "", convErr
		}
		bodyWriter.WriteField(k, vStr)
	}
	bodyWriter.Close()
	req, err := http.NewRequest("POST", url, bodyBuf)
	req.Header.Set("Content-Type", bodyWriter.FormDataContentType())
	client := &http.Client{
		Timeout: timeout,
	}
	// fmt.Printf("body:%+v req:%+v", bodyBuf, req)
	response, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()
	retByte, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return "", err
	}
	retBody = string(retByte)
	return
}

// POST请求，支持application和multipart两种传输方式，默认是application，P.S:支持隐藏日志
func PostNativeWithHideLogOption(url string, param interface{}, headParam map[string]interface{}, timeout time.Duration, IsMultipart bool, IsHttps bool, l *hlog.Logger, hideLog bool) (string, int, error) {
	h := NewHttpRequest(url, "POST", timeout)

	//设置请求的head
	h.HeadParam = headParam

	//设置是否隐藏日志
	h.HideLog = hideLog

	//是否是https请求
	h.IsHttps = IsHttps

	//设置Post参数
	if param != nil {
		v := reflect.ValueOf(param)
		t := v.Type()
		switch t.Kind() {
		case reflect.String:
			temp, _ := param.(string)
			h.ReqBody = bytes.NewBufferString(temp)
			break
		case reflect.Map:
			paramMap, _ := param.(map[string]interface{})
			if IsMultipart {
				h.ReqBody = new(bytes.Buffer)
				w := multipart.NewWriter(h.ReqBody)
				for k, v := range paramMap {
					vStr, convErr := convertToString(v)
					if convErr != nil {
						return "", 0, convErr
					}
					w.WriteField(k, vStr)
				}
				w.Close()
				h.IsMultipart = IsMultipart
				h.ContentType = w.FormDataContentType()
			} else {
				paramStr := ""
				for k, v := range paramMap {
					vStr, convErr := convertToString(v)
					if convErr != nil {
						return "", 0, convErr
					}
					paramStr = paramStr + k + "=" + vStr + "&"
				}
				h.ReqBody = bytes.NewBufferString(paramStr)
			}
			break
		}
	}

	if err := h.Exec("POST", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func Post(url string, param interface{}, headParam map[string]interface{}, timeout time.Duration, IsMultipart bool, l *hlog.Logger) (string, int, error) {
	return PostNativeWithHideLogOption(url, param, headParam, timeout, IsMultipart, false, l, false)
}

func PostHttps(url string, param interface{}, headParam map[string]interface{}, timeout time.Duration, IsMultipart bool, l *hlog.Logger) (string, int, error) {
	return PostNativeWithHideLogOption(url, param, headParam, timeout, IsMultipart, true, l, false)
}

// PUT请求，数据格式string
func Put(url string, param string, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(url, "PUT", timeout)
	h.ReqBody = bytes.NewBufferString(param)
	if err := h.Exec("PUT", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func PutWithHeader(uri string, param string, headParam map[string]interface{}, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(uri, "PUT", timeout)
	h.HeadParam = headParam
	h.ReqBody = bytes.NewBufferString(param)
	if err := h.Exec("PUT", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

// HEAD请求，数据格式string
func Head(url string, param string, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(url, "HEAD", timeout)
	h.ReqBody = bytes.NewBufferString(param)
	if err := h.Exec("HEAD", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

// DELETE请求，数据格式string
func Delete(url string, param string, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(url, "DELETE", timeout)
	h.ReqBody = bytes.NewBufferString(param)
	if err := h.Exec("DELETE", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func DeleteWithAliasUrl(url string, aliasUrl string, param string, timeout time.Duration, l *hlog.Logger) (string, int, error) {
	h := NewHttpRequest(url, "DELETE", timeout)
	h.ReqBody = bytes.NewBufferString(param)
	if err := h.ExecWithAliasUrl("DELETE", h.Url, aliasUrl, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}

func DeleteWithHeader(uri string, param string, headParam map[string]interface{}, timeout time.Duration, l *hlog.Logger) (
	string, int, error) {
	h := NewHttpRequest(uri, "DELETE", timeout)
	h.HeadParam = headParam
	h.ReqBody = bytes.NewBufferString(param)
	if err := h.Exec("DELETE", h.Url, l); err != nil {
		return "", 0, err
	}
	return h.RespBody, h.RespCode, nil
}
