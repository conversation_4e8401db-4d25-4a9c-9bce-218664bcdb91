package httpclient

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"sync"
	"time"

	"gitlab.docsl.com/security/bpm/pkg/common"

	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
)

func init() {

}

// TODO 默认的content-type，是否可以不用设置？
const (
	DefaultContentType = "application/x-www-form-urlencoded"
	JsonContentType    = "application/json"
)

// 暂时不支持cookie
type HttpRequest struct {
	Method       string
	Url          string
	ContentType  string
	Keeplive     bool
	Timeout      time.Duration
	Client       *http.Client
	Param        interface{}
	HeadParam    map[string]interface{}
	ReqBody      *bytes.Buffer
	RespCode     int
	RespBody     string
	IsMultipart  bool
	IsHttps      bool
	sync.RWMutex      // TODO 此处是否需要加锁？
	HideLog      bool //是否不打印日志
}

type Result struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 设置请求方法，默认是GET
func (this *HttpRequest) SetMethod(method string) {
	this.Method = method
}

// 设置POST请求的内容请求方式
func (this *HttpRequest) SetMultipart() {
	this.IsMultipart = true
}

// 设置keepalive，需要重新设置transport
func (this *HttpRequest) SetKeepAlive(timeout time.Duration) {
	if this.Keeplive {
		return
	}
	this.Keeplive = true
	this.Client.Transport = &http.Transport{
		Dial: func(netw, addr string) (net.Conn, error) {
			deadline := time.Now().Add(timeout)
			c, err := net.DialTimeout(netw, addr, timeout)
			if err != nil {
				return nil, err
			}
			c.SetDeadline(deadline)
			return c, nil
		},
		MaxIdleConnsPerHost: 20,
		DisableKeepAlives:   false,
	}
}

func (this *HttpRequest) DisableKeepAlive() {
	this.Keeplive = false
	this.Client.Transport = &http.Transport{
		DisableKeepAlives: true,
	}
}

// 默认只能设置三个参数
func NewHttpRequest(rawUrl string, method string, timeout time.Duration) *HttpRequest {
	client := &http.Client{
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   timeout,
				KeepAlive: 5000 * time.Millisecond,
				DualStack: true,
			}).DialContext,
			MaxIdleConns:        1000,
			MaxIdleConnsPerHost: 100, // default is 100
			TLSHandshakeTimeout: timeout,
		},
		Timeout: timeout,
	}
	isHttps := false
	URL, err := url.Parse(rawUrl)
	if err == nil && URL.Scheme == "https" {
		isHttps = true
	}
	return &HttpRequest{
		Url:      rawUrl,
		Method:   method,
		Timeout:  timeout,
		Client:   client,
		Keeplive: false,
		IsHttps:  isHttps,
		ReqBody:  new(bytes.Buffer),
	}
}

func (this *HttpRequest) TryExec(l *hlog.Logger, attemptsRemaining int) (err error) {
	//定义重试函数
	retry := func(err error) error {
		if attemptsRemaining > 0 {
			return this.TryExec(l, attemptsRemaining-1)
		}
		return err
	}
	if err = this.Exec(this.Method, this.Url, l); err == nil {
		return nil
	}
	return retry(err)
}

func (this *HttpRequest) ExecHttpRequest(request *http.Request, l *hlog.Logger) (response *http.Response, err error) {
	field := hlog.GetLogField(common.LogtagRequestOk)
	l.AddHttpTrace(request)
	reqBody := common.StringEmpty
	if request.Body != nil {
		if bodyBytes, err := ioutil.ReadAll(request.Body); err != nil {
			return nil, err
		} else {
			request.Body = ioutil.NopCloser(bytes.NewReader(bodyBytes))
			reqBody = string(bodyBytes)
		}
	}
	defer func() {
		var result Result
		oErr := err
		out := common.TrimHTML(this.RespBody)
		if this.HideLog {
			out = "hidden"
		}
		f := logrus.Fields{
			"api":    request.URL.Path,
			"url":    request.URL.String(),
			"out":    out,
			"get":    request.URL.Query(),
			"post":   reqBody,
			"header": request.Header,
			"method": request.Method,
			"code":   this.RespCode,
		}
		if err == nil && this.RespCode == http.StatusOK {
			if e := json.Unmarshal([]byte(this.RespBody), &result); e == nil {
				errno := result.Code
				if result.Code == http.StatusOK { //兼容解决
					errno = common.ERR_OK
				}
				err = common.CommonError(errno, result.Message)
			}
		}
		common.PrintLogWithError(err, l, field, f)
		err = oErr
	}()
	if err != nil {
		return nil, err
	}
	// set https
	if this.IsHttps {
		this.Client.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
	}

	if this.Keeplive {
		request.Header.Set("Connection", "Keep-Alive")
	} else {
		request.Close = true
	}
	if request.Method == "POST" {
		if this.IsMultipart {
			request.Header.Set("Content-Type", this.ContentType)
		} else {
			//request.Header.Set("Content-Type", DefaultContentType)
		}
	}
	if ua := request.Header.Get("User-Agent"); ua == common.StringEmpty {
		request.Header.Set("User-Agent", "huobi-hbpm")
	} else {
		request.Header.Set("User-Agent", fmt.Sprintf("%s huobi-hbpm", ua))
	}
	response, err = this.Client.Do(request)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	this.RespCode = response.StatusCode
	if bodyBytes, err := ioutil.ReadAll(response.Body); err != nil {
		return nil, err
	} else {
		response.Body = ioutil.NopCloser(bytes.NewReader(bodyBytes))
		this.RespBody = string(bodyBytes)
	}
	return response, nil
}

// HTTP请求的具体执行方法
func (this *HttpRequest) Exec(method string, RealUrl string, l *hlog.Logger) (err error) {
	field := hlog.GetLogField(common.LogtagRequestOk)
	body := this.ReqBody.String()
	request, err := http.NewRequest(method, RealUrl, this.ReqBody)
	if err != nil {
		return err
	}
	if ua := request.Header.Get("User-Agent"); ua == common.StringEmpty {
		request.Header.Set("User-Agent", "huobi-hbpm")
	} else {
		request.Header.Set("User-Agent", fmt.Sprintf("%s huobi-hbpm", ua))
	}
	l.AddHttpTrace(request)
	defer func() {
		var result Result
		oErr := err
		u, _ := url.Parse(RealUrl)
		get, _ := url.ParseQuery(u.RawQuery)
		post, uErr := url.QueryUnescape(body)
		if uErr != nil {
			post = body
		}
		out := this.RespBody
		if this.HideLog {
			out = "hidden"
		}
		f := logrus.Fields{
			"api":    u.Path,
			"url":    RealUrl,
			"out":    out,
			"get":    get,
			"post":   post,
			"header": request.Header,
			"method": method,
			"code":   this.RespCode,
		}
		if err == nil && this.RespCode == http.StatusOK {
			if e := json.Unmarshal([]byte(this.RespBody), &result); e == nil {
				errno := result.Code
				if result.Code == http.StatusOK { //兼容解决
					errno = common.ERR_OK
				}
				err = common.CommonError(errno, result.Message)
			}
		}
		common.PrintLogWithError(err, l, field, f)
		err = oErr
	}()
	if err != nil {
		return err
	}

	// set https
	if this.IsHttps {
		this.Client.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
	}

	if this.Keeplive {
		request.Header.Set("Connection", "Keep-Alive")
	} else {
		request.Close = true
	}
	if method == "POST" {
		if this.IsMultipart {
			request.Header.Set("Content-Type", this.ContentType)
		} else {
			request.Header.Set("Content-Type", DefaultContentType)
		}
	}

	// set header
	if this.HeadParam != nil {
		for k, v := range this.HeadParam {
			value := fmt.Sprintf("%v", v)
			request.Header.Set(k, value)
			if http.CanonicalHeaderKey(k) == "Host" {
				request.Host = value
			}
		}
	}

	response, err := this.Client.Do(request)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	this.RespCode = response.StatusCode
	contents, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return err
	}
	this.RespBody = string(contents)
	return nil
}

// HTTP请求时增加AliasUrl的处理
func (this *HttpRequest) ExecWithAliasUrl(method string, RealUrl string, AliasUrl string, l *hlog.Logger) (err error) {
	field := hlog.GetLogField(common.LogtagRequestOk)
	body := this.ReqBody.String()
	request, err := http.NewRequest(method, RealUrl, this.ReqBody)
	if err != nil {
		return err
	}
	l.AddHttpTrace(request)
	defer func() {
		var result Result
		oErr := err
		u, _ := url.Parse(RealUrl)
		get, _ := url.ParseQuery(u.RawQuery)
		post, _ := url.QueryUnescape(body)
		out := this.RespBody
		if this.HideLog {
			out = "hidden"
		}
		f := logrus.Fields{
			"api":    u.Path,
			"url":    RealUrl,
			"out":    out,
			"get":    get,
			"post":   post,
			"header": request.Header,
			"method": method,
			"code":   this.RespCode,
		}

		// 如果别名url存在,就修改api日志
		if AliasUrl != "" {
			f["api"] = AliasUrl
		}
		if err == nil && this.RespCode == http.StatusOK {
			if e := json.Unmarshal([]byte(this.RespBody), &result); e == nil {
				errno := result.Code
				if result.Code == http.StatusOK { //兼容解决
					errno = common.ERR_OK
				}
				err = common.CommonError(errno, result.Message)
			}
		}
		common.PrintLogWithError(err, l, field, f)
		err = oErr
	}()
	if err != nil {
		return err
	}

	// set https
	if this.IsHttps {
		this.Client.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
	}

	if this.Keeplive {
		request.Header.Set("Connection", "Keep-Alive")
	} else {
		request.Close = true
	}
	if method == "POST" {
		if this.IsMultipart {
			request.Header.Set("Content-Type", this.ContentType)
		} else {
			request.Header.Set("Content-Type", DefaultContentType)
		}
	}

	// set header
	if this.HeadParam != nil {
		for k, v := range this.HeadParam {
			value := fmt.Sprintf("%v", v)
			request.Header.Set(k, value)
		}
	}

	response, err := this.Client.Do(request)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	this.RespCode = response.StatusCode
	contents, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return err
	}
	this.RespBody = string(contents)
	return nil
}

func convertToString(d interface{}) (v string, err error) {
	f := reflect.ValueOf(d)
	switch f.Interface().(type) {
	case int, int8, int16, int32, int64:
		v = strconv.FormatInt(f.Int(), 10)
	case uint, uint8, uint16, uint32, uint64:
		v = strconv.FormatUint(f.Uint(), 10)
	case float32:
		v = fmt.Sprintf("%v", d)
	case float64:
		v = fmt.Sprintf("%v", d)
	case []byte:
		v = string(f.Bytes())
	case string:
		v = url.QueryEscape(f.String())
	default:
		err = errors.New("Unsupport data type in http. Only support primitives")
	}

	return
}

func convertToStringWithOutUrlencode(d interface{}) (v string, err error) {
	f := reflect.ValueOf(d)
	switch f.Interface().(type) {
	case int, int8, int16, int32, int64:
		v = strconv.FormatInt(f.Int(), 10)
	case uint, uint8, uint16, uint32, uint64:
		v = strconv.FormatUint(f.Uint(), 10)
	case float32:
		v = fmt.Sprintf("%v", d)
	case float64:
		v = fmt.Sprintf("%v", d)
	case []byte:
		v = string(f.Bytes())
	case string:
		v = f.String()
	default:
		err = errors.New("Unsupport data type in http. Only support primitives")
	}

	return
}

func (this *HttpRequest) String() string {
	return fmt.Sprintf(
		"URL:[%v] Header:[%v] Method:[%v] Timeout:[%v] ReqBody:[%v] RespBody:[%v]",
		this.Url,
		this.HeadParam,
		this.Method,
		this.Timeout,
		this.ReqBody,
		this.RespBody,
	)
}
