/**
 * @note
 *
 * @author: libi
 * @date: 2019/12/20
 */

package common

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"time"
)

/**
 * 直接对请求加签
 * 参考加密算法 "http://phabricator.huobidev.com/w/dawn/standard/api/"
 *
 */
func SignReq(req *http.Request, appId, appKey string) (*http.Request, error) {
	values := req.URL.Query()
	values.Set("AWSAccessKeyId", appId)
	values.Set("SignatureVersion", "2") //遵循AWS V2签名规范
	values.Set("SignatureMethod", "HmacSHA256")
	values.Set("Timestamp", time.Now().UTC().Format("2006-01-02T15:04:05"))
	req.URL.RawQuery = values.Encode()

	s := fmt.Sprintf("%s\n%s\n%s\n%s", req.Method, req.URL.Host, req.URL.Path, req.URL.RawQuery)
	h := hmac.New(sha256.New, []byte(appKey))
	_, err := h.Write([]byte(s))
	if err != nil {
		return nil, err
	}
	sig := base64.StdEncoding.EncodeToString(h.Sum(nil))

	values = req.URL.Query()
	values.Set("Signature", sig)
	req.URL.RawQuery = values.Encode()
	req.Header.Set("Content-Type", "application/json")
	return req, nil
}

/**
 * 对请求进行加签,返回加签后的map,可以放在DoWithParamJSON方法中
 *
 *@param method 请求的类型GET/POST
 *@param host   请求的host
 *@param path   请求的路径，uri
 *@param appId  第三方系统的appId
 *@param appKey 第三方系统的appKey
 *
 *
 *@return map 对请求加签后的map
 *@return err 发生的错误
 *
 */
func GetUrlSignMap(method, host, path, appId, appKey string) (map[string]interface{}, error) {
	u := &url.URL{}
	values := u.Query()
	values.Set("AWSAccessKeyId", appId)
	values.Set("SignatureVersion", "2") //遵循AWS V2签名规范
	values.Set("SignatureMethod", "HmacSHA256")
	values.Set("Timestamp", time.Now().UTC().Format("2006-01-02T15:04:05"))
	u.RawQuery = values.Encode()
	s := fmt.Sprintf("%s\n%s\n%s\n%s", method, host, path, u.RawQuery)
	h := hmac.New(sha256.New, []byte(appKey))
	_, err := h.Write([]byte(s))
	if err != nil {
		return nil, err
	}
	sig := base64.StdEncoding.EncodeToString(h.Sum(nil))

	values = u.Query()
	values.Set("Signature", sig)
	res := make(map[string]interface{})
	for key, val := range values {
		res[key] = val[0]
	}
	return res, nil
}

func GetSignature(
	Method,
	Host,
	Path,
	AWSAccessKeyId,
	SignatureVersion,
	SignatureMethod,
	Timestamp,
	appKey string) (string, error) {

	if IsProd() {
		//生产环境 如果host为ip，即为通过nginx rewrite过来的，需要加上
		if Host == "hbpm.prd8.apne-1c.huobiapps.com" {
			Host = "**************"
		}

		if Host == "**************" {
			Path = "/bpm" + Path
		}
	}

	u := &url.URL{}
	values := u.Query()
	values.Set("AWSAccessKeyId", AWSAccessKeyId)
	values.Set("SignatureVersion", SignatureVersion)
	values.Set("SignatureMethod", SignatureMethod)
	values.Set("Timestamp", Timestamp)
	u.RawQuery = values.Encode()
	s := fmt.Sprintf("%s\n%s\n%s\n%s", Method, Host, Path, u.RawQuery)
	h := hmac.New(sha256.New, []byte(appKey))
	_, err := h.Write([]byte(s))
	if err != nil {
		return StringEmpty, err
	}
	sig := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return sig, nil
}
