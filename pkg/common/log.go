/**
 * @note
 * 使用logger格式化打印日志
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package common

import (
	"github.com/sirupsen/logrus"
	"github.com/tmsong/hlog"
)

/**
 * @note
 * 根据error来解决打印日志的方式，并且打找下日志
 * @param error err 错误
 * @param *hlog.Logger 日志句柄
 * @param logrus.Fields fields 指定的fields, 用于叠加
 *
 */
func PrintLogWithError(err error, l *hlog.Logger, fields ...logrus.Fields) {
	var log interface{}
	var code interface{}
	var tag interface{}
	f := logrus.Fields{}
	if len(fields) >= 1 {
		if item, ok := fields[0][LogcodeName]; ok {
			code = item
		}
		if item, ok := fields[0][LogTag]; ok {
			tag = item
		}
		e := l.WithFields(fields[0])
		for i := 1; i < len(fields); i++ {
			if item, ok := fields[i][LogcodeName]; ok {
				code = item
			}
			if item, ok := fields[i][LogTag]; ok {
				tag = item
			}
			e = e.WithFields(fields[i])
		}
		log = e
	} else {
		log = l
	}
	if code != nil {
		f = logrus.Fields{
			LogcodeName: code,
		}
	} else {
		f = logrus.Fields{
			LogcodeName: ERR_OK,
		}
	}
	if tag != nil {
		f[LogTag] = tag
	}
	if e, ok := err.(*Error); ok && e != nil && e.Code != ERR_OK && e.Code != ERR_SYS && e.Code != ERR_UNKNOWN {
		f[LogcodeName] = ERR_UNKNOWN
		if ce, ok := err.(*Error); ok {
			f[LogcodeName] = ce.Code
		}
		//更换tag
		if v, ok := f[LogTag]; ok {
			if _, ok := TagDescSuccMapErr[v.(string)]; ok {
				f[LogTag] = TagDescSuccMapErr[v.(string)]
			}
		}
		if _, ok := log.(*logrus.Entry); ok {
			log.(*logrus.Entry).WithFields(f).Warningln(err.(error).Error())
		} else {
			log.(*hlog.Logger).WithFields(f).Warningln(err.(error).Error())
		}
	} else if e, ok := err.(*Error); ok && e != nil && (e.Code == ERR_SYS || e.Code == ERR_UNKNOWN) {
		f[LogcodeName] = ERR_UNKNOWN
		if ce, ok := err.(*Error); ok {
			f[LogcodeName] = ce.Code
		}
		//更换tag
		if v, ok := f[LogTag]; ok {
			if _, ok := TagDescSuccMapErr[v.(string)]; ok {
				f[LogTag] = TagDescSuccMapErr[v.(string)]
			}
		}
		if _, ok := log.(*logrus.Entry); ok {
			log.(*logrus.Entry).WithFields(f).Errorln(err.(error).Error())
		} else {
			log.(*hlog.Logger).WithFields(f).Errorln(err.(error).Error())
		}
	} else if _, ok := err.(*Error); !ok && err != nil {
		//如果没有errno,增加UNKNOWN错误码
		if _, ok := f[LogcodeName]; !ok {
			f[LogcodeName] = ERR_UNKNOWN
		}
		//更换tag
		if v, ok := f[LogTag]; ok {
			if _, ok := TagDescSuccMapErr[v.(string)]; ok {
				f[LogTag] = TagDescSuccMapErr[v.(string)]
			}
		}
		if _, ok := log.(*logrus.Entry); ok {
			log.(*logrus.Entry).WithFields(f).Errorln(err.(error).Error())
		} else {
			log.(*hlog.Logger).WithFields(f).Errorln(err.(error).Error())
		}
	} else {
		if _, ok := log.(*logrus.Entry); ok {
			log.(*logrus.Entry).WithFields(f).Infoln(LogOk)
		} else {
			log.(*hlog.Logger).WithFields(f).Infoln(LogOk)
		}
	}
}
