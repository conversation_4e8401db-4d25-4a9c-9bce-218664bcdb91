/**
 * @note
 * 一些工具函数
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package common

import (
	"bytes"
	"encoding/gob"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"gopkg.in/bufio.v1"
)

var rnd *rand.Rand
var mu sync.Mutex
var phoneReg = regexp.MustCompile(`(\d{3})\d{4}(\d{4})`)

const (
	BYTE     = 1.0
	KILOBYTE = 1024 * BYTE
	MEGABYTE = 1024 * KILOBYTE
	GIGABYTE = 1024 * MEGABYTE
	TERABYTE = 1024 * GIGABYTE
)

const Binary = "1.0"

func Version(app string) string {
	return fmt.Sprintf("%s v%s (built w/%s)", app, Binary, runtime.Version())
}

func DefaultUserAgent() string {
	return Version("huobi-portal-proxy")
}

func init() {
	rnd = rand.New(rand.NewSource(time.Now().UnixNano()))
}

func MinInt64(x, y int64) int64 {
	if x < y {
		return x
	}
	return y
}

func MaxInt64(x, y int64) int64 {
	if x > y {
		return x
	}
	return y
}

func MaxInt(x, y int) int {
	if x > y {
		return x
	}
	return y
}

func MinInt(x, y int) int {
	if x < y {
		return x
	}
	return y
}

func JsonDecode(data []byte, obj interface{}) error {
	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.UseNumber()
	return decoder.Decode(obj)
}

func JsonStringDecode(data string, obj interface{}) error {
	decoder := json.NewDecoder(bytes.NewBufferString(data))
	decoder.UseNumber()
	return decoder.Decode(obj)
}

func JsonStringToRaw(data string) (json.RawMessage, error) {
	ret := json.RawMessage{}
	if err := ret.UnmarshalJSON([]byte(data)); err != nil {
		return ret, err
	}
	return ret, nil
}

func JsonEncode(obj interface{}) ([]byte, error) {
	buf := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buf)
	err := encoder.Encode(obj)
	s := strings.Trim(string(buf.Bytes()), "\n")
	return []byte(s), err
}

func JsonEncodeNoEscape(obj interface{}) ([]byte, error) {
	buf := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buf)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(obj)
	s := strings.Trim(string(buf.Bytes()), "\n")
	return []byte(s), err
}

func JsonStringEncode(obj interface{}) (string, error) {
	buf := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buf)
	err := encoder.Encode(obj)
	s := strings.Trim(string(buf.Bytes()), "\n")
	return s, err
}

func JsonStringEncodeNoEscape(obj interface{}) (string, error) {
	buf := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buf)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(obj)
	s := strings.Trim(string(buf.Bytes()), "\n")
	return s, err
}

func GobEncode(src interface{}) ([]byte, error) {
	buf := bytes.NewBuffer(nil)
	encoder := gob.NewEncoder(buf)
	err := encoder.Encode(src)
	return buf.Bytes(), err
}

func GobDecode(data []byte, obj interface{}) error {
	buf := bytes.NewBuffer(data)
	decoder := gob.NewDecoder(buf)
	return decoder.Decode(obj)
}

func IdentifyPanic() string {
	var name string
	var file string
	var line int
	var pc [16]uintptr
	var res = []map[string]string{}
	prefix := ModuleName

	n := runtime.Callers(3, pc[:])
	i := 0
	for _, pc := range pc[:n] {
		fn := runtime.FuncForPC(pc)
		if fn == nil {
			continue
		}
		file, line = fn.FileLine(pc)
		file = file[MaxInt(0, strings.Index(file, prefix)):]
		name = strings.TrimPrefix(fn.Name(), prefix)
		res = append(res, map[string]string{
			"func": name,
			"line": fmt.Sprintf("%s:%d", file, line),
		})
		if true == strings.Contains(name, ModuleName) {
			if i >= 10 && line > 1 {
				break
			}
			i++
		}
	}
	resBytes, _ := JsonEncode(res)
	return string(resBytes)
}

/**
 * @note
 * 得到长度为lenNum的随机字符串 （数字）
 * @param int lenNum 数字串的长度
 *
 * @return string
 */
func RandNumString(lenNum int) string {
	str := "1234567890"
	ret := make([]byte, lenNum)
	length := len(str)
	for i := 0; i < lenNum; i++ {
		mu.Lock()
		pos := rnd.Intn(length)
		mu.Unlock()
		ret[i] = str[pos]
	}
	return string(ret)
}

/**
 * @note
 * 得到长度为lenNum的随机数字与大小写字母的组合
 * @param int lenNum 字符串的长度
 *
 * @return string
 */
func RandAuthStr(lenNum int) string {
	str := "1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	ret := make([]byte, lenNum)
	length := len(str)
	for i := 0; i < lenNum; i++ {
		mu.Lock()
		pos := rnd.Intn(length)
		mu.Unlock()
		ret[i] = str[pos]
	}
	return string(ret)
}

/**
 * @note
 * 获取随机长数字串id
 *
 * @return int64 id
 */
func GenerateId() int64 {
	i, _ := strconv.ParseInt(RandNumString(10), 10, 0)
	return i
}

/**
 * @note
 * 好看的json序列化，用于通知类
 *
 * @return int64 id
 */
func ToPrettyJsonString(src interface{}) (string, error) {
	out, err := json.MarshalIndent(src, "", "    ")
	return string(out), err
}

func JsonToMap(b string) (map[string]interface{}, error) {
	var obj interface{}
	decoder := json.NewDecoder(bytes.NewBufferString(b))
	decoder.UseNumber()
	decoder.Decode(&obj)
	if obj == nil {
		return map[string]interface{}{}, errors.New("not json")
	}
	m := obj.(map[string]interface{})
	return m, nil
}

func Milliseconds(t time.Duration) int64 {
	return t.Nanoseconds() / 1e6
}

func TrimHTML(html string) string {
	return strings.Join(strings.FieldsFunc(strings.Replace(html, "\\n", "", -1), func(r rune) bool {
		switch r {
		case '\t', '\n', '\v', '\f', '\r', 0x85, 0xA0, '\\':
			return true
		}
		return false
	}), "")
}

func InterfaceToStruct(i interface{}, v interface{}) (err error) {
	buf := bufio.NewBuffer([]byte{})
	encoder := json.NewEncoder(buf)
	if err = encoder.Encode(i); err != nil {
		return err
	}
	decoder := json.NewDecoder(buf)
	decoder.UseNumber()
	return decoder.Decode(v)
}

func MapToStruct(m map[string]interface{}, v interface{}) (err error) {
	buf := bufio.NewBuffer([]byte{})
	encoder := json.NewEncoder(buf)
	if err = encoder.Encode(m); err != nil {
		return err
	}
	decoder := json.NewDecoder(buf)
	decoder.UseNumber()
	return decoder.Decode(v)
}

func IsNil(src interface{}) bool {
	if src == nil {
		return true
	}
	switch reflect.TypeOf(src).Kind() {
	case reflect.Ptr, reflect.Map, reflect.Interface:
		if reflect.ValueOf(src).IsNil() {
			return true
		}
	case reflect.Slice, reflect.Array:
		if reflect.ValueOf(src).IsNil() {
			return true
		}
	}
	return false
}

func Union(slice1, slice2 []int64) []int64 {
	m := make(map[int64]int)
	for _, v := range slice1 {
		m[v]++
	}

	for _, v := range slice2 {
		times, _ := m[v]
		if times == 0 {
			slice1 = append(slice1, v)
		}
	}
	return slice1
}

// 求交集
func Intersect(slice1, slice2 []int64) []int64 {
	m := make(map[int64]int)
	nn := make([]int64, 0)
	for _, v := range slice1 {
		m[v]++
	}

	for _, v := range slice2 {
		times, _ := m[v]
		if times == 1 {
			nn = append(nn, v)
		}
	}
	return nn
}

// 求差集 slice1-并集
func Difference(slice1, slice2 []int64) []int64 {
	m := make(map[int64]int)
	nn := make([]int64, 0)
	inter := Intersect(slice1, slice2)
	for _, v := range inter {
		m[v]++
	}

	for _, value := range slice1 {
		times, _ := m[value]
		if times == 0 {
			nn = append(nn, value)
		}
	}
	return nn
}
