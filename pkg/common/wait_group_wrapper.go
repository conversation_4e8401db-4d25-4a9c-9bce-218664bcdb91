/**
 * @note
 * WaitGroup封装函数
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package common

import (
	"log"
	"reflect"
	"sync"
)

type WaitGroupWrapper struct {
	sync.WaitGroup
}

func (w *WaitGroupWrapper) Wrap(cb func()) {
	w.Add(1)
	go func() {
		defer w.Done()
		defer func() {
			if err := recover(); err != nil {
				trace := IdentifyPanic()
				log.Printf("go routine run error: %+v, trace: %+v\n", err, trace)
			}
		}()
		cb()
	}()
}

func (w *WaitGroupWrapper) WrapFunc(fn interface{}, args ...interface{}) {
	vFn := reflect.ValueOf(fn)
	if vFn.Kind() == reflect.Func {
		w.Add(1)
		var vArgs []reflect.Value = nil
		if len(args) > 0 {
			vArgs = make([]reflect.Value, len(args))
			for idx, arg := range args {
				vArgs[idx] = reflect.ValueOf(arg)
			}
		}
		go func(fn reflect.Value, args []reflect.Value) {
			defer w.Done()
			defer func() {
				if err := recover(); err != nil {
					trace := IdentifyPanic()
					log.Printf("go routine run error: %+v, trace: %+v\n", err, trace)
					return
				}
			}()
			fn.Call(args)
		}(vFn, vArgs)
	}
}
