/**
 * @note
 * atomic bool实现
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package atomic

import "sync/atomic"

type Bool int32

func NewBool() *Bool {
	return new(Bool)
}

func (this *Bool) IsTrue() bool {
	return atomic.LoadInt32((*int32)(this)) == 1
}

func (this *Bool) IsFalse() bool {
	return atomic.LoadInt32((*int32)(this)) == 0
}

func (this *Bool) SetTo(yes bool) {
	if yes {
		atomic.StoreInt32((*int32)(this), 1)
	} else {
		atomic.StoreInt32((*int32)(this), 0)
	}
}
