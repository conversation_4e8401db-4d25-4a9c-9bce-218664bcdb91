/**
 * @note
 * hash set实现
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package sets

import (
	"fmt"
	"reflect"
	"sync"
)

var itemExists = true

type HashSet struct {
	items sync.Map
}

func NewString(s ...string) HashSet {
	r := HashSet{}
	r.AddString(s...)
	return r
}

func (this *HashSet) Add(items ...interface{}) {
	for _, item := range items {
		this.items.Store(item, itemExists)
	}
}

func (this *HashSet) AddString(items ...string) {
	for _, item := range items {
		this.items.Store(item, itemExists)
	}
}

func (this *HashSet) AddInt64(items ...int64) {
	for _, item := range items {
		this.items.Store(item, itemExists)
	}
}

func (this *HashSet) Remove(items ...interface{}) {
	for _, item := range items {
		this.items.Delete(item)
	}
}

func (this *HashSet) Contains(items ...interface{}) bool {
	for _, item := range items {
		if _, contains := this.items.Load(item); !contains {
			return false
		}
	}
	return true
}

func (this *HashSet) Range(f func(item interface{}) bool) {
	this.items.Range(func(key, value interface{}) bool {
		return f(key)
	})
}

func (this *HashSet) Values() []interface{} {
	values := make([]interface{}, 0)
	this.items.Range(func(key, value interface{}) bool {
		values = append(values, key)
		return true
	})
	return values
}

func (this *HashSet) ToStringArray() []string {
	values := this.Values()
	array := make([]string, len(values))
	for idx, value := range values {
		if str, ok := value.(string); ok {
			array[idx] = str
		} else {
			array[idx] = fmt.Sprintf("%v", value)
		}
	}
	return array
}

func (this *HashSet) ToInt64Array() []int64 {
	values := this.Values()
	array := make([]int64, len(values))

	checkNumber := func(number interface{}) (int64, bool) {
		switch reflect.TypeOf(number).Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return reflect.ValueOf(number).Int(), true
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return int64(reflect.ValueOf(number).Uint()), true
		}
		return 0, false
	}

	for idx, value := range values {
		if i, ok := value.(int64); ok {
			array[idx] = i
		} else if i, ok := checkNumber(value); ok {
			array[idx] = i
		} else {
			panic("item can`t convert to int64")
		}
	}
	return array
}

func (this *HashSet) Clear() {
	this.items = sync.Map{}
}

func (this *HashSet) Len() int {
	return len(this.Values())
}

func (this *HashSet) Len32() int32 {
	return int32(this.Len())
}

func (this *HashSet) Len64() int64 {
	return int64(this.Len())
}

func (this *HashSet) Equal(set *HashSet) bool {
	if this.Len() != set.Len() {
		return false
	}
	for _, value := range set.Values() {
		if !this.Contains(value) {
			return false
		}
	}
	return true
}

func (this *HashSet) Exclude(set *HashSet) *HashSet {
	newSet := &HashSet{}
	newSet.Add(this.Values()...)
	set.Range(func(item interface{}) bool {
		newSet.Remove(item)
		return true
	})
	return newSet
}
