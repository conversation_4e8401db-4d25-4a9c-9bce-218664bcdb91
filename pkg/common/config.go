/**
 * @note
 * common配置
 *
 * <AUTHOR>
 * @date 	2019-10-29
 */
package common

type Config struct {
	Environment string

	ServeAddr string
	PProfAddr string

	Debug            bool
	Domain           []string
	CsrfCookieDomain string
	StaffCacheTime   int
	AuthTokenTTL     int
}

var (
	config Config
)

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}

func GetEnv() string {
	return config.Environment
}

func IsDev() bool {
	return config.Environment == EnvDev
}

func IsTest() bool {
	return config.Environment == EnvTest
}

func IsProd() bool {
	return config.Environment == EnvProduct
}
