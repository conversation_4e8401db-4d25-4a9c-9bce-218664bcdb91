/**
 * @note
 * 加密解密工具函数
 *
 * <AUTHOR>
 * @date 	2019-10-30
 */
package common

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
)

var (
	ErrSigInvalid  = errors.New("signature was invalid")
	ErrSigExpired  = errors.New("signature has expired")
	ErrCsrfInvalid = errors.New("csrf token was invalid")
)

// Padding interface defines functions Pad and Unpad implemented for PKCS #5 and
// PKCS #7 types of padding.
type Padding interface {
	Pad(p []byte) []byte
	Unpad(p []byte) []byte
	PadSpace(p []byte) []byte
	UnpadSpace(p []byte) []byte
	PadZero(p []byte) []byte
	UnpadZero(p []byte) []byte
}

// Padder struct embeds attributes necessary for the padding calculation
// (e.g. block size). It implements the Padding interface.
type Padder struct {
	blockSize int
}

// NewPkcs5Padding returns a PKCS5 padding type structure. The blocksize
// defaults to 8 bytes (64-bit).
// See https://tools.ietf.org/html/rfc2898 PKCS #5: Password-Based Cryptography.
// Specification Version 2.0
func NewPkcs5Padding() Padding {
	return &Padder{blockSize: 8}
}

// NewPkcs7Padding returns a PKCS7 padding type structure. The blocksize is
// passed as a parameter.
// See https://tools.ietf.org/html/rfc2315 PKCS #7: Cryptographic Message
// Syntax Version 1.5.
// For example the block size for AES is 16 bytes (128 bits).
func NewPkcs7Padding(blockSize int) Padding {
	return &Padder{blockSize: blockSize}
}

// Pad returns the byte array passed as a parameter padded with bytes such that
// the new byte array will be an exact multiple of the expected block size.
// For example, if the expected block size is 8 bytes (e.g. PKCS #5) and that
// the initial byte array is:
//
//	[]byte{0x0A, 0x0B, 0x0C, 0x0D}
//
// the returned array will be:
//
//	[]byte{0x0A, 0x0B, 0x0C, 0x0D, 0x04, 0x04, 0x04, 0x04}
//
// The value of each octet of the padding is the size of the padding. If the
// array passed as a parameter is already an exact multiple of the block size,
// the original array will be padded with a full block.
func (p *Padder) Pad(buf []byte) []byte {
	bufLen := len(buf)
	padLen := p.blockSize - (bufLen % p.blockSize)
	padText := bytes.Repeat([]byte{byte(padLen)}, padLen)
	return append(buf, padText...)
}

// Unpad removes the padding of a given byte array, according to the same rules
// as described in the Pad function. For example if the byte array passed as a
// parameter is:
//
//	[]byte{0x0A, 0x0B, 0x0C, 0x0D, 0x04, 0x04, 0x04, 0x04}
//
// the returned array will be:
//
//	[]byte{0x0A, 0x0B, 0x0C, 0x0D}
func (p *Padder) Unpad(buf []byte) []byte {
	bufLen := len(buf)
	if bufLen == 0 {
		panic("invalid padding size")
	}

	pad := buf[bufLen-1]
	padLen := int(pad)
	if padLen > bufLen || padLen > p.blockSize {
		panic("invalid padding size")
	}

	for _, v := range buf[bufLen-padLen:] {
		if v != pad {
			panic("invalid padding size")
		}
	}

	return buf[:bufLen-padLen]
}

// Specific paded space, NOT recommend
func (p *Padder) PadSpace(buf []byte) []byte {
	bufLen := len(buf)
	padLen := p.blockSize - (bufLen % p.blockSize)
	padText := bytes.Repeat([]byte(" "), padLen)
	return append(buf, padText...)
}

func (p *Padder) UnpadSpace(buf []byte) []byte {
	bufLen := len(buf)
	if bufLen == 0 {
		panic("invalid padding size")
	}
	return bytes.TrimRight(buf, " ")
}

func (p *Padder) PadZero(buf []byte) []byte {
	bufLen := len(buf)
	padLen := p.blockSize - (bufLen % p.blockSize)

	padText := bytes.Repeat([]byte("\x00"), padLen)
	return append(buf, padText...)
}

func (p *Padder) UnpadZero(buf []byte) []byte {
	bufLen := len(buf)
	if bufLen == 0 {
		panic("invalid padding size")
	}
	return bytes.TrimRight(buf, "\x00")
}

func GCMEncrypt(key, nonce, plaintext []byte) []byte {
	// The key argument should be the AES key, either 16 or 32 bytes
	// to select AES-128 or AES-256.
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err.Error())
	}

	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		panic(err.Error())
	}

	ciphertext := aesgcm.Seal(nil, nonce, plaintext, nil)
	return ciphertext
}

func GCMDecrypt(key, nonce, ciphertext []byte) []byte {
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err.Error())
	}

	aesgcm, err := cipher.NewGCM(block)
	if err != nil {
		panic(err.Error())
	}

	plaintext, err := aesgcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		panic(err.Error())
	}
	return plaintext
}

const PAD_SPACE = "PAD_SPACE"
const PAD_ZERO = "PAD_ZERO"

func CBCEncrypt(key, iv, plaintext []byte, args ...string) []byte {
	padder := NewPkcs7Padding(aes.BlockSize)
	var padType string
	if len(args) > 0 {
		padType = args[0]
	}
	if padType == PAD_SPACE {
		plaintext = padder.PadSpace(plaintext)
	} else if padType == PAD_ZERO {
		plaintext = padder.PadZero(plaintext)
	} else {
		plaintext = padder.Pad(plaintext)
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	ciphertext := make([]byte, len(plaintext))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, plaintext)

	return ciphertext
}

func CBCDecrypt(key, iv, ciphertext []byte, args ...string) []byte {
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}

	// The IV needs to be unique, but not secure. Therefore it's common to
	// include it at the beginning of the ciphertext.
	if len(ciphertext) < aes.BlockSize {
		panic("ciphertext too short")
	}

	// CBC mode always works in whole blocks.
	if len(ciphertext)%aes.BlockSize != 0 {
		panic("ciphertext is not a multiple of the block size")
	}

	mode := cipher.NewCBCDecrypter(block, iv)

	// CryptBlocks can work in-place if the two arguments are the same.
	mode.CryptBlocks(ciphertext, ciphertext)

	var padType string
	if len(args) > 0 {
		padType = args[0]
	}
	padder := NewPkcs7Padding(aes.BlockSize)
	if padType == PAD_SPACE {
		ciphertext = padder.UnpadSpace(ciphertext)
	} else if padType == PAD_ZERO {
		ciphertext = padder.UnpadZero(ciphertext)
	} else {
		ciphertext = padder.Unpad(ciphertext)
	}
	return ciphertext
}

func GenerateKey(uid string) []byte {
	key := make([]byte, 0)
	key = append(key, AESKey[:8]...)
	key = append(key, uid[:8]...)
	return key
}

func EncryptByUid(uid, plaintext string) string {
	key := GenerateKey(uid)
	ciphertext := CBCEncrypt(key, key, []byte(plaintext))
	return hex.EncodeToString(ciphertext)
}

func DecryptByUid(uid, ciphertext string) string {
	ciphertextBytes, err := hex.DecodeString(ciphertext)
	if err != nil {
		panic(err.Error())
	}
	key := GenerateKey(uid)
	plaintext := CBCDecrypt(key, key, ciphertextBytes)
	return string(plaintext)
}

func EncryptoHmac256(message string, secret string) string {
	key := []byte(secret)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(message))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func createSignature(secret, message []byte) []byte {
	h := hmac.New(sha256.New, secret)
	h.Write(message)
	return h.Sum(nil)
}

func CreateSignedValue(secret, value string, clock time.Time) string {
	if clock.IsZero() {
		clock = time.Now()
	}
	value = base64.StdEncoding.EncodeToString([]byte(value))
	toSign := []byte(fmt.Sprintf("%d:%s", clock.Unix(), value))
	signature := createSignature([]byte(secret), toSign)
	return base64.StdEncoding.EncodeToString(append(toSign, signature...))
}

func DecodeSignedValue(secret, value string, maxAgeSeconds int64, clock time.Time) (string, error) {
	if clock.IsZero() {
		clock = time.Now()
	}

	var passedSig, signedValue []byte
	var timestampField, valueField string

	if ret, err := base64.StdEncoding.DecodeString(value); err != nil {
		return StringEmpty, ErrSigInvalid
	} else {
		if len(ret) < 32 {
			return StringEmpty, ErrSigInvalid
		}
		length := len(ret) - 32
		passedSig = ret[length:]
		signedValue = ret[:length]

		decodeFields := strings.Split(string(signedValue), ":")
		if len(decodeFields) < 2 {
			return StringEmpty, ErrSigInvalid
		}
		timestampField = decodeFields[0]
		valueField = decodeFields[1]
	}

	expectedSig := createSignature([]byte(secret), signedValue)
	if !hmac.Equal([]byte(passedSig), expectedSig) {
		return StringEmpty, ErrSigInvalid
	}
	timestamp, err := strconv.ParseInt(timestampField, 10, 64)
	if err != nil {
		return StringEmpty, ErrSigInvalid
	}
	if timestamp < clock.Unix()-maxAgeSeconds {
		// The signature has expired.
		return StringEmpty, ErrSigExpired
	}
	if ret, err := base64.StdEncoding.DecodeString(valueField); err != nil {
		return StringEmpty, err
	} else {
		return fmt.Sprintf("%s", ret), nil
	}
}
