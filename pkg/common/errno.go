/**
 * @note
 * 错误码定义
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package common

const (
	ERR_OK                = 0
	ERR_PARAM             = 1
	ERR_SYS               = 2
	ERR_UNKNOWN           = 3
	ERR_AUTHERR           = 4
	ERR_JSON              = 5
	ERR_NO_PERMISSION     = 6
	ERR_REDIRECT_TO_LOGIN = 7
	ERR_INTER_SIGNATURE   = 8
	ERR_INTER_NO_USER_ID  = 9
	ERR_INTER_ERR_USER_ID = 10
	ERR_PROXY             = 11
	ERR_CONTEXT_CANCELED  = 12
	ERR_GET_PERMISSION    = 13

	//20-29 定义db返回错误
	ERR_DB_NO_ROWS            = 20
	ERR_DB_CONNECTION_REFUSED = 21
	ERR_DB_BAD_CONNECTION     = 22
	ERR_DB_INCORRECT_TABLE    = 23
	ERR_DB_DUPLICATE_ENTRY    = 24
	ERR_DB_TABLE_NOT_EXIST    = 25

	ERR_S3_UPLOAD_FAILED                       = 100
	ERR_S3_GET_FILE_PRESIGN_URL                = 101
	ERR_S3_UPLOAD_TYPE_INVALID                 = 102
	ERR_S3_GET_IMAGE_STREAM_URL                = 103
	ERR_S3_UPLOAD_CONTENTTYPE_MATCH_FILESUFFIX = 104

	ERR_GENERATE_AUTH_TOKEN = 10000

	ERR_QUERY_USER_GROUP       = 100000
	ERR_USER_GROUP_NOT_EXIST   = 100001
	ERR_CREATE_USER_GROUP      = 100002
	ERR_DELETE_USER_GROUP      = 100003
	ERR_ADD_USER_TO_GROUP      = 100004
	ERR_DELETE_USER_FROM_GROUP = 100005
	ERR_UPDATE_USER_GROUP_INFO = 100006
	ERR_NOT_USER_GROUP_OWNER   = 100007

	ERR_ENABLE_WORKFLOW_TEMPLATE            = 200000
	ERR_DISABLE_WORKFLOW_TEMPLATE           = 200001
	ERR_EDIT_WORKFLOW_TEMPLATE              = 200002
	ERR_WORKFLOW_TEMPLATE_NOT_EXIST         = 200003
	ERR_QUERY_WORKFLOW_TEMPLATE             = 200004
	ERR_DELETE_WORKFLOW_TEMPLATE            = 200005
	ERR_CREATE_WORKFLOW_TEMPLATE            = 200006
	ERR_NOT_TEMPLATE_OWNER                  = 200007
	ERR_WORKFLOW_TEMPLATE_NOT_STAGING       = 200008
	ERR_WORKFLOW_TEMPLATE_STAFF_ERROR       = 200009
	ERR_WORKFLOW_TEMPLATE_NODE_DETAIL       = 200010
	ERR_WORKFLOW_TEMPLATE_GATEWAY_CONDITION = 200011
	ERR_WORKFLOW_TEMPLATE_EDIT_TAG          = 200012

	ERR_CREATE_WORKFLOW_TEMPLATE_GROUP              = 300000
	ERR_ENABLE_WORKFLOW_TEMPLATE_GROUP              = 300001
	ERR_DISABLE_WORKFLOW_TEMPLATE_GROUP             = 300002
	ERR_UPDATE_WORKFLOW_TEMPLATE_GROUP              = 300003
	ERR_QUERY_WORKFLOW_TEMPLATE_GROUP               = 300004
	ERR_DELETE_WORKFLOW_TEMPLATE_GROUP              = 300005
	ERR_NOT_TEMPLATE_GROUP_OWNER                    = 300006
	ERR_WORKFLOW_TEMPLATE_GROUP_NOT_EXIST           = 300007
	ERR_DELETE_WORKFLOW_TEMPLATE_GROUP_HAS_TEMPLATE = 300008

	ERR_QUERY_WORKFLOW                             = 400000
	ERR_WITHDRAW_WORKFLOW                          = 400001
	ERR_CREATE_WORKFLOW                            = 400002
	ERR_WITHDRAW_OTHERS_WORKFLOW                   = 400003
	ERR_WORKFLOW_STATE_NOT_CORRECT                 = 400004
	ERR_WORKFLOW_IMPOSSIBLE_AFTER_CUT              = 400005
	ERR_PREHANDLE_WORKFLOW                         = 400006
	ERR_URGE_WORKFLOW                              = 400007
	ERR_NO_AUTHORITY_URGE_WORKFLOW                 = 400008
	ERR_WORKFLOW_CANNOT_FIND_APPROVERS             = 400009
	ERR_RECREATE_WORKFLOW                          = 400010
	ERR_CANNOT_RECREATE_NON_RESET_WORKFLOW         = 400011
	ERR_RELATE_WORKFLOW_INPUT_ERROR                = 400012
	ERR_COMPLETE_WORKFLOW                          = 400013
	ERR_CANNOT_COMPLETE_NON_NEED_COMPLETE_WORKFLOW = 400014
	ERR_MISSING_SELF_SELECT_APPROVERS              = 400015

	ERR_QUERY_PROCESS                              = 500000
	ERR_UPDATE_PROCESS                             = 500001
	ERR_UPDATE_PROCESS_STATE_NOT_CORRECT           = 500002
	ERR_UPDATE_PROCESS_INVALID_NODE_TYPE           = 500003
	ERR_UPDATE_PROCESS_INVALID_NODE_STATE          = 500004
	ERR_PROCESS_NOT_EXIST                          = 500005
	ERR_UPDATE_PROCESS_NEED_COMMENT                = 500006
	ERR_UPDATE_PROCESS_NO_PERMISSION_OF_DATA_FIELD = 500007
	ERR_QUERY_TODO_PROCESS_COUNT                   = 500008
	ERR_ADD_PROCESS                                = 500009
	ERR_SEND_NOTIFY_AFTER_PROCESS_ADD              = 500010
	ERR_DELETE_PROCESS                             = 500011
	ERR_HANDOVER_PROCESS_TO_SELF                   = 500012
	ERR_COUNTERSIGN_PROCESS_TO_SELF                = 500013
	ERR_REMARK_PROCESS                             = 500014
	ERR_NO_PERMISSION_OF_PORTAL_PROCESS            = 500015
	ERR_NO_PERMISSION_OF_BPM_PROCESS               = 500016
	ERR_ROLLBACK_PROCESS                           = 500017
	ERR_WORKFLOW_OCCUPIED                          = 500018
	ERR_CANNOT_ROLLBACK_TO_SELF_NODE               = 500019
	ERR_CANT_DO_APPROVED_PROCESS                   = 500020
	ERR_MARK_ALL_NOTIFY_PROCESS                    = 500021
	ERR_PROCESS_MAYBE_ALREADY_DONE                 = 500022
	ERR_SAME_COUNTERSIGN_NOT_ALLOW                 = 500023
	ERR_NEED_COMPLETE_PROCESS                      = 500024

	ERR_QUERY_DEPARTMENT_TREE  = 600000
	ERR_QUERY_DEPARTMENT_INFO  = 600001
	ERR_QUERY_STAFF_USER_INFO  = 600002
	ERR_STAFF_USER_LEAVED      = 600003
	ERR_QUERY_DEPARTMENT_USERS = 600004
	ERR_STAFF_USER_NOT_EXIST   = 600005
	ERR_STAFF_USER_APPLIST     = 600006

	ERR_CREATE_APP_AUTH_KEY = 800000
	ERR_DELETE_APP_AUTH_KEY = 800001
	ERR_UPDATE_APP_AUTH_KEY = 800002
	ERR_QUERY_APP_AUTH_KEY  = 800003
	ERR_LIST_APP_AUTH_INFO  = 800004

	ERR_CREATE_WORKFLOW_TAG = 850000
	ERR_DELETE_WORKFLOW_TAG = 850001
	ERR_UPDATE_WORKFLOW_TAG = 850002
	ERR_QUERY_WORKFLOW_TAG  = 850003

	ERR_CREATE_APPMSG = 900000
	ERR_LIST_APPMSG   = 900001

	ERR_INTERACT_CALLBACK                 = 1000000
	ERR_INTERACT_PROCESS_NO_NEED_CALLBACK = 1000001

	//MSG-CENTER
	ERR_PUBLISH_MESSAGE            = 10000001
	ERR_DISABLE_MESSAGE            = 10000002
	ERR_DRAFT_MESSAGE              = 10000003
	ERR_ENABLE_MESSAGE             = 10000004
	ERR_DELETE_MESSAGE             = 10000005
	ERR_LIST_MESSAGE               = 10000006
	ERR_MESSAGE_DETAIL             = 10000007
	ERR_MESSAGE_READINFO           = 10000008
	ERR_LIST_NOTIFICATION          = 10000009
	ERR_NOTIFICATION_DETAIL        = 10000010
	ERR_LIST_ANNOUNCEMENT          = 10000011
	ERR_MSGTMPL_NOT_EXIST          = 10000012
	ERR_MSGTMPL_CANNOT_DISABLE     = 10000013
	ERR_STAFF_INFO                 = 10000014
	ERR_MSGTMPL_CANNOT_ENABLE      = 10000015
	ERR_GET_NOTIFICATION_UNREADCNT = 10000016
	ERR_GET_ALL_OFFICE_SITE        = 10000017
	ERR_MSGTMPL_ENABLE_FAILED      = 10000018
	ERR_NOTIFICATION_NOT_EXIST     = 10000020
)

var ErrnoDesc = map[int]string{
	ERR_OK:                "ok",
	ERR_PARAM:             "请求有点问题",
	ERR_SYS:               "服务开小差，请稍等",
	ERR_UNKNOWN:           "服务开小差，休息一会再试试",
	ERR_AUTHERR:           "授权失败，请反馈客服",
	ERR_JSON:              "格式解析错误",
	ERR_NO_PERMISSION:     "没有权限，请反馈客服",
	ERR_REDIRECT_TO_LOGIN: "重定向到登录页面",
	ERR_INTER_SIGNATURE:   "请求签名不正确",
	ERR_INTER_NO_USER_ID:  "请求未指定用户，请检查参数",
	ERR_INTER_ERR_USER_ID: "所指定用户有误，请检查参数",
	ERR_PROXY:             "代理请求出错",
	ERR_CONTEXT_CANCELED:  "请求被中断",
	ERR_GET_PERMISSION:    "获取IAM权限失败",

	//db相关
	ERR_DB_NO_ROWS:            "db没有记录",
	ERR_DB_CONNECTION_REFUSED: "拒绝连接",
	ERR_DB_BAD_CONNECTION:     "失效的连接",
	ERR_DB_INCORRECT_TABLE:    "db表名非法",
	ERR_DB_DUPLICATE_ENTRY:    "重复的key操作",
	ERR_DB_TABLE_NOT_EXIST:    "db表名不存在",

	ERR_S3_UPLOAD_FAILED:                       "S3上传失败",
	ERR_S3_GET_FILE_PRESIGN_URL:                "获取s3对象的preSignURL失败",
	ERR_S3_UPLOAD_TYPE_INVALID:                 "只允许上传pdf/xls/xlsx/doc/docx/zip/csv/jpg/png格式的文件",
	ERR_S3_GET_IMAGE_STREAM_URL:                "获取图片流失败",
	ERR_S3_UPLOAD_CONTENTTYPE_MATCH_FILESUFFIX: "上传的文件内容类型与文件后缀名不一致",

	ERR_QUERY_USER_GROUP:                    "查询用户组失败",
	ERR_USER_GROUP_NOT_EXIST:                "用户组不存在",
	ERR_CREATE_USER_GROUP:                   "创建用户组失败",
	ERR_DELETE_USER_GROUP:                   "删除用户组失败",
	ERR_ADD_USER_TO_GROUP:                   "添加用户到用户组失败",
	ERR_DELETE_USER_FROM_GROUP:              "从用户组删除用户失败",
	ERR_UPDATE_USER_GROUP_INFO:              "更新用户组失败",
	ERR_NOT_USER_GROUP_OWNER:                "非用户组owner,没有权限操作",
	ERR_ENABLE_WORKFLOW_TEMPLATE:            "上线工作流模板失败",
	ERR_DISABLE_WORKFLOW_TEMPLATE:           "下线工作流模板失败",
	ERR_CREATE_WORKFLOW_TEMPLATE:            "创建工作流模板失败",
	ERR_EDIT_WORKFLOW_TEMPLATE:              "编辑工作流模板失败",
	ERR_WORKFLOW_TEMPLATE_NOT_EXIST:         "查找的工作流模板不存在",
	ERR_QUERY_WORKFLOW_TEMPLATE:             "查询工作流模板失败",
	ERR_DELETE_WORKFLOW_TEMPLATE:            "删除工作流模板失败",
	ERR_NOT_TEMPLATE_OWNER:                  "无操作此模板权限",
	ERR_WORKFLOW_TEMPLATE_NOT_STAGING:       "模板状态非编辑中，无法修改",
	ERR_WORKFLOW_TEMPLATE_STAFF_ERROR:       "配置用户信息错误，请检查用户信息是否正确",
	ERR_WORKFLOW_TEMPLATE_NODE_DETAIL:       "不合法的节点配置，请修改后提交",
	ERR_WORKFLOW_TEMPLATE_GATEWAY_CONDITION: "表达式无效，请修改后提交",
	ERR_WORKFLOW_TEMPLATE_EDIT_TAG:          "TAG修改失败",

	ERR_CREATE_WORKFLOW_TEMPLATE_GROUP:              "创建模板组失败",
	ERR_ENABLE_WORKFLOW_TEMPLATE_GROUP:              "启动模板组失败",
	ERR_DISABLE_WORKFLOW_TEMPLATE_GROUP:             "禁用模板组失败",
	ERR_UPDATE_WORKFLOW_TEMPLATE_GROUP:              "更新模板组失败",
	ERR_QUERY_WORKFLOW_TEMPLATE_GROUP:               "查询模板组失败",
	ERR_DELETE_WORKFLOW_TEMPLATE_GROUP:              "删除模板组失败",
	ERR_NOT_TEMPLATE_GROUP_OWNER:                    "无操作此模板组权限",
	ERR_WORKFLOW_TEMPLATE_GROUP_NOT_EXIST:           "此模板组不存在",
	ERR_DELETE_WORKFLOW_TEMPLATE_GROUP_HAS_TEMPLATE: "无法删除仍然存在模板(或模板草稿)的模板组",

	ERR_QUERY_WORKFLOW:                             "查询工作流失败",
	ERR_WITHDRAW_WORKFLOW:                          "撤回工作流失败",
	ERR_CREATE_WORKFLOW:                            "创建工作流失败",
	ERR_WITHDRAW_OTHERS_WORKFLOW:                   "无法撤回其他人的工作流",
	ERR_WORKFLOW_STATE_NOT_CORRECT:                 "工作流状态错误，无法操作",
	ERR_WORKFLOW_IMPOSSIBLE_AFTER_CUT:              "经过分析，您的流程必然会导致失败，请更改表单重新尝试提交或联系管理员",
	ERR_PREHANDLE_WORKFLOW:                         "预处理工作流失败",
	ERR_URGE_WORKFLOW:                              "流程催办失败",
	ERR_NO_AUTHORITY_URGE_WORKFLOW:                 "无法催办别人创建的流程",
	ERR_WORKFLOW_CANNOT_FIND_APPROVERS:             "找不到审批人，请联系管理员",
	ERR_RECREATE_WORKFLOW:                          "重新提交流程失败",
	ERR_CANNOT_RECREATE_NON_RESET_WORKFLOW:         "无法重新提交未被驳回的流程",
	ERR_RELATE_WORKFLOW_INPUT_ERROR:                "关联审批单校验失败，请检查您的输入",
	ERR_CANNOT_COMPLETE_NON_NEED_COMPLETE_WORKFLOW: "无法提交未被审批人退回补充资料的流程",
	ERR_COMPLETE_WORKFLOW:                          "补充流程资料失败",
	ERR_MISSING_SELF_SELECT_APPROVERS:              "缺少自选审批人",

	ERR_QUERY_PROCESS:                              "查询任务失败",
	ERR_UPDATE_PROCESS:                             "处理任务失败",
	ERR_UPDATE_PROCESS_STATE_NOT_CORRECT:           "任务已被处理",
	ERR_UPDATE_PROCESS_INVALID_NODE_TYPE:           "不是一个需要处理的节点",
	ERR_UPDATE_PROCESS_INVALID_NODE_STATE:          "节点状态错误，可能已被其他人处理",
	ERR_PROCESS_NOT_EXIST:                          "任务不存在",
	ERR_UPDATE_PROCESS_NEED_COMMENT:                "请填写审批意见后提交",
	ERR_UPDATE_PROCESS_NO_PERMISSION_OF_DATA_FIELD: "无法修改您没有权限修改的数据",
	ERR_QUERY_TODO_PROCESS_COUNT:                   "查询代办任务数量失败",
	ERR_ADD_PROCESS:                                "创建流程无关的任务失败",
	ERR_SEND_NOTIFY_AFTER_PROCESS_ADD:              "发送通知失败",
	ERR_DELETE_PROCESS:                             "删除代办任务失败",
	ERR_HANDOVER_PROCESS_TO_SELF:                   "无法转交给自己",
	ERR_COUNTERSIGN_PROCESS_TO_SELF:                "无法加签给自己",
	ERR_REMARK_PROCESS:                             "评论任务失败",
	ERR_NO_PERMISSION_OF_PORTAL_PROCESS:            "无法操作Portal系统的任务",
	ERR_NO_PERMISSION_OF_BPM_PROCESS:               "无法操作Bpm系统的任务",
	ERR_ROLLBACK_PROCESS:                           "退回任务失败",
	ERR_WORKFLOW_OCCUPIED:                          "操作失败，可能流程正在运行，请稍后再试或联系管理员",
	ERR_CANNOT_ROLLBACK_TO_SELF_NODE:               "不能退回到当前节点",
	ERR_CANT_DO_APPROVED_PROCESS:                   "此任务您已经处理过或还未到您处理，无法操作",
	ERR_MARK_ALL_NOTIFY_PROCESS:                    "标记已读失败",
	ERR_PROCESS_MAYBE_ALREADY_DONE:                 "处理任务失败，可能已被他人处理",
	ERR_NEED_COMPLETE_PROCESS:                      "退回至发起人补充资料失败",
	ERR_SAME_COUNTERSIGN_NOT_ALLOW:                 "不允许重复地向前或向后加签",

	ERR_QUERY_DEPARTMENT_TREE:  "查询部门树失败",
	ERR_QUERY_DEPARTMENT_INFO:  "查询部门信息失败",
	ERR_QUERY_STAFF_USER_INFO:  "查询员工信息失败",
	ERR_QUERY_DEPARTMENT_USERS: "查询部门员工失败",
	ERR_STAFF_USER_LEAVED:      "员工已离职",
	ERR_STAFF_USER_NOT_EXIST:   "员工不存在",
	ERR_STAFF_USER_APPLIST:     "获取可用app失败",

	ERR_CREATE_APP_AUTH_KEY: "为应用创建appId和appKey失败",
	ERR_DELETE_APP_AUTH_KEY: "删除appId失败",
	ERR_UPDATE_APP_AUTH_KEY: "更新appauth失败",
	ERR_QUERY_APP_AUTH_KEY:  "查询appKey失败",
	ERR_LIST_APP_AUTH_INFO:  "展示app auth列表失败",

	ERR_CREATE_APPMSG: "添加appMsg失败",
	ERR_LIST_APPMSG:   "查询appMsg失败",

	ERR_CREATE_WORKFLOW_TAG: "创建Tag失败",
	ERR_DELETE_WORKFLOW_TAG: "删除Tag失败",
	ERR_UPDATE_WORKFLOW_TAG: "更新Tag失败",
	ERR_QUERY_WORKFLOW_TAG:  "查询Tag失败",

	ERR_INTERACT_CALLBACK:                 "处理第三方系统回调失败",
	ERR_INTERACT_PROCESS_NO_NEED_CALLBACK: "任务状态不需要回调，可能已经回调过",

	//MSG-CENTER
	ERR_PUBLISH_MESSAGE:            "发布消息失败",
	ERR_DISABLE_MESSAGE:            "下线消息失败",
	ERR_DRAFT_MESSAGE:              "保存草稿消息失败",
	ERR_ENABLE_MESSAGE:             "上线消息失败",
	ERR_DELETE_MESSAGE:             "删除消息失败",
	ERR_LIST_MESSAGE:               "查询消息失败",
	ERR_MESSAGE_DETAIL:             "查看消息模板详情失败",
	ERR_MESSAGE_READINFO:           "查看消息阅读人详情失败",
	ERR_LIST_NOTIFICATION:          "查看通知列表失败",
	ERR_NOTIFICATION_DETAIL:        "查看通知详情失败",
	ERR_LIST_ANNOUNCEMENT:          "获取公告列表失败",
	ERR_MSGTMPL_NOT_EXIST:          "消息模板不存在",
	ERR_MSGTMPL_CANNOT_DISABLE:     "消息模板不处于Enabled状态，不能Disable",
	ERR_STAFF_INFO:                 "传入的staff信息不存在",
	ERR_MSGTMPL_CANNOT_ENABLE:      "消息模板没有处于草稿状态，无法Enable",
	ERR_GET_NOTIFICATION_UNREADCNT: "获取通知未读数量失败",
	ERR_GET_ALL_OFFICE_SITE:        "查询所有的办公地点失败",
	ERR_MSGTMPL_ENABLE_FAILED:      "消息模板不符合要求，无法上线",
	ERR_NOTIFICATION_NOT_EXIST:     "通知不存在",
}
