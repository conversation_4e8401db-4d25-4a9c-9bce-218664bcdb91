/**
 * @note
 * 自定义的context相关，随逻辑透传
 *
 * <AUTHOR>
 * @date 	2019-10-29
 */
package common

import (
	"github.com/gin-gonic/gin"
	"github.com/jianfengye/collection"
	"github.com/tmsong/hlog"
	"gitlab.docsl.com/security/common"
)

type Operator interface {
	Ctx() HContextIface
}

type HContextIface interface {
	Log() *hlog.Logger
	WaitGroup() *WaitGroupWrapper
	GinCtx() *gin.Context
	User() *User
	AppFlag() AppFlag
	SetAppFlag(AppFlag)
}

type HContext struct {
	logger  *hlog.Logger
	wg      *WaitGroupWrapper
	gctx    *gin.Context
	user    *User
	appFlag AppFlag
}

func (hctx *HContext) Log() *hlog.Logger {
	return hctx.logger
}

func (hctx *HContext) WaitGroup() *WaitGroupWrapper {
	return hctx.wg
}

func (hctx *HContext) GinCtx() *gin.Context {
	return hctx.gctx
}

func (hctx *HContext) User() *User {
	return hctx.user
}

func (hctx *HContext) AppFlag() AppFlag {
	return hctx.appFlag
}

func (hctx *HContext) SetAppFlag(flag AppFlag) {
	hctx.appFlag = flag
}

func NewContext(l *hlog.Logger, gctx *gin.Context) *HContext {
	if gctx == nil {
		gctx = &gin.Context{}
	}
	gctx.Set(common.KeyLogger, l)
	ret := &HContext{
		logger: l,
		wg:     new(WaitGroupWrapper),
		gctx:   gctx,
		user:   &User{},
	}
	return ret
}

type UserPermissionRule struct {
	DenyAll   bool
	AllowAll  bool
	AllowItem collection.ICollection
	DenyItem  collection.ICollection
}

func (p *UserPermissionRule) HasPermission(v interface{}) bool {

	if p.DenyAll {
		return false
	}

	if p.DenyItem.Contains(v) {
		return false
	}

	if p.AllowAll {
		return true
	}

	if p.AllowItem.Contains(v) {
		return true
	}

	return false
}

type User struct {
	UserId              int64                      `json:"userId"`      //当前userId
	AccountName         string                     `json:"accountName"` //账户名（用户名）
	DisplayName         string                     `json:"userName"`    //用户展示名称（名字）
	Email               string                     `json:"email"`       //邮箱
	EmployeeId          string                     `json:"employeeId"`  //工号
	Origin              string                     `json:"origin"`      //调用接口的来源
	OriginAppId         int64                      `json:"originAppId"` //调用接口的三方appId
	DeptAndUserGroupIds *AllDeptIdsAndUserGroupIds `json:"-"`
}

type AllDeptIdsAndUserGroupIds struct {
	AllDeptIds      []int64  `json:"allDeptIds"`
	AllDeptNames    []string `json:"allDeptNames"`
	AllUserGroupIds []int64  `json:"allUserGroupIds"`
}

type AppFlag string

const (
	ToB    AppFlag = "ToB"
	ToC    AppFlag = "ToC"
	Cas    AppFlag = "CAS"
	Inter  AppFlag = "Inter"
	Portal AppFlag = "Portal"
)

func SetAppFlag(flag AppFlag) gin.HandlerFunc {
	return func(gctx *gin.Context) {
		ctx := GetHCtx(gctx)
		ctx.Log().Debugln("Set appFlag to", flag)
		ctx.SetAppFlag(flag)
		return
	}
}
