/**
 * @note
 * uuid生成相关定义
 *
 * <AUTHOR>
 * @date 	2019-11-21
 */
package common

import (
	"fmt"
	"github.com/google/uuid"
	"strings"
)

type ResourceType string

const (
	RESOURCE_TYPE_NODE              ResourceType = "Node"
	RESOURCE_TYPE_WORKFLOW          ResourceType = "Workflow"
	RESOURCE_TYPE_PROCESS           ResourceType = "Process"
	RESOURCE_TYPE_WORKFLOW_TEMPLATE ResourceType = "WorkflowTemplate"
	RESOURCE_TYPE_NODE_TEMPLATE     ResourceType = "NodeTemplate"
	RESOURCE_TYPE_S3_OBJECT         ResourceType = "S3Object"
	RESOURCE_TYPE_MESSAGE           ResourceType = "Message"
	RESOURCE_TYPE_NOTIFY            ResourceType = "Notify"
)

var ResourceTypeNamespaceMap = map[ResourceType]uuid.UUID{
	RESOURCE_TYPE_WORKFLOW:          uuid.Must(uuid.Parse("dcc3baa3-da92-4f5e-a6c1-e38f743fb656")),
	RESOURCE_TYPE_NODE:              uuid.Must(uuid.Parse("7f04d88e-1ce5-4465-af72-27d6c48d56a9")),
	RESOURCE_TYPE_PROCESS:           uuid.Must(uuid.Parse("5e876914-32fa-4040-87e3-3cfea6746024")),
	RESOURCE_TYPE_WORKFLOW_TEMPLATE: uuid.Must(uuid.Parse("7f0c99cb-63c9-4221-882e-6bc124eb1447")),
	RESOURCE_TYPE_NODE_TEMPLATE:     uuid.Must(uuid.Parse("28541122-1c26-4e03-ac6a-af79a084d507")),
	RESOURCE_TYPE_S3_OBJECT:         uuid.Must(uuid.Parse("7c0e0ac2-5d84-4529-8104-16be9168108a")),
	RESOURCE_TYPE_MESSAGE:           uuid.Must(uuid.Parse("ebc6ca62-a9ae-48ac-8475-f94cfd34f900")),
	RESOURCE_TYPE_NOTIFY:            uuid.Must(uuid.Parse("748bb4e3-39fc-45a2-be54-8029873a2045")),
}

func ResourceNamespace(resourceType ResourceType) (ns uuid.UUID, ok bool) {
	ns, ok = ResourceTypeNamespaceMap[resourceType]
	return
}

func NewUuid(typ ResourceType) string {
	if ns, ok := ResourceNamespace(typ); ok {
		data := []byte(fmt.Sprintf("%s_%s", GetEnv(), uuid.New().String()))
		return strings.Replace(uuid.NewSHA1(ns, data).String(), "-", StringEmpty, -1)
	} else {
		return strings.Replace(uuid.New().String(), "-", StringEmpty, -1)
	}
}
