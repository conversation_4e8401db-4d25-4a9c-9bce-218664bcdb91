/**
 * @note
 * 一些公用常量
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package common

import (
	"errors"
	"fmt"
	"gitlab.docsl.com/security/common"
	"mime"
	"time"

	"gorm.io/gorm"
)

func init() {
	mime.AddExtensionType(".pdf", "application/pdf")
	mime.AddExtensionType(".xls", "application/vnd.ms-excel")
	mime.AddExtensionType(".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	mime.AddExtensionType(".doc", "application/msword")
	mime.AddExtensionType(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
	mime.AddExtensionType(".zip", "application/zip")
	mime.AddExtensionType(".zip", "application/x-zip-compressed")
	mime.AddExtensionType(".csv", "text/csv; charset=utf-8")
	mime.AddExtensionType(".csv", "application/vnd.ms-excel")
	mime.AddExtensionType(".jpg", "image/jpeg")
	mime.AddExtensionType(".png", "image/png")
	mime.AddExtensionType(".jpeg", "image/jpeg")
	mime.AddExtensionType(".bmp", "image/bmp")
	mime.AddExtensionType(".bmp", "image/x-windows-bmp")
	mime.AddExtensionType(".txt", "text/plain")
	mime.AddExtensionType(".ppt", "application/vnd.ms-powerpoint")
	mime.AddExtensionType(".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation")
	mime.AddExtensionType(".rar", "application/x-rar-compressed")
	mime.AddExtensionType(".rar", "application/x-rar")
	mime.AddExtensionType(".rar", "application/rar")
	mime.AddExtensionType(".rar", "application/octet-stream")
	mime.AddExtensionType(".msg", "application/vnd.ms-outlook")
	mime.AddExtensionType(".msg", "application/octet-stream")
}

const (
	ModuleName       = "bpm"
	DBName           = "bpm"
	MaxMemory        = 2 << 32
	GracefulTimeout  = 10 * time.Second
	MaxNameLength    = 50
	MaxTagLength     = 10
	MaxDescLength    = 200
	MaxCommentLength = 1000
	CsrfCookieName   = "_c"
)

const (
	MODULE_NAME_MSGCENTER               = "msgcenter"
	MAX_TITLE_LENGTH                    = 80
	MAX_SENDERNAME_LENGTH               = 40
	MAX_CONTENT_LENGTH_FOR_ANNOUNCEMENT = 80
)

// 环境名定义
const (
	EnvDev     = "dev"
	EnvProduct = "prod"
	EnvTest    = "test"
)

// 秘钥定义
var (
	// The AES key either 16 or 32 bytes to select AES-128 or AES-256.
	AESKey           = []byte("9869c7503f92b79d1f448a34d300f198")
	LocalLocation, _ = time.LoadLocation("Asia/Shanghai")
)

// 公共错误定义
var (
	ErrRecordNotFound   = gorm.ErrRecordNotFound
	ErrConfig           = errors.New("get config err")
	ErrHttpRequestParam = errors.New("http request param err")
	ErrHttpResponse     = errors.New("get http response err")
	ErrBizCode          = errors.New("business code err ")
	ErrIndexOutOfRange  = errors.New("index out of range")
	ErrNoPermission     = errors.New("no permission")
	ErrNoInput          = errors.New("no input")
	ErrParamInput       = errors.New("the input param is not illegal")
	ErrUserFromProxy    = errors.New("proxy user info err")

	//func err
	ErrMissingAMandatoryParameterf = func(field string) error {
		return fmt.Errorf("missing a mandatory parameter [%s]", field)
	}
	ErrInvalidFieldf = func(field string) error {
		return fmt.Errorf("invalid %s", field)
	}
	ErrInvalidParameterOptionf = func(field string) error {
		return fmt.Errorf("invalid parameter[%s], the value is not in the valid options", field)
	}
)

const (
	StringEmpty      = ""
	StringJsonEmpty  = "{}"
	StringArrayEmpty = "[]"
	StringDelimiter  = "-"
)

const (
	LogOk  string = "ok"
	LogTag string = "tag"
)

const (
	LogtagRequestOk string = "_com_http_success"
	LogtagThriftOk  string = "_com_thrift_success"
	LogtagRedisOk   string = "_com_redis_success"
	LogtagAccessIn  string = "_com_request_in"
	LogtagAccessOut string = "_com_request_out"
	LogtagMysqlOk   string = "_com_mysql_success"
)

const (
	LogtagRequestErr string = "_com_http_failure"
	LogtagThriftErr  string = "_com_thrift_failure"
	LogtagRedisErr   string = "_com_redis_failure"
	LogtagMysqlErr   string = "_com_mysql_failure"
)

var TagDescSuccMapErr = map[string]string{
	LogtagRequestOk: LogtagRequestErr,
	LogtagThriftOk:  LogtagThriftErr,
	LogtagRedisOk:   LogtagRedisErr,
	LogtagMysqlOk:   LogtagMysqlErr,
}

const (
	LogcodeName string = "code"
)

/**
 * @note
 * 相关的Header常量
 */
const (
	HuobiHeaderRid       = "huobi-header-rid"
	HuobiHeaderCsrfToken = "huobi-header-csrf-token"
	HuobiSsoUserId       = "huobi-sso-userid"
	HuobiHeaderUserAgent = "huobi-header-user-agent"
	HuobiSsoUser         = "huobi-sso-user"
	HuobiHeaderAuthToken = "huobi-header-auth-token"
)

var AllowHttpMethods = []string{"PUT", "PATCH", "POST", "GET", "DELETE"}
var AllowHttpHeaders = []string{
	"Origin", "Accept", "Content-Type", "Authorization",
	common.XRequestIDHeaderKey,
	HuobiHeaderCsrfToken,
	HuobiHeaderUserAgent,
	HuobiHeaderAuthToken,
}

var ExposeHttpHeaders = []string{
	common.XRequestIDHeaderKey,
	HuobiHeaderUserAgent,
}

const (
	UrlLogout = "/cas/logout"
)

const (
	UserTableName                   = "bpm_user"
	DepartmentTableName             = "bpm_department"
	UserDepartmentRelationTableName = "bpm_user_department_relation"
)
