/**
 * @note
 * 通用的接口返回值定义
 *
 * <AUTHOR>
 * @date 	2019-10-24
 */
package common

import (
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
)

type Error struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Desc      interface{} `json:"data"`
	Stack     interface{} `json:"stack,omitempty"`
	RequestId string      `json:"requestId,omitempty"`
}

func NewError(code int, desc ...interface{}) *Error {
	message, ok := ErrnoDesc[code]
	if !ok {
		message = "Unknown Error"
	}

	d, s := parseDesc(desc...)
	return &Error{Code: code, Message: message, Desc: d, Stack: s}
}

func NewErrorWithMessage(code int, message string, desc ...interface{}) *Error {
	var ok bool
	if len(message) == 0 {
		if message, ok = ErrnoDesc[code]; !ok {
			message = "Unknown Error"
		}
	}
	d, s := parseDesc(desc...)
	return &Error{Code: code, Message: message, Desc: d, Stack: s}
}

func NewAccessControlError(code int, message string) *Error {
	return &Error{Code: code, Message: message, Desc: []interface{}{}}
}

func (e Error) Json() []byte {
	b, err := json.Marshal(e)
	if err != nil {
		if _, ok := err.(*json.UnsupportedTypeError); ok {
			return nil
		}
		if _, ok := err.(*json.UnsupportedValueError); ok {
			return nil
		}
		panic(err)
	}
	return b
}

func (e Error) JsonExcludeStack() []byte {
	e.Stack = nil
	return e.Json()
}

func (e Error) ExcludeStack() Error {
	e.Stack = nil
	return e
}

func (e Error) Error() string {
	var data, stack string
	switch desc := e.Desc.(type) {
	case []interface{}:
		for _, val := range desc {
			data += fmt.Sprintf("{%v}", val)
		}
	default:
		b, err := json.Marshal(desc)
		if err != nil {
			panic(err)
		}
		data = fmt.Sprintf("{%v}", string(b))
	}
	switch s := e.Stack.(type) {
	case []interface{}:
		for _, val := range s {
			stack += fmt.Sprintf("{%v}", val)
		}
	default:
		b, err := json.Marshal(s)
		if err != nil {
			panic(err)
		}
		stack = fmt.Sprintf("{%v}", string(b))
	}
	return fmt.Sprintf("[errno=%v][errmsg=%v][data=%v][stack=%v]", e.Code, e.Message, data, stack)
}

func (e *Error) AppendDesc(desc ...interface{}) *Error {
	d, s := parseDesc(desc...)
	if dSlice, ok := d.([]interface{}); ok {
		e.Desc = append(e.Desc.([]interface{}), dSlice...)
	} else {
		e.Desc = append(e.Desc.([]interface{}), d)
	}
	e.Stack = append(e.Stack.([]interface{}), s...)
	return e
}

func (e *Error) AppendStack(stack ...interface{}) *Error {
	e.Stack = append(e.Stack.([]interface{}), stack...)
	return e
}

func (e *Error) SetDesc(desc interface{}) *Error {
	if desc == nil {
		e.Desc = []interface{}{}
	} else {
		e.Desc = desc
	}
	return e
}

func (e *Error) SetErrorDetailToUser(detail interface{}) *Error {
	if err, ok := detail.(error); ok {
		detail = errors.Cause(err)
	}
	e.Message = fmt.Sprintf("%s: %v", e.Message, detail)
	return e
}

func parseDesc(desc ...interface{}) (data interface{}, stack []interface{}) {
	dataSlice := make([]interface{}, 0)
	stack = make([]interface{}, 0)
	for _, item := range desc {
		switch item.(type) {
		case error:
			stack = append(stack, item.(error).Error())
		default:
			dataSlice = append(dataSlice, item)
		}
	}
	//没有输出，data为null，1个输出，data为object，多个输出，data为array<object>
	if len(dataSlice) == 1 {
		data = dataSlice[0]
	} else if len(dataSlice) > 1 {
		data = dataSlice
	}
	return data, stack
}

func PanicErr(errno int) {
	panic(NewError(errno))
}

func PanicErrf(errno int, sfmt string, args ...interface{}) {
	panic(NewError(errno, fmt.Errorf(sfmt, args...)))
}

func CommonError(errno int, errmsg string, desc ...interface{}) *Error {
	return &Error{Code: errno, Message: errmsg, Desc: desc}

}
