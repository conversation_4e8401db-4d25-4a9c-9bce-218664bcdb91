/**
 * @note
 * gin的Context.Keys中存放的公用结构体
 *
 * <AUTHOR>
 * @date 	2019-10-25
 */
package common

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"net/url"
)

const (
	KEY_HCTX = "_ctx"
	KEY_RET  = "_ret"
)

/* @note
 * 从Keys里拿出自定义的Context，出错会panic
 */
func GetHCtx(gctx *gin.Context) HContextIface {
	if h, ok := gctx.Get(KEY_HCTX); !ok {
		panic("gctx no hctx")
	} else {
		return h.(HContextIface)
	}
}

/* @note
 * 将自定义的Context放入gin.Context中，出错会panic
 */
func SetHCtx(ctx *gin.Context, hctx HContextIface) {
	if hctx == nil {
		panic("nil hctx")
	}
	ctx.Set(KEY_HCTX, hctx)
}

/* @note
 * 从Keys里拿出返回值
 */
func GetRet(ctx *gin.Context) interface{} {
	ret, _ := ctx.Get(KEY_RET)
	return ret
}

/* @note
 * 将返回值放入Keys中
 */
func SetRet(ctx *gin.Context, ret interface{}) {
	ctx.Set(KEY_RET, ret)
}

/* @note
 * 直接从Keys里拿出Error类型，**此处假设使用者一定清楚返回值是Error类型的，否则会panic**
 */
func GetRetError(ctx *gin.Context) *Error {
	retErr, _ := ctx.Get(KEY_RET)
	if e, ok := retErr.(Error); ok {
		return &e
	}
	return retErr.(*Error)
}

/* @note
 * 处理callback接口
 */

func HandleJumpTo(validSites ...string) gin.HandlerFunc {
	validUrls := make([]*url.URL, len(validSites))
	for idx, site := range validSites {
		validUrls[idx], _ = url.Parse(site)
	}
	return func(gctx *gin.Context) {
		jumpToUrl := gctx.Query("jump_to")
		if len(jumpToUrl) == 0 {
			gctx.String(http.StatusOK, "ok")
			return
		}
		u, err := url.Parse(jumpToUrl)
		if err != nil {
			gctx.String(http.StatusOK, "ok")
			return
		}
		var isValid bool
		for _, validUrl := range validUrls {
			if u.Host == validUrl.Host {
				isValid = true
			}
		}
		if !isValid {
			if len(validUrls) > 0 { //如果不是合法的host，改写host
				u.Host = validUrls[0].Host
			} else {
				gctx.String(http.StatusOK, "ok") //否则直接return
				return
			}
		}
		gctx.Redirect(http.StatusFound, u.String())
		ctx := GetHCtx(gctx)
		ctx.Log().Debugln("call back, redirect to", u.String())
		gctx.Writer.WriteHeaderNow()
		return
	}
}

func AlwaysReturnOK(gctx *gin.Context) {
	SetRet(gctx, NewError(ERR_OK))
	return
}
