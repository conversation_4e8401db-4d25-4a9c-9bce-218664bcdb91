#!/bin/bash

# 镜像名称
IMAGE_NAME="bpm"

# 获取当前最新版本号
latest_version=$(docker images | grep $IMAGE_NAME | awk '{print $2}' | sort -nr | head -n 1)

# 检查是否获取到版本号
if [ -z "$latest_version" ]; then
    echo "No version found for image $IMAGE_NAME"
    latest_version=0
fi

# 增加版本号
new_version=$((latest_version + 1))
build_env="prod"

if [ ! -z $1 ]; then
  build_env=$1
fi

# 处理依赖
go mod vendor

# 构建新镜像
docker build --build-arg BUILD_ENV=$build_env --build-arg MODULE=$module -f Dockerfile -t $IMAGE_NAME:$new_version  .

# 输出新版本号
echo "New image version is $new_version"

# 停止旧版本的
docker ps |grep $IMAGE_NAME:$latest_version|awk '{print $1}' |xargs docker stop


if [ "$build_env" = "dev" ]; then
  docker run -d --name $IMAGE_NAME-$new_version -e BPM_CONFIG_CRYPT_KEY -p 20080:20080 -p 7761:7761 -p 9090:9090 -v /Users/<USER>/logs:/go/src/bpm/output/logs $IMAGE_NAME:$new_version
elif [ "$build_env" = "test" ]; then
  docker run -d --name $IMAGE_NAME-$new_version -e BPM_CONFIG_CRYPT_KEY -p 20080:20080 -p 7761:7761 -p 9090:9090 -v /root/data/logs/bpm:/go/src/bpm/output/logs $IMAGE_NAME:$new_version
else
  docker run -d --name $IMAGE_NAME-$new_version -e BPM_CONFIG_CRYPT_KEY -p 20080:20080 -p 7761:7761 -p 9090:9090 -v /root/data/logs/bpm:/go/src/bpm/output/logs $IMAGE_NAME:$new_version
fi
