FROM golang:alpine
LABEL maintainer="tim.song"

ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct

ENV PROJECT="bpm"

WORKDIR $GOPATH/src/$PROJECT
COPY . .

ARG BUILD_ENV="prod"

RUN go env -w GOINSECURE=gitlab.docsl.com
RUN go env -w GOPRIVATE=gitlab.docsl.com
RUN go install -v ./cmd && go build -v -o output/$PROJECT ./cmd
RUN mkdir -p output/logs
RUN cp -r conf/$BUILD_ENV.toml output/conf.toml

FROM golang:alpine
LABEL maintainer="tim.song"

ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct

ENV PROJECT="bpm"

WORKDIR $GOPATH/src/$PROJECT
COPY --from=0 $GOPATH/src/$PROJECT/output ./output

CMD ["sh","-c","./output/$PROJECT -config ./output/conf.toml -logfile ./output/logs/bpm.log"]
